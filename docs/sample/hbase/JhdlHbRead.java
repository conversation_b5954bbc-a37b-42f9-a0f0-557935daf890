package com.jhidc.cdc.sample.hbase;
/*
//----------------
样本例子代码：JhdlHbRead
查询和获取hbase的数据，等多种读、查操作
args[0] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[1] HBase的命名空间，如“jhdl”
args[2] HBase的表名称，如“test”
//----------------
//*/

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
javac -cp .:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* JhdlHbRead.java
java -cp .:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* JhdlHbRead jhmk01,jhmk02,jhmk03 ttns test
testns:test1
ttns:test
//*/


public class JhdlHbRead{
    public static void main(String[] args){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        //开始执行
        System.out.println("");
        System.out.println(df.format(new Date()) + " Start JhdlHbRead ...");        
        System.out.println("");
        //初始化debug信息
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start JhdlHbRead ...\r\n";

        /*
args[0] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[1] HBase的命名空间，如“jhdl”
args[2] HBase的表名称，如“test”
        //*/
        String strArgsHbaseAddr = "jhmk01,jhmk02,jhmk03";
        String strArgsHbaseNS = "jhdl";
        String strArgsHbaseTab = "testbig";
        if (args != null && args.length != 0) {
            strArgsHbaseAddr = args[0];
            strArgsHbaseNS = args[1];
            strArgsHbaseTab = args[2];
        }

        Configuration hbaseconfig = null;
        Connection hbaseconn = null;

        try{
            strSrcDebug += df.format(new Date()) + " Connect To HBase ...\r\n";

            hbaseconfig = HBaseConfiguration.create();
            hbaseconfig.set("hbase.zookeeper.property.clientPort","2181");
            hbaseconfig.set("hbase.zookeeper.quorum",strArgsHbaseAddr);
//            hbaseconfig.set("hbase.zookeeper.quorum","127.0.0.1:2181");
            hbaseconn = ConnectionFactory.createConnection(hbaseconfig);
            //开始各种读、查操作
            String strHbTableName = strArgsHbaseNS + ":" + strArgsHbaseTab;
            String strHbColFamily = "cf";
            System.out.println(" Start scan Table=" + strHbTableName);
            strSrcDebug += "Start scan Table=" + strHbTableName + " \r\n";
            Table hTable = hbaseconn.getTable(TableName.valueOf(strHbTableName));
            Scan hScan = new Scan();
            List<Get> hArrGet = new ArrayList<Get>();
            //scan正向两行
            System.out.println(" 正向 Scan 两行数据！");
            strSrcDebug += " 正向 Scan 两行数据！ \r\n";
            hScan.setReversed(false); //正向查询
            hScan.setLimit(2); //指定返回行数
            ResultScanner hResScan = hTable.getScanner(hScan);
            for(Result hRs : hResScan){
                byte[] bhRowKey = hRs.getRow();
                long lhRowKey = bRowKeyTolRowKey(bhRowKey);
                if(lhRowKey<0){
                    System.out.println("bRowKeyTolRowKey Error !");
                    strSrcDebug += "bRowKeyTolRowKey Error \r\n";
                }
                strSrcDebug += "rowkey=" + Long.toString(lhRowKey) + " \r\n";
                //显示cell内容
                for(Cell hcell : hRs.rawCells()){
                    String hTmpColFamily = new String(CellUtil.cloneFamily(hcell));
                    String hTmpColQualifier = new String(CellUtil.cloneQualifier(hcell));
                    String hTmpValue = new String(CellUtil.cloneValue(hcell));
                    strSrcDebug += "列 " + hTmpColFamily + ":" + hTmpColQualifier + "=" + hTmpValue + " \r\n";
                }
                //保存rowkey用于后面get
                Get hget = new Get(bhRowKey);
                hArrGet.add(hget);
            } //for(Result hRs : hResScan){
            //scan反向两行
            hResScan.close();
            System.out.println(" 反向 Scan 两行数据！");
            strSrcDebug += " 反向 Scan 两行数据！ \r\n";
            hScan.setReversed(true); //反向查询
            hScan.setLimit(2); //指定返回行数
            hResScan = hTable.getScanner(hScan);
            for(Result hRs : hResScan){
                byte[] bhRowKey = hRs.getRow();
                long lhRowKey = bRowKeyTolRowKey(bhRowKey);
                if(lhRowKey<0){
                    System.out.println("bRowKeyTolRowKey Error !");
                    strSrcDebug += "bRowKeyTolRowKey Error \r\n";
                }
                strSrcDebug += "rowkey=" + Long.toString(lhRowKey) + " \r\n";
                //显示cell内容
                for(Cell hcell : hRs.rawCells()){
                    String hTmpColFamily = new String(CellUtil.cloneFamily(hcell));
                    String hTmpColQualifier = new String(CellUtil.cloneQualifier(hcell));
                    String hTmpValue = new String(CellUtil.cloneValue(hcell));
                    strSrcDebug += "列 " + hTmpColFamily + ":" + hTmpColQualifier + "=" + hTmpValue + " \r\n";
                }
                //保存rowkey用于后面get
                Get hget = new Get(bhRowKey);
                hArrGet.add(hget);                
            } //for(Result hRs : hResScan){
            hResScan.close();
            //使用get
            System.out.println(" 使用 get 多行数据！");
            strSrcDebug += " 使用 get 多行数据！ \r\n";
            Result[] hArrRes = hTable.get(hArrGet);
            for(Result hRs : hArrRes){
                byte[] bhRowKey = hRs.getRow();
                long lhRowKey = bRowKeyTolRowKey(bhRowKey);
                if(lhRowKey<0){
                    System.out.println("bRowKeyTolRowKey Error !");
                    strSrcDebug += "bRowKeyTolRowKey Error \r\n";
                }
                strSrcDebug += "rowkey=" + Long.toString(lhRowKey) + " \r\n";
                //显示cell内容
                for(Cell hcell : hRs.rawCells()){
                    String hTmpColFamily = new String(CellUtil.cloneFamily(hcell));
                    String hTmpColQualifier = new String(CellUtil.cloneQualifier(hcell));
                    String hTmpValue = new String(CellUtil.cloneValue(hcell));
                    strSrcDebug += "列 " + hTmpColFamily + ":" + hTmpColQualifier + "=" + hTmpValue + " \r\n";
                }
            } //for(Result hRs : hArrRes){
            hArrGet.clear();

            strSrcDebug += "Close Table=" + strHbTableName + " \r\n";            
            hTable.close();
            //关闭连接
            if (hbaseconn!=null){
                hbaseconn.close();
            }
            strSrcDebug += df.format(new Date()) + " Close HBase !!\r\n";
        }catch (Exception ex){
            strSrcDebug += getErrorInfoFromException(ex);            
        }        

        //把debug信息写入到文件
        try{
            File file =new File("JhdlHbRead.log");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();
        }catch(IOException e){
            e.printStackTrace();
        }

        //总体结束
        System.out.println("");
        System.out.println(df.format(new Date()) + " End !!");
        System.out.println("");
    } //public static void main(String[] args){
    
    public static String getErrorInfoFromException(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString() + "\r\n";
        } catch (Exception e2) {
            return "getErrorInfoFromException Error!\r\n";
        }
    } //public static String getErrorInfoFromException(Exception e) {

    /*
    // 把Bytes类型的 HBase RowKey 转成 Long 类型的 RowKey
    //*/
    public static long bRowKeyTolRowKey(byte[] byteRowKey){
        long lRes = -1;
        if(byteRowKey.length != 8){
            return lRes;
        }
        long ltmp = 0;
        int jj = 56;
        lRes = 0;
        for(int ii=0;ii<8;ii++){
            ltmp = byteRowKey[ii];
            lRes += ltmp<<jj;
            jj -= 8;
        }
        return lRes;
    } //public static long bRowKeyTolRowKey(byte[] byteRowKey){

}
