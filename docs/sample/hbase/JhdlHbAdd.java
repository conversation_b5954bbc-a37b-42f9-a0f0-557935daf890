package com.jhidc.cdc.sample.hbase;
/*
//----------------
样本例子代码：JhdlHbAdd
往hbase数据库写入测试表与数据，包含列theid，thetype，thestr
args[0]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[1]	HBase的命名空间，如“jhdl”
args[2]	HBase的表名称，如“test”
args[3]	开始rowkey值（long型），如“100”就是从“101”开始
args[4]	写入的模拟数据行数，如“100”则随机产生100行数据
//----------------
测试情况：100w条数据，3个字段（theid long型,thetype 3~7字符串,thestr 10~1000字符串）
--环境1：（实际插入用时56秒）
jhmk01,jhmk02,jhmk03（分别是2 cpu ，4 GB Ram）
jhmk01 hmaster（一个副本）
jhmk02 hregion（实际全部数据保存到这里）
jhmk03 hregion
--环境2：（实际插入用时1分钟22秒）
jhmk01,jhmk02,jhmk03（分别是1 cpu ，1 GB Ram）
jhmk01 hmaster（一个副本）
jhmk02 hregion（实际全部数据保存到这里）
jhmk03 hregion
//*/

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.NamespaceNotFoundException;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/*
javac -cp .:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* JhdlHbAdd.java
java -cp .:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* JhdlHbAdd jhmk01,jhmk02,jhmk03 testns test1 0 10000
//*/


public class JhdlHbAdd{
    public static void main(String[] args){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        //开始执行
        System.out.println(" ");
        System.out.println(df.format(new Date()) + " Start JhdlHbAdd ...");        
        System.out.println(" ");
        //初始化debug信息，写入日志文件
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start JhdlHbAdd ...\r\n";

        /*
args[0]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[1]	HBase的命名空间，如“jhdl”
args[2]	HBase的表名称，如“test”
args[3]	开始rowkey值（long型），如“100”就是从“101”开始
args[4]	写入的模拟数据行数，如“100”则随机产生100行数据        
        //*/
        String strArgsHbaseAddr = "jhmk01,jhmk02,jhmk03";
        String strArgsHbaseNS = "jhdl";
        String strArgsHbaseTab = "testbig";
        long lHbRowId = 0; //开始的rowkey
        int iMaxCount = 10000; //最多生成多少条数据
        if (args != null && args.length != 0) {
        	strArgsHbaseAddr = args[0];
        	strArgsHbaseNS = args[1];
        	strArgsHbaseTab = args[2];
            lHbRowId = Long.parseLong(args[3]);
            iMaxCount = Integer.parseInt(args[4]);
        }

        Configuration hbaseconfig = null;
        Connection hbaseconn = null;
        Admin hbaseadmin = null;

        try{
            strSrcDebug += df.format(new Date()) + " Connect To HBase ...\r\n";
            hbaseconfig = HBaseConfiguration.create();
            hbaseconfig.set("hbase.zookeeper.property.clientPort","2181");
            hbaseconfig.set("hbase.zookeeper.quorum",strArgsHbaseAddr);
            hbaseconn = ConnectionFactory.createConnection(hbaseconfig);
            hbaseadmin = hbaseconn.getAdmin();
        
            //检查namespace是否存在
            boolean isCteateNS = false;
        	try{
            	NamespaceDescriptor hNamespaceDes = hbaseadmin.getNamespaceDescriptor(strArgsHbaseNS);
        	}catch (NamespaceNotFoundException exNotFound){
        		isCteateNS = true;
        	}
            if(isCteateNS){
		        System.out.println(" ");
            	System.out.println("Create Namespace " + strArgsHbaseNS);
            	strSrcDebug += "Create Namespace " + strArgsHbaseNS + " !\r\n";
            	hbaseadmin.createNamespace(NamespaceDescriptor.create(strArgsHbaseNS).build());
            }else{
		        System.out.println(" ");
            	System.out.println("Namespace " + strArgsHbaseNS + " is Exists!");
            	strSrcDebug += "Namespace " + strArgsHbaseNS + " is Exists !\r\n";
            }

            //需要预先建立命名空间jhdl（create_namespace 'jhdl'）
            //cf:sys_id,sys_code,sys_name,sys_content,sys_area
            String strHbTableName = strArgsHbaseNS + ":" + strArgsHbaseTab;
            String strHbColFamily = "cf";
            TableName hTableName = TableName.valueOf(strHbTableName);
            if(hbaseadmin.tableExists(hTableName)){
	            strSrcDebug += " the table " + strHbTableName + " exists !\r\n";
            }else{
	            TableDescriptorBuilder hTabDescBuild = TableDescriptorBuilder.newBuilder(TableName.valueOf(strHbTableName));
    	        ColumnFamilyDescriptor hColFamDesc = ColumnFamilyDescriptorBuilder.of(strHbColFamily);
        	    hTabDescBuild.setColumnFamily(hColFamDesc);
            	TableDescriptor hTabDesc = hTabDescBuild.build();
                hbaseadmin.createTable(hTabDesc);
	            strSrcDebug += " create the table " + strHbTableName + " !\r\n";
            }
            //真正插入数据
            Table hTable = hbaseconn.getTable(hTableName);
            int iCurIndex = 0; //当前插入条序号
            byte[] bhColFamilyName = Bytes.toBytes(strHbColFamily); //表族名
            byte[] bhColNameTheId = Bytes.toBytes("theid");
            byte[] bhColNameTheType = Bytes.toBytes("thetype");
            byte[] bhColNameTheStr = Bytes.toBytes("thestr");

            byte[][] bhDataTheType = new byte[100][];
            String strTmp = "";
            int ii = 0;
            int jj = 201;
            int iTypeIndex = 0;
            for(ii=0;ii<100;ii++){
                strTmp = Integer.toString(jj);
                bhDataTheType[ii] = Bytes.toBytes(strTmp);
                jj ++;
            }

            String strSrcData = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
            int iSrcDataLen = strSrcData.length();
            int kk,hh,ilenstr;
            //每1千条插入一次
            List<Put> hArrPut = new ArrayList<Put>();
            int iPutIndex = 0;
            int iPutMax = 1000;
            //开始生产数据
            for(iCurIndex=0;iCurIndex<iMaxCount;iCurIndex++){
                lHbRowId ++;
                Put hPut1 = new Put(Bytes.toBytes(lHbRowId)); //set rowkey
                //theid
                jj = iCurIndex + 1000;
                hPut1.addColumn(bhColFamilyName,bhColNameTheId,Bytes.toBytes(jj));
                //thetype
                jj = (int) (Math.random()*100);
                hPut1.addColumn(bhColFamilyName,bhColNameTheType,bhDataTheType[jj]);
                //thestr
                jj = (int) (Math.random()*100); //单数字符开始位置
                kk = jj % iSrcDataLen;
                if(kk>=iSrcDataLen) kk=iSrcDataLen-1;
                jj = (int) (Math.random()*100); //双数字符开始位置
                hh = jj % iSrcDataLen;
                if(hh>=iSrcDataLen) hh=iSrcDataLen-1;
                ilenstr = (int) (Math.random()*100); //字符总数量
                strTmp = "";
                for(ii=0;ii<ilenstr;ii++){
                    strTmp += strSrcData.substring(kk,kk+1);
                    kk++;
                    if(kk>=iSrcDataLen) kk=0;
                    strTmp += strSrcData.substring(hh,hh+1);
                    hh++;
                    if(hh>=iSrcDataLen) hh=0;
                }
                hPut1.addColumn(bhColFamilyName,bhColNameTheStr,Bytes.toBytes(strTmp));
                //先压入池内
                hArrPut.add(hPut1);
                iPutIndex ++;
                if(iPutIndex>=iPutMax){
                	//真正插入
                	hTable.put(hArrPut);
                	strSrcDebug += " put Data=" + Integer.toString(iPutIndex) + " index=" + Integer.toString(iCurIndex) + " !\r\n";
                	System.out.println(" put Data=" + Integer.toString(iPutIndex) + " index=" + Integer.toString(iCurIndex) + " !\r\n");
                	iPutIndex = 0;
                	hArrPut.clear();
                }
            } //for(iCurIndex=0;iCurIndex<iMaxCount;iCurIndex++){
            if(!hArrPut.isEmpty()){
                //真正插入
                hTable.put(hArrPut);
                strSrcDebug += " put Data=" + Integer.toString(iPutIndex) + " index=" + Integer.toString(iCurIndex) + " !\r\n";
                System.out.println(" put Data=" + Integer.toString(iPutIndex) + " index=" + Integer.toString(iCurIndex) + " !\r\n");
                iPutIndex = 0;
                hArrPut.clear();
            } //if(hArrPut.isEmpty()){

            hTable.close();

            bhDataTheType = null;
            bhColFamilyName = null;
            bhColNameTheId = null;
            bhColNameTheType = null;
            bhColNameTheStr = null;

            strSrcDebug += " Total put Data " + Integer.toString(iCurIndex) + " !\r\n";
            System.out.println("");
            System.out.println(" Total put Data " + Integer.toString(iCurIndex) + " !");
            System.out.println("");

            //关闭连接
            if (hbaseadmin!=null){
                hbaseadmin.close();
            }            
            if (hbaseconn!=null){
                hbaseconn.close();
            }
            strSrcDebug += df.format(new Date()) + " Close HBase !!\r\n";
        }catch (Exception ex){
            strSrcDebug += getErrorInfoFromException(ex);
        }        

        //把debug信息写入到文件
        try{
            File file =new File("JhdlHbAdd.log");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();
        }catch(IOException e){
            e.printStackTrace();
        }

        //总体结束
        System.out.println(" ");
        System.out.println(df.format(new Date()) + " End !!");
        System.out.println(" ");
    }
    
    public static String getErrorInfoFromException(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString() + "\r\n";
        } catch (Exception e2) {
            return "getErrorInfoFromException Error!\r\n";
        }
    }
}
