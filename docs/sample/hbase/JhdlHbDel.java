package com.jhidc.cdc.sample.hbase;
/*
//----------------
样本例子代码：JhdlHbDel
删除hbase的表
args[0] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[1] HBase的命名空间，如“jhdl”
args[2] HBase的表名称，如“test”
//----------------
//*/

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.NamespaceNotFoundException;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Admin;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/*
javac -cp .:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* JhdlHbDel.java
java -cp .:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* JhdlHbDel jhmk01,jhmk02,jhmk03 testns test1
testns:test1
//*/


public class JhdlHbDel{
    public static void main(String[] args){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        //开始执行
        System.out.println("");
        System.out.println(df.format(new Date()) + " Start JhdlHbDel ...");        
        System.out.println("");
        //初始化debug信息
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start JhdlHbDel ...\r\n";

        /*
args[0] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[1] HBase的命名空间，如“jhdl”
args[2] HBase的表名称，如“test”
        //*/
        String strArgsHbaseAddr = "jhmk01,jhmk02,jhmk03";
        String strArgsHbaseNS = "jhdl";
        String strArgsHbaseTab = "testbig";
        if (args != null && args.length != 0) {
            strArgsHbaseAddr = args[0];
            strArgsHbaseNS = args[1];
            strArgsHbaseTab = args[2];
        }

        Configuration hbaseconfig = null;
        Connection hbaseconn = null;
        Admin hbaseadmin = null;

        try{
            strSrcDebug += df.format(new Date()) + " Connect To HBase ...\r\n";

            hbaseconfig = HBaseConfiguration.create();
            hbaseconfig.set("hbase.zookeeper.property.clientPort","2181");
            hbaseconfig.set("hbase.zookeeper.quorum",strArgsHbaseAddr);
//            hbaseconfig.set("hbase.zookeeper.quorum","127.0.0.1:2181");
            hbaseconn = ConnectionFactory.createConnection(hbaseconfig);
            hbaseadmin = hbaseconn.getAdmin();

            System.out.println("");
            System.out.println(" Start Drop Table..");        
            System.out.println("");
            //检查namespace是否存在
            boolean isCteateNS = false;
            try{
                NamespaceDescriptor hNamespaceDes = hbaseadmin.getNamespaceDescriptor(strArgsHbaseNS);
            }catch (NamespaceNotFoundException exNotFound){
                isCteateNS = true;
            }
            if(isCteateNS){
                System.out.println("Namespace " + strArgsHbaseNS + " 不存在！");
                strSrcDebug += "Namespace " + strArgsHbaseNS + " 不存在!\r\n";
            }else{
                System.out.println("Namespace " + strArgsHbaseNS + " is Exists!");
                strSrcDebug += "Namespace " + strArgsHbaseNS + " is Exists !\r\n";
                //先检查表是否存在
                String strHbTableName = strArgsHbaseNS + ":" + strArgsHbaseTab;
                TableName hTableName = TableName.valueOf(strHbTableName);
                if(hbaseadmin.tableExists(hTableName)){
                    strSrcDebug += "the table " + strHbTableName + " exists !\r\n";
                    System.out.println("table " + strHbTableName + " exists ！");
                    //先disable
                    hbaseadmin.disableTable(hTableName);
                    strSrcDebug += "disableTable " + strHbTableName + " \r\n";
                    System.out.println("disableTable " + strHbTableName);
                    //再drop
                    hbaseadmin.deleteTable(hTableName);
                    strSrcDebug += "deleteTable " + strHbTableName + " \r\n";
                    System.out.println("deleteTable " + strHbTableName);
                }else{
                    strSrcDebug += "create the table " + strHbTableName + " !\r\n";
                    System.out.println("table " + strHbTableName + " 不存在 ！");
                } //if(hbaseadmin.tableExists(hTableName)){
            } //if(isCteateNS){

            //关闭连接
            if (hbaseadmin!=null){
                hbaseadmin.close();
            }            
            if (hbaseconn!=null){
                hbaseconn.close();
            }
            strSrcDebug += df.format(new Date()) + " Close HBase !!\r\n";
        }catch (Exception ex){
            strSrcDebug += getErrorInfoFromException(ex);            
        }        

        //把debug信息写入到文件
        try{
            File file =new File("JhdlHbDel.log");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();
        }catch(IOException e){
            e.printStackTrace();
        }

        //总体结束
        System.out.println("");
        System.out.println(df.format(new Date()) + " End !!");
        System.out.println("");
    } //public static void main(String[] args){
    
    public static String getErrorInfoFromException(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString() + "\r\n";
        } catch (Exception e2) {
            return "getErrorInfoFromException Error!\r\n";
        }
    } //public static String getErrorInfoFromException(Exception e) {

    /*
    // 把Bytes类型的 HBase RowKey 转成 Long 类型的 RowKey
    //*/
    public static long bRowKeyTolRowKey(byte[] byteRowKey){
        long lRes = -1;
        if(byteRowKey.length != 8){
            return lRes;
        }
        long ltmp = 0;
        int jj = 56;
        lRes = 0;
        for(int ii=0;ii<8;ii++){
            ltmp = byteRowKey[ii];
            lRes += ltmp<<jj;
            jj -= 8;
        }
        return lRes;
    } //public static long bRowKeyTolRowKey(byte[] byteRowKey){

}
