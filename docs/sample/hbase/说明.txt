说明
－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－
序号	类名称	输入参数	说明
	Etl2oStru		Oracle数据库的表结构存入到HBase里“jhdl:jh_tabinfo_01”
		args[0]	Oracle的地址、端口、库名，如“192.168.62.156:1521:TEST”
		args[1]	Oracle用户名，如“medrec”
		args[2]	Oracle密码，如“medrec”
		args[3]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[4]	HBase业务系统编码，如“01”
✔	Etl2oCont		Oracle数据库中单个表数据存入到HBase里“jhdl:01_xxxx”
		args[0]	Oracle的地址、端口、库名，如“192.168.62.156:1521:TEST”
		args[1]	Oracle用户名，如“medrec”
		args[2]	Oracle密码，如“medrec”
		args[3]	Oracle要抽取的数据表名称，如“pat_info”
		args[4]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[5]	HBase业务系统编码，如“01”
		args[6]	写入数据行总数，如“10000”，“0”表示全表写入
	Etl2sStru		SQL Server数据库的表结构存入到HBase里“jhdl:jh_tabinfo_01”
		args[0]	SQL Server的地址、端口、库名，如“192.168.62.156:1433;databaseName=jhemr;”
		args[1]	SQL Server用户名，如“medrec”
		args[2]	SQL Server密码，如“medrec”
		args[3]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[4]	HBase业务系统编码，如“01”
✔	Etl2sCont		SQL Server数据库中单个表数据存入到HBase里“jhdl:01_xxxx”
		args[0]	SQL Server的地址、端口、库名，如“192.168.62.156:1433;databaseName=jhemr;”
		args[1]	SQL Server用户名，如“medrec”
		args[2]	SQL Server密码，如“medrec”
		args[3]	SQL Server要抽取的数据表名称，如“pat_info”
		args[4]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[5]	HBase业务系统编码，如“01”
		args[6]	写入数据行总数，如“10000”，“0”表示全表写入
	Etl2mStru		MySql数据库的表结构存入到HBase里“jhdl:jh_tabinfo_01”
		args[0]	MySql的地址、端口、库名，如“localhost:3306/TEST”
		args[1]	MySql用户名，如“medrec”
		args[2]	MySql密码，如“medrec”
		args[3]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[4]	HBase业务系统编码，如“01”
✔	Etl2mCont		MySql数据库中单个表数据存入到HBase里“jhdl:01_xxxx”
		args[0]	MySql的地址、端口、库名，如“localhost:3306/TEST”
		args[1]	MySql用户名，如“medrec”
		args[2]	MySql密码，如“medrec”
		args[3]	MySql要抽取的数据表名称，如“pat_info”
		args[4]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[5]	HBase业务系统编码，如“01”
		args[6]	写入数据行总数，如“10000”，“0”表示全表写入
✔	Etl2cStru		Cache数据库的表结构存入到HBase里“jhdl:jh_tabinfo_01”
		args[0]	Cache的地址、端口、命名空间，如“**************:1972/SAMPLES”
		args[1]	Cache用户名，如“medrec”
		args[2]	Cache密码，如“medrec”
		args[3]	Cache的schmas，如“sample”
		args[4]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[5]	HBase业务系统编码，如“01”
✔	Etl2cCont		Cache数据库中单个表数据存入到HBase里“jhdl:01_xxxx”
		args[0]	Cache的地址、端口、命名空间，如“**************:1972/SAMPLES”
		args[1]	Cache用户名，如“medrec”
		args[2]	Cache密码，如“medrec”
		args[3]	Cache的schmas，如“sample”
		args[4]	Cache要抽取的数据表名称，如“pat_info”
		args[5]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[6]	HBase业务系统编码，如“01”
		args[7]	写入数据行总数，如“10000”，“0”表示全表写入
✔	JhdlHbAdd		往hbase数据库写入测试表与数据，包含列theid，thetype，thestr
		args[0]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[1]	HBase的命名空间，如“jhdl”
		args[2]	HBase的表名称，如“test”
		args[3]	开始rowkey值（long型），如“100”就是从“101”开始
		args[4]	写入的模拟数据行数，如“100”则随机产生100行数据
✔	JhdlHbRead		查询和获取hbase的数据，等多种读、查操作
		args[0]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[1]	HBase的命名空间，如“jhdl”
		args[2]	HBase的表名称，如“test”
✔	JhdlHbDel		删除hbase的表
		args[0]	HBase访问地址，如“jhmk01,jhmk02,jhmk03”
		args[1]	HBase的命名空间，如“jhdl”
		args[2]	HBase的表名称，如“test”
			
			

