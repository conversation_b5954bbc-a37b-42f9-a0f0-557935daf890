package com.jhidc.cdc.sample.hbase;
/*
//----------------
样本例子代码：Etl2mCont
MySQL数据库中单个表数据存入到HBase里“jhdl:01_xxxx”
args[0] MySQL的地址、端口、库名，如“localhost:3306/TEST”
args[1] MySQL用户名，如“medrec”
args[2] MySQL密码，如“medrec”
args[3] MySQL要抽取的数据表名称，如“pat_info”
args[4] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[5] HBase业务系统编码，如“01”
args[6] 写入数据行总数，如“10000”，“0”表示全表写入
//----------------
//*/

import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.NamespaceNotFoundException;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.*;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class Etl2mCont {

/* 
javac -cp .:mysql-connector-java-5.1.39-bin.jar:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* Etl2mCont.java
java -cp .:mysql-connector-java-5.1.39-bin.jar:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* Etl2mCont ***************:3306/test test test wj3_project jhmk01,jhmk02,jhmk03 03 10
//*/
 
    public static void main(String[] args) {
        //debug use
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start Etl2mCont (JHIDC MySql table content data to HBase) ......\r\n";
        System.out.println(" ");
        System.out.println(df.format(new Date()) + "Start Etl2mCont (JHIDC MySql table content data to HBase) .....");
        System.out.println(" ");

/*
//----------------
args[0] MySQL的地址、端口、库名，如“localhost:3306/TEST”
args[1] MySQL用户名，如“medrec”
args[2] MySQL密码，如“medrec”
args[3] MySQL要抽取的数据表名称，如“pat_info”
args[4] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[5] HBase业务系统编码，如“01”
args[6] 写入数据行总数，如“10000”，“0”表示全表写入
//----------------
//*/
        String strJdbcDriver = "com.mysql.jdbc.Driver";
        String strJdbcDbUrl = "********************************";
        String strJdbcUser = "jhemr";
        String strJdbcPass = "jhemr";
        String strMySqlTable = "Company";
        String strHbaseUrl = "jhmk01,jhmk02,jhmk03";
        String strHbaseSysKey = "01"; //业务系统编码
        String strHbaseNamespace = "jhdl";
        String strHbaseColFamily = "cf";
        long lMaxRowCount = 0;
        if (args != null && args.length != 0) {
            strJdbcDbUrl = "jdbc:mysql://" + args[0];
            strJdbcUser = args[1];
            strJdbcPass = args[2];
            strMySqlTable = args[3];
            strHbaseUrl = args[4];
            strHbaseSysKey = args[5];
            lMaxRowCount = Long.parseLong(args[6]);
        }
        String strHbaseTabName = strHbaseNamespace + ":" + strHbaseSysKey + "_" + strMySqlTable;

        //MySql jdbc 
        java.sql.Connection connJdbc = null;
        Statement stmtJdbc = null;
        ResultSet rsJdbc = null;
        ResultSetMetaData rsmdJdbc = null;
        //hbase
        org.apache.hadoop.conf.Configuration hbaseconfig = null;
        org.apache.hadoop.hbase.client.Connection hbaseconn = null;
        org.apache.hadoop.hbase.client.Admin hbaseadmin = null;
        //other
        String strSql = "";

        try{
            //connect MySql
            Class.forName(strJdbcDriver);        
            connJdbc = DriverManager.getConnection(strJdbcDbUrl,strJdbcUser,strJdbcPass);
            stmtJdbc = connJdbc.createStatement();
            System.out.println("");
            System.out.println("MySql JDBC Connected ");
            System.out.println("");
            //connect hbase
            hbaseconfig = HBaseConfiguration.create();
            hbaseconfig.set("hbase.zookeeper.property.clientPort","2181");
            hbaseconfig.set("hbase.zookeeper.quorum",strHbaseUrl);
            hbaseconn = ConnectionFactory.createConnection(hbaseconfig);
            hbaseadmin = hbaseconn.getAdmin();
            System.out.println("");
            System.out.println("HBase Connected ");
            System.out.println("");
            //检查namespace是否存在
            boolean isCteateNS = false;
            try{
                NamespaceDescriptor hNamespaceDes = hbaseadmin.getNamespaceDescriptor(strHbaseNamespace);
            }catch (NamespaceNotFoundException exNotFound){
                isCteateNS = true;
            }
            if(isCteateNS){
                System.out.println(" ");
                System.out.println("Create Namespace " + strHbaseNamespace);
                strSrcDebug += "Create Namespace " + strHbaseNamespace + " !\r\n";
                hbaseadmin.createNamespace(NamespaceDescriptor.create(strHbaseNamespace).build());
            }else{
                System.out.println(" ");
                System.out.println("Namespace " + strHbaseNamespace + " is Exists!");
                strSrcDebug += "Namespace " + strHbaseNamespace + " is Exists !\r\n";
            }
            //是否需要创建表jhdl:jh_tabinfo_01
            TableName hTableName = TableName.valueOf(strHbaseTabName);
            if(hbaseadmin.tableExists(hTableName)){
                strSrcDebug += " the table " + strHbaseTabName + " exists !\r\n";
                System.out.println(" the table " + strHbaseTabName + " exists !");
            }else{
                TableDescriptorBuilder hTabDescBuild = TableDescriptorBuilder.newBuilder(TableName.valueOf(strHbaseTabName));
                ColumnFamilyDescriptor hColFamDesc = ColumnFamilyDescriptorBuilder.of(strHbaseColFamily);
                hTabDescBuild.setColumnFamily(hColFamDesc);
                TableDescriptor hTabDesc = hTabDescBuild.build();
                hbaseadmin.createTable(hTabDesc);
                strSrcDebug += " create the table " + strHbaseTabName + " !\r\n";
                System.out.println(" create the table " + strHbaseTabName + " !");
            }
            //获取最新的rowkey，创建缓冲池
            Table hTable = hbaseconn.getTable(hTableName);
            //常规化01_xxxx的列族名
            byte[] bhColFamilyName = Bytes.toBytes(strHbaseColFamily); //表族名
            //获取最新的rowkey值
            long lHbRowId = 0;
            Scan hScan = new Scan();
            hScan.setReversed(true); //反向查询
            hScan.setOneRowLimit(); //指定返回1行
            ResultScanner hResScan = hTable.getScanner(hScan);
            for(Result hRs : hResScan){
                byte[] bhRowKey = hRs.getRow();
                lHbRowId = bRowKeyTolRowKey(bhRowKey);
                if(lHbRowId<0){
                    System.out.println("Get the last Rowkey Error !");
                    strSrcDebug += "Get the last Rowkey Error \r\n";
                }
                strSrcDebug += "last rowkey=" + Long.toString(lHbRowId) + " \r\n";
                break; //只取一行
            } //for(Result hRs : hResScan){
            hResScan.close();
            lHbRowId ++;
            //载入表数据xxxx
            List<Put> hArrPut = new ArrayList<Put>();
            int iPutIndex = 0;
            int iPutMax = 1000; //每1000行写入一次
            int iMySqlColCount = 0;
            strSql = "select * from " + strMySqlTable;
            rsJdbc = stmtJdbc.executeQuery(strSql);
            rsmdJdbc = rsJdbc.getMetaData();
            iMySqlColCount = rsmdJdbc.getColumnCount();
            //先分析表结构，（注意这里可能会与之前导入的表结构不一致）
            byte[][] bArrMySqlColName = new byte[iMySqlColCount][];
            int[] iArrMySqlColType = new int[iMySqlColCount];
            int[] iArrNullCount = new int[iMySqlColCount]; //记录每个字段有多少行是null（不写入到hbase）
            int ii;
            long lMySqlRowCount = 0;
            String strTmpStr = "";
            for(ii=0;ii<iMySqlColCount;ii++){
                strTmpStr = rsmdJdbc.getColumnName(ii+1);
                bArrMySqlColName[ii] = Bytes.toBytes(strTmpStr);
                strSrcDebug += " Column=" + Integer.toString(ii) + " Name=" + strTmpStr;
                iArrMySqlColType[ii] = rsmdJdbc.getColumnType(ii+1);
                iArrNullCount[ii] = 0;
                switch(iArrMySqlColType[ii]){
                    case Types.CHAR : 
                    case Types.VARCHAR : 
                        //getString
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getString \r\n";
                        break;
                    case Types.LONGVARCHAR : 
                        //getString ???
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " Ignore \r\n";
                        break;
                    case Types.DECIMAL : 
                    case Types.NUMERIC : 
                        //getBigDecimal
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getBigDecimal \r\n";
                        break;
                    case Types.BIT : 
                        //getBoolean
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getBoolean \r\n";
                        break;
                    case Types.TINYINT : 
                        //getByte
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getByte \r\n";
                        break;
                    case Types.SMALLINT : 
                        //getShort
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getShort \r\n";
                        break;
                    case Types.INTEGER : 
                        //getInt
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getInt \r\n";
                        break;
                    case Types.BIGINT : 
                        //getLong
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getLong \r\n";
                        break;
                    case Types.REAL : 
                        //getFloat
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getFloat \r\n";
                        break;
                    case Types.FLOAT : 
                    case Types.DOUBLE : 
                        //getDouble
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getDouble \r\n";
                        break;
                    case Types.BINARY : 
                    case Types.VARBINARY : 
                        //getBytes
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getBytes \r\n";
                        break;
                    case Types.LONGVARBINARY : 
                        //getBytes ????
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " Ignore \r\n";
                        break;
                    case Types.DATE : 
                        //getDate
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getDate \r\n";
                        break;
                    case Types.TIME : 
                        //getTime
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getTime \r\n";
                        break;
                    case Types.TIMESTAMP : 
                        //getTimestamp
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " GetBy=getTimestamp \r\n";
                        break;
                    case Types.ARRAY : 
                    case Types.BLOB : 
                    case Types.BOOLEAN : 
                    case Types.CLOB : 
                    case Types.DATALINK : 
                    case Types.DISTINCT : 
                    case Types.JAVA_OBJECT : 
                    case Types.LONGNVARCHAR : 
                    case Types.NCHAR : 
                    case Types.NCLOB : 
                    case Types.NULL : 
                    case Types.NVARCHAR : 
                    case Types.OTHER : 
                    case Types.REF : 
                    case Types.REF_CURSOR : 
                    case Types.ROWID : 
                    case Types.SQLXML : 
                    case Types.STRUCT : 
                    case Types.TIME_WITH_TIMEZONE : 
                    case Types.TIMESTAMP_WITH_TIMEZONE : 
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " Ignore \r\n";
                        break;
                    default:
                        strSrcDebug += " Typte=" + rsmdJdbc.getColumnTypeName(ii+1) + " Ignore \r\n";
                } //switch(){                
            } //for(ii=0;ii<iMySqlColCount;ii++){
            //压入缓冲池
            while(rsJdbc.next()){
                lMySqlRowCount ++;
                Put htmpPut = new Put(Bytes.toBytes(lHbRowId));
                for(ii=0;ii<iMySqlColCount;ii++){
                    byte[] bTmpValue = null;
                    switch(iArrMySqlColType[ii]){
                        case Types.CHAR : 
                        case Types.VARCHAR : 
                            String strTmpValue = rsJdbc.getString(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(strTmpValue);
                            }
                            break;
                        case Types.BIT : 
                            boolean boolTmpValue = rsJdbc.getBoolean(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(boolTmpValue);
                            }
                            break;
                        case Types.TINYINT : 
                            byte bbTmpValue = rsJdbc.getByte(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = new byte[1];
                                bTmpValue[0] = bbTmpValue;
                            }
                            break;
                        case Types.SMALLINT : 
                            short shTmpValue = rsJdbc.getShort(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(shTmpValue);
                            }
                            break;
                        case Types.INTEGER : 
                            int iiTmpValue = rsJdbc.getInt(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(iiTmpValue);
                            }
                            break;
                        case Types.BIGINT : 
                            long llTmpValue = rsJdbc.getLong(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(llTmpValue);
                            }
                            break;
                        case Types.REAL : 
                            float fTmpValue = rsJdbc.getFloat(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(fTmpValue);
                            }
                            break;
                        case Types.FLOAT : 
                        case Types.DOUBLE : 
                            double ddTmpValue = rsJdbc.getDouble(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(ddTmpValue);
                            }
                            break;
                        case Types.BINARY : 
                        case Types.VARBINARY : 
                            bTmpValue = rsJdbc.getBytes(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                                bTmpValue = null;
                            }
                            break;
                        case Types.DATE : 
                            //日期型先转成字符再转bytes
                            java.sql.Date dateTmpValue = rsJdbc.getDate(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                //java.sql.Date toString = yyyy-mm-dd
                                bTmpValue = Bytes.toBytes(dateTmpValue.toString());
                            }
                            break;
                        case Types.TIME : 
                            //日期型先转成字符再转bytes
                            java.sql.Time timeTmpValue = rsJdbc.getTime(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                //java.sql.Time toString = hh:mm:ss
                                bTmpValue = Bytes.toBytes(timeTmpValue.toString());
                            }
                            break;
                        case Types.TIMESTAMP : 
                            //日期型先转成字符再转bytes
                            java.sql.Timestamp tistTmpValue = rsJdbc.getTimestamp(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                //java.sql.Timestamp toString = yyyy-mm-dd hh:mm:ss.fffffffff
                                bTmpValue = Bytes.toBytes(tistTmpValue.toString());
                            }
                            break;
                        case Types.DECIMAL : 
                        case Types.NUMERIC : 
                            java.math.BigDecimal bdTmpValue = rsJdbc.getBigDecimal(ii+1);
                            if(rsJdbc.wasNull()){
                                iArrNullCount[ii] ++;
                            }else{
                                bTmpValue = Bytes.toBytes(bdTmpValue);
                            }                            
                            break;
                        case Types.ARRAY : 
                        case Types.BLOB : 
                        case Types.BOOLEAN : 
                        case Types.CLOB : 
                        case Types.DATALINK : 
                        case Types.DISTINCT : 
                        case Types.JAVA_OBJECT : 
                        case Types.LONGNVARCHAR : 
                        case Types.NCHAR : 
                        case Types.NCLOB : 
                        case Types.NULL : 
                        case Types.NVARCHAR : 
                        case Types.OTHER : 
                        case Types.REF : 
                        case Types.REF_CURSOR : 
                        case Types.ROWID : 
                        case Types.SQLXML : 
                        case Types.STRUCT : 
                        case Types.TIME_WITH_TIMEZONE : 
                        case Types.TIMESTAMP_WITH_TIMEZONE : 
                        case Types.LONGVARCHAR : 
                        case Types.LONGVARBINARY : 
                            //这里暂时不处理，也不记录为空
                            break;
                        default:
                            //这里暂时不处理，也不记录为空
                    } //switch(){
                    if(bTmpValue==null){
                        //空的不处理
                    }else{
                        if(bTmpValue.length>0){
                            htmpPut.addColumn(bhColFamilyName,bArrMySqlColName[ii],bTmpValue);
                        }else{
                            //空的不处理
                        }
                    }
                } //for(ii=1;ii<(iMySqlColCount+1);ii++){
                hArrPut.add(htmpPut);
                iPutIndex ++;
                //每1000行写入一次
                if(iPutIndex>=iPutMax){
                    //真正插入
                    hTable.put(hArrPut);
                    strSrcDebug += " put MySql Data Count=" + Integer.toString(iPutIndex) + " index=" + Long.toString(lMySqlRowCount) + " !\r\n";
                    System.out.println(" put MySql Data Count=" + Integer.toString(iPutIndex) + " index=" + Long.toString(lMySqlRowCount));
                    iPutIndex = 0;
                    hArrPut.clear();
                }
                lHbRowId ++;
                if(lMaxRowCount>0){
                    //需要限制写入多少行数据
                    if(lMySqlRowCount>=lMaxRowCount){
                        strSrcDebug += " MySql Data Rows Max=" + Long.toString(lMaxRowCount) + " !\r\n";
                        System.out.println(" MySql Data Rows Max=" + Long.toString(lMaxRowCount));
                        break;
                    }
                }
            } //while(rsJdbc.next()){            
            //把剩余的行写入
            if(!hArrPut.isEmpty()){
                //真正插入
                hTable.put(hArrPut);
                strSrcDebug += " put MySql Data Count=" + Integer.toString(iPutIndex) + " index=" + Long.toString(lMySqlRowCount) + " !\r\n";
                System.out.println(" put MySql Data Count=" + Integer.toString(iPutIndex) + " index=" + Long.toString(lMySqlRowCount));
                iPutIndex = 0;
                hArrPut.clear();
            } //if(hArrPut.isEmpty()){
            //free
            hTable.close();
            bArrMySqlColName = null;
            iArrMySqlColType = null;            
            
            System.out.println("DisConnected MySql and HBase ");
            //close hbase
            if(hbaseadmin != null){
                hbaseadmin.close();
            }
            if(hbaseconn != null){
                hbaseconn.close();
            }
            //close MySql jdbc
            if(rsJdbc != null){
                rsJdbc.close();
            }
            if(stmtJdbc != null){
                stmtJdbc.close();
            }
            if(connJdbc != null){
                connJdbc.close();
            }

            //输入各个字段的null总数，不写入到hbase
            strSrcDebug += "MySql Data Null Report (no write to HBase) \r\n";
            for(ii=0;ii<iMySqlColCount;ii++){
                strSrcDebug += "Col-" + Integer.toString(ii) + " NullRows=" + Integer.toString(iArrNullCount[ii]) + " !\r\n";
            }
            strSrcDebug += " - - - - - - - - - \r\n";
            System.out.println(" - - - - - - - - - ");
            strSrcDebug += "Report : Total Put Rows=" + Long.toString(lMySqlRowCount) + " !\r\n";
            System.out.println("Report : Total Put Rows=" + Long.toString(lMySqlRowCount));

        }catch(SQLException se){
            strSrcDebug += getErrorInfoFromException(se);
        }catch(Exception e){
            strSrcDebug += getErrorInfoFromException(e);
        }

        //write debug
        try{
            File file =new File("Etl2mCont.log");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();                        
        }catch(IOException e){
            e.printStackTrace();
        }

        System.out.println(" ");
        System.out.println("End !");
        System.out.println(" ");
    } //public static void main(String[] args) {

    public static String getErrorInfoFromException(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString() + "\r\n";
        } catch (Exception e2) {
            return "getErrorInfoFromException Error!\r\n";
        }
    } //public static String getErrorInfoFromException(Exception e) {

    /*
    // 把Bytes类型的 HBase RowKey 转成 Long 类型的 RowKey
    //*/
    public static long bRowKeyTolRowKey(byte[] byteRowKey){
        long lRes = -1;
        if(byteRowKey.length != 8){
            return lRes;
        }
        long ltmp = 0;
        int jj = 56;
        lRes = 0;
        for(int ii=0;ii<8;ii++){
            ltmp = byteRowKey[ii];
            lRes += ltmp<<jj;
            jj -= 8;
        }
        return lRes;
    } //public static long bRowKeyTolRowKey(byte[] byteRowKey){

}
