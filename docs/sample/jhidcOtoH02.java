//-----------------------------------------//
// JHMK Intelligent Data Collection System
// JHIDC Oracle to HBase 02
// Oracle tables name to HBase
// 从Oracle数据库中读出表名，写入到hbase数据库中
//-----------------------------------------//

import java.util.Date;
import java.text.SimpleDateFormat;
import java.io.File;
import java.io.FileWriter;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.Connection;
import java.sql.Statement;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;

 
public class jhidcOtoH02 {

/* 
javac -cp .:ojdbc8.jar jhidcOtoH02.java
java -cp .:ojdbc8.jar jhidcOtoH02
//*/

    static final String JDBC_DRIVER = "oracle.jdbc.OracleDriver";
    //set ip,database,user,password
    static final String DB_URL = "******************************************"; 
    static final String USER = "medrec";
    static final String PASS = "medrec";
 
    public static void main(String[] args) {
        Connection conn = null;
        Statement stmt = null;
        //set datatime format
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //init debug
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start JHIDC Oracle 02 ......\r\n";
        
        System.out.println("Start JHIDC Oracle 02 .....");
        int iCountTable=0;
        long lCountRows=0;
        String strTableNames="";
        
        try{
            Class.forName(JDBC_DRIVER);        
            conn = DriverManager.getConnection(DB_URL,USER,PASS);        
            stmt = conn.createStatement();
            String sql;
            int ii;
            //read all tables
            sql = "select TABLE_NAME from user_tables";
            ResultSet rs = stmt.executeQuery(sql);
            while(rs.next()){
                if(strTableNames.length()>0){
                    strTableNames += ";" + rs.getString(1);
                }else{
                    strTableNames = rs.getString(1);
                }
                iCountTable++;
            }
            rs.close();
            strSrcDebug += df.format(new Date()) + " Total Tables : " + Integer.toString(iCountTable) + "\r\n";
            //先处理一个数据库，不能与hbase同时连接            
            String[][] strArrTableColumns = null; //记录表结构            
            String[] strArrTableIndex = null; //记录主键
            long[] lArrTableRowsCount = null; //记录表拥有的行数量
            String[] strArrTable = null; //各个表名称
            //read table rows
            if(iCountTable>0){
                strArrTableColumns = new String[iCountTable][5];
                strArrTableIndex = new String[iCountTable];
                lArrTableRowsCount = new long[iCountTable];
                strArrTable = strTableNames.split(";");
                for(ii=0;ii<strArrTable.length;++ii){
                    strSrcDebug += "-------- " + Integer.toString(ii);
                    strSrcDebug += " Table : " + strArrTable[ii] + " ----------\r\n";
                    //read table columns
                    sql = "select COLUMN_ID,COLUMN_NAME,DATA_TYPE,DATA_LENGTH,NULLABLE ";
                    sql += " from user_tab_columns where Table_Name='" + strArrTable[ii] + "' order by COLUMN_ID";
                    rs = stmt.executeQuery(sql);
                    while(rs.next()){                        
                        strSrcDebug += " " + rs.getString(1) + " " + rs.getString(2) + " " + rs.getString(3);
                        strSrcDebug += " " + rs.getString(4) + " " + rs.getString(5) + " \r\n";
                        strArrTableColumns[ii][0] = rs.getString(1);
                        strArrTableColumns[ii][1] = rs.getString(2);
                        strArrTableColumns[ii][2] = rs.getString(3);
                        strArrTableColumns[ii][3] = rs.getString(4);
                        strArrTableColumns[ii][4] = rs.getString(5);
                    }
                    rs.close();
                    //read index
                    String strColName = "";
                    sql = "select INDEX_NAME,COLUMN_NAME from user_ind_columns where TABLE_NAME='" + strArrTable[ii];
                    sql += "' and INDEX_NAME=(select INDEX_NAME from user_indexes ";
                    sql += "where UNIQUENESS='UNIQUE' and INDEX_TYPE='NORMAL' and rownum<2 ";
                    sql += " and TABLE_NAME='" + strArrTable[ii] + "') ";
                    rs = stmt.executeQuery(sql);
                    while(rs.next()){
                        if(strColName.length()<1){
                            strColName = rs.getString(2);
                            strSrcDebug += " Table Index <" + rs.getString(1) + "> " + rs.getString(2);
                            strArrTableIndex[ii] = rs.getString(2);
                        }else{
                            strSrcDebug += ";" + rs.getString(2);
                            strArrTableIndex[ii] += ";" + rs.getString(2);
                        }                        
                    }
                    rs.close();
                    if(strColName.length()>0){
                        strSrcDebug += " \r\n";
                    }else{
                        //可能没有主键
                        strSrcDebug += " No Index \r\n";
                        strColName = "*";
                    }
                    //read rows
                    if(strColName.length()>0){
                        //read table rows
                        long lRows=0;
                        sql = "select count(" + strColName + ") from " + strArrTable[ii];
                        rs = stmt.executeQuery(sql);
                        if(rs.next()){
                            lRows = rs.getLong(1);
                        }
                        rs.close();
                        strSrcDebug += " Total Rows : " + Long.toString(lRows) + "\r\n";
                        lCountRows += lRows;
                        lArrTableRowsCount[ii] = lRows;
                    } //if(strTableNames.length()>0){

                    //end one table
                    strSrcDebug += "\r\n";
                } //for(ii=0;ii<strArrTable.length;++ii){
            } //if(iCountTable>0){            
            rs.close();
            stmt.close();
            conn.close();
            strSrcDebug += df.format(new Date()) + " Total Rows : " + Long.toString(lCountRows) + "\r\n";

            strSrcDebug += df.format(new Date()) + " Start Use Memory Data !\r\n";
            //额外处理数组数据
            if(iCountTable>0){
                for(ii=0;ii<strArrTable.length;++ii){
                    strSrcDebug += df.format(new Date()) + " Table=" + strArrTable[ii];
                    strSrcDebug += df.format(new Date()) + " COLUMN_ID=" + strArrTableColumns[ii][0];
                    strSrcDebug += df.format(new Date()) + " Index=" + strArrTableIndex[ii];
                    strSrcDebug += df.format(new Date()) + " RowCount=" + Long.toString(lArrTableRowsCount[ii]) + " \r\n";
                } //for(ii=0;ii<strArrTable.length;++ii){
            } //if(iCountTable>0){
            //释放数组资源
            strArrTableColumns = null;
            strArrTableIndex = null;
            lArrTableRowsCount = null;
            strArrTable = null;
            strSrcDebug += df.format(new Date()) + " End Memory Data !\r\n";

        }catch(SQLException se){
            se.printStackTrace();
        }catch(Exception e){
            e.printStackTrace();
        }
        
        //write debug
        try{
            File file =new File("OtoH-02.txt");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();                        
        }catch(IOException e){
            e.printStackTrace();
        }

        System.out.println("End !");
    }
}
