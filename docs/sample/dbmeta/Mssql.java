package com.jhidc.cdc.sample.dbmeta;//-----------------------------------------//
// JHMK Intelligent Data Collection System
// JHIDC SQL Server 01
// MS SQL Server table rows and columns
//-----------------------------------------//

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Mssql {
 
// cd C:\forwork\jhidc
// javac -cp .;sqljdbc42.jar jhidcMssql01.java
// java -cp .;sqljdbc42.jar jhidcMssql01
 
    static final String JDBC_DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";  
    //set ip,database,user,password
    static final String DB_URL = "jdbc:sqlserver://**************:1433;databaseName=jhemr;";
    static final String USER = "test";
    static final String PASS = "test";

    public static void main(String[] args) {
        Connection conn = null;
        Statement stmt = null;
        //set datatime format
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //init debug
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start JHIDC MS SQL Server 01 ......\r\n";
        
        System.out.println("Start JHIDC MS SQL Server 01 .....");
        int iCountTable=0;
        int iCountRows=0;
        String strTableNames="";
        String strTableId="";
        
        try{
            Class.forName(JDBC_DRIVER);        
            conn = DriverManager.getConnection(DB_URL,USER,PASS);        
            stmt = conn.createStatement();
            String sql;
            int ii;
            //read all tables
            sql = "select id,name from sysobjects where xtype='U' order by id";
            ResultSet rs = stmt.executeQuery(sql);
            while(rs.next()){
                if(strTableNames.length()>0){
                    strTableId += ";" + rs.getString(1);
                    strTableNames += ";" + rs.getString(2);
                }else{
                    strTableId = rs.getString(1);
                    strTableNames = rs.getString(2);
                }
                iCountTable++;
            }
            rs.close();
            strSrcDebug += df.format(new Date()) + " Total Tables : " + Integer.toString(iCountTable) + "\r\n";
            //read table rows
            if(iCountTable>0){
                String[] strArrTableId = strTableId.split(";");
                String[] strArrTable = strTableNames.split(";");
                for(ii=0;ii<strArrTable.length;++ii){
                    strSrcDebug += "-------- " + Integer.toString(ii);
                    strSrcDebug += " Table : " + strArrTable[ii] + " ----------\r\n";
                    //read table columns
                    //select a.colorder as colid,a.name as colname,b.name as coltype,a.length as collength,a.isnullable as colisnull from syscolumns a left join systypes b on a.xtype=b.xtype where a.id=1605580758 order by a.colorder
                    sql = "select a.colorder as colid,a.name as colname,b.name as coltype,";
                    sql += "a.length as collength,a.isnullable as colisnull from syscolumns a ";
                    sql += "left join systypes b on a.xtype=b.xtype where a.id=" + strArrTableId[ii];
                    sql += " order by a.colorder";                    
                    rs = stmt.executeQuery(sql);
                    while(rs.next()){
                        strSrcDebug += " " + rs.getString(1) + " " + rs.getString(2) + " " + rs.getString(3);
                        strSrcDebug += " " + rs.getString(4) + " " + rs.getString(5) + " \r\n";
                    }
                    rs.close();
                    //read index
                    String strColName = "";
                    sql = "sp_pkeys @table_name='" + strArrTable[ii] + "' ";
                    rs = stmt.executeQuery(sql);
                    while(rs.next()){
                        if(strColName.length()<1){
                            strColName = rs.getString(4);
                            strSrcDebug += " Table Index <" + rs.getString(6) + "> " + rs.getString(4);
                        }else{
                            strSrcDebug += ";" + rs.getString(4);
                        }
                    }
                    rs.close();
                    if(strColName.length()>0){
                        strSrcDebug += " \r\n";
                    }else{
                        //not index
                        strSrcDebug += " No Index \r\n";
                        strColName = "*";
                    }
                    //read rows
                    if(strColName.length()>0){
                        //read table rows
                        int iRows=0;
                        sql = "select count(" + strColName + ") from " + strArrTable[ii];
                        rs = stmt.executeQuery(sql);
                        if(rs.next()){
                            iRows = rs.getInt(1);
                        }
                        rs.close();
                        strSrcDebug += " Total Rows : " + Integer.toString(iRows) + "\r\n";
                        iCountRows += iRows;
                    } //if(strTableNames.length()>0){                        
                    //end one table
                    strSrcDebug += "\r\n";
                } //for(ii=0;ii<strArrTable.length;++ii){
            } //if(iCountTable>0){

            rs.close();
            stmt.close();
            conn.close();

            strSrcDebug += df.format(new Date()) + " Total Rows : " + Integer.toString(iCountRows) + "\r\n";
        }catch(SQLException se){
            se.printStackTrace();
        }catch(Exception e){
            e.printStackTrace();
        }
        
        //write debug
        try{
            File file =new File("MssqlDB-01.txt");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();                        
        }catch(IOException e){
            e.printStackTrace();
        }

        System.out.println("End !");
    }
}
