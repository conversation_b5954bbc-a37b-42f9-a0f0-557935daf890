package com.jhidc.cdc.sample.dbmeta;//-----------------------------------------//
// JHMK Intelligent Data Collection System
// JHIDC Oracle 02 
// Oracle table rows and columns
//-----------------------------------------//

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Oracle02 {
 
// cd C:\forwork\jhidc
// javac -cp .;ojdbc8.jar jhidcOracle02.java
// java -cp .;ojdbc8.jar jhidcOracle02
 
    static final String JDBC_DRIVER = "oracle.jdbc.OracleDriver";
    //set ip,database,user,password
    static final String DB_URL = "******************************************"; 
    static final String USER = "medrec";
    static final String PASS = "medrec";
 
    public static void main(String[] args) {
        Connection conn = null;
        Statement stmt = null;
        //set datatime format
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //init debug
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start JHIDC Oracle 02 ......\r\n";
        
        System.out.println("Start JHIDC Oracle 02 .....");
        int iCountTable=0;
        int iCountRows=0;
        String strTableNames="";
        
        try{
            Class.forName(JDBC_DRIVER);        
            conn = DriverManager.getConnection(DB_URL,USER,PASS);        
            stmt = conn.createStatement();
            String sql;
            int ii;
            //read all tables
            sql = "select TABLE_NAME from user_tables";
            ResultSet rs = stmt.executeQuery(sql);
            while(rs.next()){
                if(strTableNames.length()>0){
                    strTableNames += ";" + rs.getString(1);
                }else{
                    strTableNames = rs.getString(1);
                }
                iCountTable++;
            }
            rs.close();
            strSrcDebug += df.format(new Date()) + " Total Tables : " + Integer.toString(iCountTable) + "\r\n";
            //read table rows
            if(iCountTable>0){
                String[] strArrTable = strTableNames.split(";");
                for(ii=0;ii<strArrTable.length;++ii){
                    strSrcDebug += "-------- " + Integer.toString(ii);
                    strSrcDebug += " Table : " + strArrTable[ii] + " ----------\r\n";
                    //read table columns
                    sql = "select COLUMN_ID,COLUMN_NAME,DATA_TYPE,DATA_LENGTH,NULLABLE ";
                    sql += " from user_tab_columns where Table_Name='" + strArrTable[ii] + "' order by COLUMN_ID";
                    rs = stmt.executeQuery(sql);
                    while(rs.next()){
                        strSrcDebug += " " + rs.getString(1) + " " + rs.getString(2) + " " + rs.getString(3);
                        strSrcDebug += " " + rs.getString(4) + " " + rs.getString(5) + " \r\n";
                    }
                    rs.close();
                    //read index
                    // @@@@@@@@@@@ not index .....
                    String strColName = "";
                    sql = "select INDEX_NAME,COLUMN_NAME from user_ind_columns where TABLE_NAME='" + strArrTable[ii];
                    sql += "' and INDEX_NAME=(select INDEX_NAME from user_indexes ";
                    sql += "where UNIQUENESS='UNIQUE' and INDEX_TYPE='NORMAL' and rownum<2 ";
                    sql += " and TABLE_NAME='" + strArrTable[ii] + "') ";
                    rs = stmt.executeQuery(sql);
                    while(rs.next()){
                        if(strColName.length()<1){
                            strColName = rs.getString(2);
                            strSrcDebug += " Table Index <" + rs.getString(1) + "> " + rs.getString(2);
                        }else{
                            strSrcDebug += ";" + rs.getString(2);
                        }
                    }
                    rs.close();
                    if(strColName.length()>0){
                        strSrcDebug += " \r\n";
                    }else{
                        //not index
                        strSrcDebug += " No Index \r\n";
                        strColName = "*";
                    }
                    //read rows
                    if(strColName.length()>0){
                        //read table rows
                        int iRows=0;
                        sql = "select count(" + strColName + ") from " + strArrTable[ii];
                        rs = stmt.executeQuery(sql);
                        if(rs.next()){
                            iRows = rs.getInt(1);
                        }
                        rs.close();
                        strSrcDebug += " Total Rows : " + Integer.toString(iRows) + "\r\n";
                        iCountRows += iRows;
                    } //if(strTableNames.length()>0){                        
                    //end one table
                    strSrcDebug += "\r\n";
                } //for(ii=0;ii<strArrTable.length;++ii){
            } //if(iCountTable>0){

            rs.close();
            stmt.close();
            conn.close();

            strSrcDebug += df.format(new Date()) + " Total Rows : " + Integer.toString(iCountRows) + "\r\n";
        }catch(SQLException se){
            se.printStackTrace();
        }catch(Exception e){
            e.printStackTrace();
        }
        
        //write debug
        try{
            File file =new File("OracleDB-02.txt");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();                        
        }catch(IOException e){
            e.printStackTrace();
        }

        System.out.println("End !");
    }
}
