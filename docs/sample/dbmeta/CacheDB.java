package com.jhidc.cdc.sample.dbmeta;


import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSetMetaData;

public class CacheDB {

    public static void main(String[] args) {
        try {
            Class.forName("com.intersys.jdbc.CacheDriver").newInstance();
            String url = "*****************************************";
            String username = "_SYSTEM";
            String password = "SYS";
            Connection dbconnection = DriverManager.getConnection(url, username, password);

            String sql = "call %SQL_Manager.Schemas()";
            String sql1 = "CALL %SQL_Manager.TablesOnly('Sample') ";
            String sql2 = "CALL %SQL_Manager.Fields('Sample','company')  ";
            CallableStatement cs = dbconnection.prepareCall(sql2);
            java.sql.ResultSet rs = cs.executeQuery();

            ResultSetMetaData rsmd = rs.getMetaData();
            int colnum = rsmd.getColumnCount();
            while (rs.next()) {
                for (int i = 1; i <= colnum; i++)
                    System.out.println(rs.getString(i) + "  ");
            }
            System.out.println();
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
