//-----------------------------------------//
// JHMK Intelligent Data Collection System
// JHIDC Oracle to HBase 03
// Oracle tables name to HBase
// 从Oracle数据库中读出表名，写入到hbase数据库中
//-----------------------------------------//

import java.util.Date;
import java.text.SimpleDateFormat;
import java.io.File;
import java.io.FileWriter;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.Statement;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Admin;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.TableDescriptor;
import org.apache.hadoop.hbase.client.ColumnFamilyDescriptor;
import org.apache.hadoop.hbase.client.TableDescriptorBuilder;
import org.apache.hadoop.hbase.client.ColumnFamilyDescriptorBuilder;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.hbase.io.compress.Compression.Algorithm;


public class jhidcOtoH03 {

/* 
javac -cp .:ojdbc8.jar:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* jhidcOtoH03.java
java -cp .:ojdbc8.jar:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* jhidcOtoH03
//*/

    static final String JDBC_DRIVER = "oracle.jdbc.OracleDriver";
    //set ip,database,user,password
    static final String DB_URL = "jdbc:oracle:thin:@**************:1521:TEST"; 
    static final String USER = "medrec";
    static final String PASS = "medrec";
 
    public static void main(String[] args) {
        java.sql.Connection conn = null;
        java.sql.Statement stmt = null;
        //set datatime format
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //init debug
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start JHIDC Oracle 03 ......\r\n";
        
        System.out.println("Start JHIDC Oracle 03 .....");
        int iCountTable=0;
        long lCountRows=0;
        String strTableNames="";

        org.apache.hadoop.conf.Configuration hbaseconfig = null;
        org.apache.hadoop.hbase.client.Connection hbaseconn = null;
        org.apache.hadoop.hbase.client.Admin hbaseadmin = null;
        
        try{
            Class.forName(JDBC_DRIVER);        
            conn = DriverManager.getConnection(DB_URL,USER,PASS);        
            stmt = conn.createStatement();
            String sql;
            int ii;
            //read all tables
            sql = "select TABLE_NAME from user_tables";
            ResultSet rs = stmt.executeQuery(sql);
            while(rs.next()){
                if(strTableNames.length()>0){
                    strTableNames += ";" + rs.getString(1);
                }else{
                    strTableNames = rs.getString(1);
                }
                iCountTable++;
            }
            rs.close();
            strSrcDebug += df.format(new Date()) + " Total Tables : " + Integer.toString(iCountTable) + "\r\n";
            //先处理一个数据库，不能与hbase同时连接
            int[] iArrTableColCount = null; //记录每个表有多少字段
            String[][] strArrTableColumns = null; //记录每个表的结构
            int[] iArrTableIndCount = null; //记录每个表有多少个主键字段
            String[] strArrTableIndex = null; //记录主键
            long[] lArrTableRowsCount = null; //记录表拥有的行数量
            String[] strArrTable = null; //各个表名称
            //read table rows
            if(iCountTable>0){
                iArrTableColCount = new int[iCountTable];
                strArrTableColumns = new String[iCountTable][5];
                iArrTableIndCount = new int[iCountTable];
                strArrTableIndex = new String[iCountTable];
                lArrTableRowsCount = new long[iCountTable];
                strArrTable = strTableNames.split(";");
                for(ii=0;ii<strArrTable.length;++ii){
                    strSrcDebug += "-------- " + Integer.toString(ii);
                    strSrcDebug += " Table : " + strArrTable[ii] + " ----------\r\n";
                    //read table columns
                    sql = "select COLUMN_ID,COLUMN_NAME,DATA_TYPE,DATA_LENGTH,NULLABLE ";
                    sql += " from user_tab_columns where Table_Name='" + strArrTable[ii] + "' order by COLUMN_ID";
                    rs = stmt.executeQuery(sql);
                    iArrTableColCount[ii] = 0;
                    while(rs.next()){
                        if(iArrTableColCount[ii]>0){
                            strArrTableColumns[ii][0] += ";" + rs.getString(1);
                            strArrTableColumns[ii][1] += ";" + rs.getString(2);
                            strArrTableColumns[ii][2] += ";" + rs.getString(3);
                            strArrTableColumns[ii][3] += ";" + rs.getString(4);
                            strArrTableColumns[ii][4] += ";" + rs.getString(5);
                        }else{
                            strArrTableColumns[ii][0] = rs.getString(1);
                            strArrTableColumns[ii][1] = rs.getString(2);
                            strArrTableColumns[ii][2] = rs.getString(3);
                            strArrTableColumns[ii][3] = rs.getString(4);
                            strArrTableColumns[ii][4] = rs.getString(5);
                        }
                        iArrTableColCount[ii] ++;

                        strSrcDebug += " " + rs.getString(1) + " " + rs.getString(2) + " " + rs.getString(3);
                        strSrcDebug += " " + rs.getString(4) + " " + rs.getString(5) + " \r\n";
                    }
                    rs.close();
                    //read index
                    String strColName = "";
                    sql = "select INDEX_NAME,COLUMN_NAME from user_ind_columns where TABLE_NAME='" + strArrTable[ii];
                    sql += "' and INDEX_NAME=(select INDEX_NAME from user_indexes ";
                    sql += "where UNIQUENESS='UNIQUE' and INDEX_TYPE='NORMAL' and rownum<2 ";
                    sql += " and TABLE_NAME='" + strArrTable[ii] + "') ";
                    rs = stmt.executeQuery(sql);
                    iArrTableIndCount[ii] = 0;
                    while(rs.next()){
                        if(strColName.length()<1){
                            strColName = rs.getString(2);
                            strSrcDebug += " Table Index <" + rs.getString(1) + "> " + rs.getString(2);
                            strArrTableIndex[ii] = rs.getString(2);
                        }else{
                            strSrcDebug += ";" + rs.getString(2);
                            strArrTableIndex[ii] += ";" + rs.getString(2);
                        }
                        iArrTableIndCount[ii] ++;
                    }
                    rs.close();
                    if(strColName.length()>0){
                        strSrcDebug += " \r\n";
                    }else{
                        //可能没有主键
                        strSrcDebug += " No Index \r\n";
                        strColName = "*";
                    }
                    //read rows
                    if(strColName.length()>0){
                        //read table rows
                        long lRows=0;
                        sql = "select count(" + strColName + ") from " + strArrTable[ii];
                        rs = stmt.executeQuery(sql);
                        if(rs.next()){
                            lRows = rs.getLong(1);
                        }
                        rs.close();
                        strSrcDebug += " Total Rows : " + Long.toString(lRows) + "\r\n";
                        lCountRows += lRows;
                        lArrTableRowsCount[ii] = lRows;
                    } //if(strTableNames.length()>0){

                    //end one table
                    strSrcDebug += "\r\n";
                } //for(ii=0;ii<strArrTable.length;++ii){
            } //if(iCountTable>0){            
            rs.close();
            stmt.close();
            conn.close();
            strSrcDebug += df.format(new Date()) + " Total Rows : " + Long.toString(lCountRows) + "\r\n";

            strSrcDebug += df.format(new Date()) + " Start HBase .....\r\n";
            //额外处理数组数据
            if(iCountTable>0){
                //下面准备处理hbase部分
                hbaseconfig = HBaseConfiguration.create();
                hbaseconfig.set("hbase.zookeeper.quorum","127.0.0.1:2181");
                hbaseconn = ConnectionFactory.createConnection(hbaseconfig);
                hbaseadmin = hbaseconn.getAdmin();
                String strHbTableName = "jhdl:jh_otoh_03";
                String strHbColFamily = "cf";
                TableName hTableName = TableName.valueOf(strHbTableName);
                if(hbaseadmin.tableExists(hTableName)){
                    strSrcDebug += df.format(new Date()) + " the table " + strHbTableName + " exists !\r\n";
                }else{
                    TableDescriptorBuilder hTabDescBuild = TableDescriptorBuilder.newBuilder(TableName.valueOf(strHbTableName));
                    ColumnFamilyDescriptor hColFamDesc = ColumnFamilyDescriptorBuilder.of(strHbColFamily);
                    hTabDescBuild.setColumnFamily(hColFamDesc);
                    TableDescriptor hTabDesc = hTabDescBuild.build();
                    hbaseadmin.createTable(hTabDesc);
                    strSrcDebug += df.format(new Date()) + " create the table " + strHbTableName + " !\r\n";
                }
                Table hTable = hbaseconn.getTable(hTableName);
                long lHbRowId = 0;
                String strHbColTableName = "tab_n"; //以后保存到字典表中；表名称
                String strHbColColumnCount = "col_c"; //以后保存到字典表中；表结构中字段的数量
                String strHbColColumnId = "col_i_"; //以后保存到字典表中；表结构字段序号
                String strHbColColumnName = "col_n_"; //以后保存到字典表中；表结构字段名称
                String strHbColColumnType = "col_t_"; //以后保存到字典表中；表结构字段类型
                String strHbColColumnLen = "col_l_"; //以后保存到字典表中；表结构字段长度
                String strHbColColumnNull = "col_u_"; //以后保存到字典表中；表结构字段是否允许为空
                String strHbColIndexCount = "ind_c"; //以后保存到字典表中；表结构主键字段数量
                String strHbColIndexName = "ind_n_"; //以后保存到字典表中；表结构主键字段名称
                String strHbColRowsCount = "row_c"; //以后保存到字典表中；该表拥有数据行总数

                for(ii=0;ii<strArrTable.length;++ii){
                    lHbRowId ++;
                    Put htmpPut = new Put(Bytes.toBytes(lHbRowId)); //set rowkey
                    htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbColTableName),Bytes.toBytes(strArrTable[ii]));
                    htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbColColumnCount),Bytes.toBytes(iArrTableColCount[ii]));
                    htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbColIndexCount),Bytes.toBytes(iArrTableIndCount[ii]));
                    htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbColRowsCount),Bytes.toBytes(lArrTableRowsCount[ii]));

                    String strHbTmpColName = "";
                    String strHbTmpColValue = "";
                    String[] strArrTmpColId = strArrTableColumns[ii][0].split(";");
                    String[] strArrTmpColName = strArrTableColumns[ii][1].split(";");
                    String[] strArrTmpColType = strArrTableColumns[ii][2].split(";");
                    String[] strArrTmpColLen = strArrTableColumns[ii][3].split(";");
                    String[] strArrTmpColNull = strArrTableColumns[ii][4].split(";");                    
                    int jj=0;
                    for(jj=0;jj<strArrTmpColId.length;++jj){
                        strHbTmpColName = strHbColColumnId + Integer.toString(jj+1, 10);
                        strHbTmpColValue = strArrTmpColId[jj];
                        htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbTmpColName),Bytes.toBytes(strHbTmpColValue));
                        strHbTmpColName = strHbColColumnName + Integer.toString(jj+1, 10);
                        strHbTmpColValue = strArrTmpColName[jj];
                        htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbTmpColName),Bytes.toBytes(strHbTmpColValue));
                        strHbTmpColName = strHbColColumnType + Integer.toString(jj+1, 10);
                        strHbTmpColValue = strArrTmpColType[jj];
                        htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbTmpColName),Bytes.toBytes(strHbTmpColValue));
                        strHbTmpColName = strHbColColumnLen + Integer.toString(jj+1, 10);
                        strHbTmpColValue = strArrTmpColLen[jj];
                        htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbTmpColName),Bytes.toBytes(strHbTmpColValue));
                        strHbTmpColName = strHbColColumnNull + Integer.toString(jj+1, 10);
                        strHbTmpColValue = strArrTmpColNull[jj];
                        htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbTmpColName),Bytes.toBytes(strHbTmpColValue));
                    } //for(jj=0;jj<strArrTmpColId.length;++jj){
                    strArrTmpColName = strArrTableIndex[ii].split(";");
                    for(jj=0;jj<strArrTmpColName.length;++jj){
                        strHbTmpColName = strHbColIndexName + Integer.toString(jj+1, 10);
                        strHbTmpColValue = strArrTmpColName[jj];
                        htmpPut.addColumn(Bytes.toBytes(strHbColFamily),Bytes.toBytes(strHbTmpColName),Bytes.toBytes(strHbTmpColValue));
                    } //for(jj=0;jj<strArrTmpColName.length;++jj){

                    strSrcDebug += df.format(new Date()) + " insert Rowkey=" + Long.toString(lHbRowId, 10) + " OK^_^\r\n";
                    hTable.put(htmpPut);
                } //for(ii=0;ii<strArrTable.length;++ii){
                //关闭hbase连接
                hTable.close();
                if (hbaseadmin!=null){
                    hbaseadmin.close();
                }            
                if (hbaseconn!=null){
                    hbaseconn.close();
                }
            } //if(iCountTable>0){
            //释放数组资源
            iArrTableColCount = null;
            strArrTableColumns = null;
            iArrTableIndCount = null;
            strArrTableIndex = null;
            lArrTableRowsCount = null;
            strArrTable = null;
            strSrcDebug += df.format(new Date()) + " End HBase !\r\n";

        }catch(SQLException se){
            se.printStackTrace();
        }catch(Exception e){
            e.printStackTrace();
        }
        
        //write debug
        try{
            File file =new File("OtoH-03.txt");
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();                        
        }catch(IOException e){
            e.printStackTrace();
        }

        System.out.println("End !");
    }
}
