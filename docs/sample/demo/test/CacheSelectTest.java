package com.jhidc.cdc.demo.test;

import java.sql.*;

public class CacheSelectTest {

    public static void main(String[] args) {
        String className=null;
        String url=null;
        String user=null;
        String password=null;

        className="com.intersys.jdbc.CacheDriver";
        url="*****************************************";
        user="_SYSTEM";
        password="SYS";

        try {
            Class.forName(className);
        } catch (Exception e) {
            System.out.println(className+"driver failed to load.");
        }

        try {
            Connection con=
                    DriverManager.getConnection(url,user,password);

            Statement stmt=con.createStatement();
            ResultSet rs =stmt.executeQuery("SELECT * FROM sample.vendor t WHERE t.VENDOR = 1");
            ResultSetMetaData rsmd =rs.getMetaData();

            int numberOfColumns=rsmd.getColumnCount();

            if (numberOfColumns <= 0) {
                System.out.println("无记录");
            }

            int rowCount=1;

            while (rs.next()) {
                for(int i=1;i<=numberOfColumns;i++){
                    System.out.print(rs.getMetaData().getColumnName(i) + ":" +rs.getString(i)+"\t");
                }
                System.out.println("");
                rowCount++;
            }

            stmt.close();
            con.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
