package com.jhidc.cdc.demo.etl;
/*
//----------------
样本例子代码：Etl2cStru
Cache数据库的表结构存入到HBase里“jhdl:jh_tabinfo_01”
args[0] Cache的地址、端口、命名空间，如“**************:1972/SAMPLES”
args[1] Cache用户名，如“medrec”
args[2] Cache密码，如“medrec”
args[3] Cache的schmas，如“sample”
args[4] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[5] HBase业务系统编码，如“01”
//----------------
东华系统
SQLUser.OE_Order（医嘱主表）
SQLUser.OperationMedicalPrice_Contrast（？）
SQLUser.PA_Person
SQLUser.PA_PatMas
//*/

import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.NamespaceNotFoundException;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.*;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Date;


public class JhmkEtl2cStru {

/*
javac -cp .:cachedb.jar:cachejdbc.jar:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* Etl2cStru.java
java -cp .:cachedb.jar:cachejdbc.jar:/jhmk/hbase-2.2.2/lib/*:/jhmk/hbase-2.2.2/lib/client-facing-thirdparty/* Etl2cStru ***************:1972/SAMPLES _SYSTEM SYS Sample jhmk01,jhmk02,jhmk03 01
//*/

    public static void main(String[] args) {
        //debug use
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String strSrcDebug = df.format(new Date());
        strSrcDebug += " Start Etl2cStru (JHIDC Cache table structure to HBase) ......\r\n";
        System.out.println(" ");
        System.out.println(df.format(new Date()) + "Start Etl2cStru (JHIDC Cache table structure to HBase) .....");
        System.out.println(" ");

/*
//----------------
args[0] Cache的地址、端口、命名空间，如“**************:1972/SAMPLES”
args[1] Cache用户名，如“medrec”
args[2] Cache密码，如“medrec”
args[3] Cache的schmas，如“sample”
args[4] HBase访问地址，如“jhmk01,jhmk02,jhmk03”
args[5] HBase业务系统编码，如“01”
args[6] 是否需要初始化Hbase的表，如“0”
//----------------
//*/
        String strJdbcDriver = "com.intersys.jdbc.CacheDriver";
        String strJdbcDbUrl = "*****************************************";
        String strJdbcUser = "_SYSTEM";
        String strJdbcPass = "SYS";
        String strCacheSchema = "Sample";
        String strHbaseUrl = "jhmk01,jhmk02,jhmk03";
        String strHbaseSysKey = "02"; //业务系统编码
        String strHbaseNamespace = "JHDL";
        String strHbaseColFamily = "CF";
        String isNeedInitHbaseTable = "1";
        if (args != null && args.length != 0) {
            strJdbcDbUrl = "jdbc:Cache://" + args[0];
            strJdbcUser = args[1];
            strJdbcPass = args[2];
            strCacheSchema = args[3];
            strHbaseUrl = args[4];
            strHbaseSysKey = args[5];
            //新增参数
            isNeedInitHbaseTable = args[6];
        }
        String strHbaseTabName = strHbaseNamespace + ":JH_TABINFO_" + strHbaseSysKey;

        //cache jdbc
        java.sql.Connection connJdbc = null;
        CallableStatement cstJdbc = null;
        ResultSet rsJdbc = null;
        //hbase
        org.apache.hadoop.conf.Configuration hbaseconfig = null;
        Connection hbaseconn = null;
        Admin hbaseadmin = null;
        //other
        String strSql = "";

        try{
            //connect cache
            Class.forName(strJdbcDriver);
            connJdbc = DriverManager.getConnection(strJdbcDbUrl,strJdbcUser,strJdbcPass);
            System.out.println("");
            System.out.println("Cache JDBC Connected ");
            System.out.println("");
            //connect hbase
            hbaseconfig = HBaseConfiguration.create();
            hbaseconfig.set("hbase.zookeeper.property.clientPort","2181");
            hbaseconfig.set("hbase.zookeeper.quorum",strHbaseUrl);
            hbaseconn = ConnectionFactory.createConnection(hbaseconfig);
            hbaseadmin = hbaseconn.getAdmin();
            System.out.println("");
            System.out.println("HBase Connected ");
            System.out.println("");
            //检查namespace是否存在
            boolean isCteateNS = false;
            try{
                NamespaceDescriptor hNamespaceDes = hbaseadmin.getNamespaceDescriptor(strHbaseNamespace);
            }catch (NamespaceNotFoundException exNotFound){
                isCteateNS = true;
            }
            if(isCteateNS){
                System.out.println(" ");
                System.out.println("Create Namespace " + strHbaseNamespace);
                strSrcDebug += "Create Namespace " + strHbaseNamespace + " !\r\n";
                hbaseadmin.createNamespace(NamespaceDescriptor.create(strHbaseNamespace).build());
            }else{
                System.out.println(" ");
                System.out.println("Namespace " + strHbaseNamespace + " is Exists!");
                strSrcDebug += "Namespace " + strHbaseNamespace + " is Exists !\r\n";
            }
            //是否需要创建表JHDL:JH_TABINFO_01
            TableName hTableName = TableName.valueOf(strHbaseTabName);
            if(hbaseadmin.tableExists(hTableName)){
                strSrcDebug += " the table " + strHbaseTabName + " exists !\r\n";
                System.out.println(" the table " + strHbaseTabName + " exists !");

                //如果表存在，删除表
                strSrcDebug += " delete table " + strHbaseTabName + " exists !\r\n";
                System.out.println(" delete table " + strHbaseTabName + " exists !");

                hbaseadmin.disableTable(TableName.valueOf(strHbaseTabName));
                hbaseadmin.deleteTable(TableName.valueOf(strHbaseTabName));
            }
            TableDescriptorBuilder hTabDescBuild = TableDescriptorBuilder.newBuilder(TableName.valueOf(strHbaseTabName));
            ColumnFamilyDescriptor hColFamDesc = ColumnFamilyDescriptorBuilder.of(strHbaseColFamily);
            hTabDescBuild.setColumnFamily(hColFamDesc);
            TableDescriptor hTabDesc = hTabDescBuild.build();
            hbaseadmin.createTable(hTabDesc);
            strSrcDebug += " create the table " + strHbaseTabName + " !\r\n";
            System.out.println(" create the table " + strHbaseTabName + " !");

            //获取单个schemas全部表
            List<String> lArrCacheTabName = new ArrayList<String>();
            int iCacheTabCount = 0;
            //Cache - 先“CALL %SQL_Manager.TablesOnly('Sample')”获取全部表，只读取字段“NAME”就可以，缓冲全部表名称
            strSql = "CALL %SQL_Manager.TablesOnly('" + strCacheSchema + "')";
            cstJdbc = connJdbc.prepareCall(strSql);
            rsJdbc = cstJdbc.executeQuery();
            while(rsJdbc.next()){
                lArrCacheTabName.add(rsJdbc.getString("NAME"));
            }
            rsJdbc.close();
            iCacheTabCount = lArrCacheTabName.size();
            strSrcDebug += " Cache Total Tables : " + Integer.toString(iCacheTabCount) + "\r\n";
            System.out.println(" Cache Total Tables : " + Integer.toString(iCacheTabCount));
            //循环处理各个表结构
            if(iCacheTabCount>0){
                //先连接jh_tabinfo_01
                Table hTable = hbaseconn.getTable(hTableName);
                //常规化jh_tabinfo_01的列族名
                byte[] bhColFamilyName = Bytes.toBytes(strHbaseColFamily); //表族名
                //获取最新的rowkey值
                long lHbRowId = 0;
                Scan hScan = new Scan();
                hScan.setReversed(true); //反向查询
                hScan.setOneRowLimit(); //指定返回1行
                ResultScanner hResScan = hTable.getScanner(hScan);
                for(Result hRs : hResScan){
                    byte[] bhRowKey = hRs.getRow();
                    lHbRowId = bRowKeyTolRowKey(bhRowKey);
                    if(lHbRowId<0){
                        System.out.println("Get the last Rowkey Error !");
                        strSrcDebug += "Get the last Rowkey Error \r\n";
                    }
                    strSrcDebug += "last rowkey=" + Long.toString(lHbRowId) + " \r\n";
                    break; //只取一行
                } //for(Result hRs : hResScan){
                hResScan.close();
                lHbRowId ++;
                if(lHbRowId>0){
                    //无效的rowkey不继续
                    byte[] bhColTabName = Bytes.toBytes("TAB_NAME"); //族名
                    byte[] bhColFieCount = Bytes.toBytes("FLD_COUNT"); //族名
                    //每1000张表插入一次hbase
                    List<Put> hArrPut = new ArrayList<Put>();
                    int iPutIndex = 0;
                    int iPutMax = 10;
                    int iTmpColCount = 0;
                    int itmp=0;
                    String strTmpValue = "";
                    String strTmpColName = "";
                    for(int ii=0;ii<iCacheTabCount;ii++){
                        //获取第一行数据列的jdbcTypeName
                        Map<String, Integer> fieldJdbcTypeMap = new HashMap<>();
                        try {
                            strSrcDebug += " Get first row data : " + strCacheSchema + "." + lArrCacheTabName.get(ii) + "\r\n";
                            System.out.println(" Get first row data: " + strCacheSchema + "." + lArrCacheTabName.get(ii));

                            fieldJdbcTypeMap = createFieldJdbcTypeMap(connJdbc, strCacheSchema, lArrCacheTabName.get(ii));

                            strSrcDebug += " Get first row data finish: " + strCacheSchema + "." + lArrCacheTabName.get(ii) + "\r\n";
                            System.out.println(" Get first row data finish: " + strCacheSchema + "." + lArrCacheTabName.get(ii));
                        } catch (Exception e) {
                            strSrcDebug += " Get first row data error: " + strCacheSchema + "." + lArrCacheTabName.get(ii) + "\r\n";
                            System.out.println(" Get first row data error: " + strCacheSchema + "." + lArrCacheTabName.get(ii));
                            e.printStackTrace();

                            //如果获取数据表第一行数据失败，退出不处理该表
                            continue;
                        }

                        //rowkey
                        Put htmpPut = new Put(Bytes.toBytes(lHbRowId));
                        //表名称
                        strTmpValue = strCacheSchema + "." + lArrCacheTabName.get(ii);
                        String tableName = strTmpValue.toUpperCase();//表名大小
                        htmpPut.addColumn(bhColFamilyName,bhColTabName,Bytes.toBytes(tableName));
                        //Cache - 再用“CALL %SQL_Manager.Fields('Sample','Company')”读取表结构
                        strSql = "CALL %SQL_Manager.Fields('" + strCacheSchema + "','" + lArrCacheTabName.get(ii) + "')";
                        cstJdbc = connJdbc.prepareCall(strSql);
                        rsJdbc = cstJdbc.executeQuery();
                        strSrcDebug += strSql + "\r\n";

                        //整理各个字段
                        iTmpColCount = 0;
                        while(rsJdbc.next()){
                            iTmpColCount++;
                            strTmpColName = "";
                            //FIELD_NAME 字段名称
                            //DATATYPE 字段类型（cache专用）
                            //COLUMN_NUMBER 字段序号
                            //REQUIRED 不能为空字段，必须有内容
                            //UNIQUE 主键字段，唯一的
                            //COLLATION_FUNCTION ？
                            //HIDDEN 隐藏的字段
                            //MAXLEN 最大长度
                            //BLOB 是否blob字段
                            //CONTAINER_FIELD ？
                            //SELECTIVITY ？
                            //XDBC_TYPENAME 标准字段类型（通用）
                            //REFERENCE_TO ？
                            //表结构字段序号COLUMN_NUMBER
                            itmp = rsJdbc.getInt("COLUMN_NUMBER");
                            strTmpColName = "FLD_I_" + Integer.toString(itmp);
                            htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),Bytes.toBytes(itmp));
                            //表结构字段名称FIELD_NAME
                            strTmpValue = rsJdbc.getString("FIELD_NAME");
                            if(strTmpValue != null){
                                if(strTmpValue.length()>0){
                                    strTmpColName = "FLD_N_" + Integer.toString(itmp);
                                    htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),Bytes.toBytes(strTmpValue.toUpperCase()));
                                }
                            }
                            //表结构字段类型XDBC_TYPENAME
                            strTmpValue = rsJdbc.getString("XDBC_TYPENAME");
                            if(strTmpValue != null){
                                if(strTmpValue.length()>0){
                                    strTmpColName = "FLD_T_" + Integer.toString(itmp);
                                    htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),Bytes.toBytes(strTmpValue));
                                }
                            }
                            //表结构字段长度MAXLEN
                            strTmpValue = rsJdbc.getString("MAXLEN");
                            if(strTmpValue != null){
                                if(strTmpValue.length()>0){
                                    strTmpColName = "FLD_L_" + Integer.toString(itmp);
                                    int intTmpValue = 0;
                                    try {
                                        intTmpValue = Integer.valueOf(strTmpValue);
                                        htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),Bytes.toBytes(intTmpValue));
                                    } catch (NumberFormatException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                            //表结构字段是否必须字段，不允许为空；注意oracle等关系数据库的结构统一问题
                            strTmpValue = rsJdbc.getString("REQUIRED");
                            if(strTmpValue != null){
                                if(strTmpValue.length()>0){
                                    strTmpColName = "FLD_R_" + Integer.toString(itmp);
                                    byte byteTmpValue = 0;
                                    if ("Yes".equals(strTmpValue)) {
                                        byteTmpValue = 1;
                                    }
                                    htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),new byte[] {byteTmpValue});
                                }
                            }
                            //表结构字段是否为主键，唯一的；注意oracle等关系数据库的结构统一问题
                            strTmpValue = rsJdbc.getString("UNIQUE");
                            if(strTmpValue != null){
                                if(strTmpValue.length()>0){
                                    strTmpColName = "FLD_U_" + Integer.toString(itmp);
                                    byte byteTmpValue = 0;
                                    if ("Yes".equals(strTmpValue)) {
                                        byteTmpValue = 1;
                                    }
                                    htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),new byte[] {byteTmpValue});
                                }
                            }
                            //表结构字段是否为blob；用于兼容cache
                            strTmpValue = rsJdbc.getString("BLOB");
                            if(strTmpValue != null){
                                if(strTmpValue.length()>0){
                                    strTmpColName = "FLD_B_" + Integer.toString(itmp);
                                    byte byteTmpValue = 0;
                                    if ("Yes".equals(strTmpValue)) {
                                        byteTmpValue = 1;
                                    }
                                    htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),new byte[] {byteTmpValue});
                                }
                            }

                            //表结构字段jdbcTypeName
                            try {
                                String fieldName = rsJdbc.getString("FIELD_NAME");
                                Integer jdbcType = fieldJdbcTypeMap.get(fieldName);
                                if (jdbcType != null) {
                                    strTmpColName = "FLD_J_" + Integer.toString(itmp);
                                    htmpPut.addColumn(bhColFamilyName,Bytes.toBytes(strTmpColName),Bytes.toBytes(jdbcType));
                                }
                            } catch (Exception e) {
                                strSrcDebug += " hbase put jdbcType error: " + strCacheSchema + "." + lArrCacheTabName.get(ii) + "\r\n";
                                System.out.println(" hbase put jdbcType error: " + strCacheSchema + "." + lArrCacheTabName.get(ii));
                                e.printStackTrace();
                            }
                        } //while(rsJdbc.next()){
                        rsJdbc.close();
                        //该表拥有多少个字段
                        htmpPut.addColumn(bhColFamilyName,bhColFieCount,Bytes.toBytes(iTmpColCount));
                        strSrcDebug += lArrCacheTabName.get(ii) + " Fields " + Integer.toString(iTmpColCount) + " \r\n";
                        //先压入池内
                        hArrPut.add(htmpPut);
                        iPutIndex ++;
                        if(iPutIndex>=iPutMax){
                            //真正插入
                            hTable.put(hArrPut);
                            strSrcDebug += " put Cache Table Count=" + Integer.toString(iPutIndex) + " index=" + Integer.toString(ii) + " !\r\n";
                            System.out.println(" put Cache Table Count=" + Integer.toString(iPutIndex) + " index=" + Integer.toString(ii));
                            iPutIndex = 0;
                            hArrPut.clear();
                        }
                        lHbRowId ++;

                        //创建表表结构完成，初始时候Hbase表
                        String strBizHbaseTabName = strHbaseNamespace + ":" + strHbaseSysKey + "_" + tableName;
                        if ("1".equals(isNeedInitHbaseTable)) {
                            TableName hBizTableName = TableName.valueOf(strBizHbaseTabName);
                            if(hbaseadmin.tableExists(hBizTableName)){
                                strSrcDebug += " the biz table " + strBizHbaseTabName + " exists !\r\n";
                                System.out.println(" the biz table " + strBizHbaseTabName + " exists !");

                                strSrcDebug += " delete biz table " + strBizHbaseTabName + " exists !\r\n";
                                System.out.println(" delete biz table " + strBizHbaseTabName + " exists !");
                                hbaseadmin.disableTable(TableName.valueOf(strBizHbaseTabName));
                                hbaseadmin.deleteTable(TableName.valueOf(strBizHbaseTabName));
                            }
                            hTabDescBuild = TableDescriptorBuilder.newBuilder(TableName.valueOf(strBizHbaseTabName));
                            hColFamDesc = ColumnFamilyDescriptorBuilder.of(strHbaseColFamily);
                            hTabDescBuild.setColumnFamily(hColFamDesc);
                            hTabDesc = hTabDescBuild.build();
                            hbaseadmin.createTable(hTabDesc);
                            strSrcDebug += " create the biz table " + strBizHbaseTabName + " !\r\n";
                            System.out.println(" create the biz table " + strBizHbaseTabName + " !");
                        }

                    } //for(int ii=0;ii<iCacheTabCount;ii++){
                    if(!hArrPut.isEmpty()){
                        //真正插入
                        hTable.put(hArrPut);
                        iTmpColCount = hArrPut.size();
                        strSrcDebug += " put Cache Table Count=" + Integer.toString(iTmpColCount) + " index=" + Integer.toString(iCacheTabCount) + " !\r\n";
                        System.out.println(" put Cache Table Count=" + Integer.toString(iTmpColCount) + " index=" + Integer.toString(iCacheTabCount));
                        iPutIndex = 0;
                        hArrPut.clear();
                    } //if(hArrPut.isEmpty()){
                } //if(lHbRowId>0){
                hTable.close();
            } //if(iCacheTabCount>0){
            //free
            lArrCacheTabName.clear();

            System.out.println("DisConnected Cache and HBase ");
            //close hbase
            if(hbaseadmin != null){
                hbaseadmin.close();
            }
            if(hbaseconn != null){
                hbaseconn.close();
            }
            //close cache jdbc
            if(rsJdbc != null){
                rsJdbc.close();
            }
            if(connJdbc != null){
                connJdbc.close();
            }
        }catch(SQLException se){
            strSrcDebug += getErrorInfoFromException(se);
        }catch(Exception e){
            strSrcDebug += getErrorInfoFromException(e);
        }

        //write debug
        try{
            File file =new File("JhmkEtl2cStru.log" + "_" + strHbaseSysKey  + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            //if file doesnt exists, then create it
            if(!file.exists()){
                file.createNewFile();
            }
            //true = append file
            FileWriter fileWritter = new FileWriter(file.getName(),true);
            BufferedWriter bufferWritter = new BufferedWriter(fileWritter);
            bufferWritter.write(strSrcDebug);
            bufferWritter.close();
        }catch(IOException e){
            e.printStackTrace();
        }

        System.out.println(" ");
        System.out.println("End !");
        System.out.println(" ");
    } //public static void main(String[] args) {

    public static String getErrorInfoFromException(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString() + "\r\n";
        } catch (Exception e2) {
            return "getErrorInfoFromException Error!\r\n";
        }
    } //public static String getErrorInfoFromException(Exception e) {

    /*
    // 把Bytes类型的 HBase RowKey 转成 Long 类型的 RowKey
    //*/
    public static long bRowKeyTolRowKey(byte[] byteRowKey){
        /*long lRes = -1;
        if(byteRowKey.length != 8){
            return lRes;
        }
        long ltmp = 0;
        int jj = 56;
        lRes = 0;
        for(int ii=0;ii<8;ii++){
            ltmp = byteRowKey[ii];
            lRes += ltmp<<jj;
            jj -= 8;
        }
        return lRes;*/
        return Bytes.toLong(byteRowKey);
    } //public static long bRowKeyTolRowKey(byte[] byteRowKey){

    /**
     * 查询表第一行数据，获取每个字段JDBC类型
     */
    public static Map<String, Integer> createFieldJdbcTypeMap(java.sql.Connection connJdbc, String strCacheSchema, String strCacheTable) throws Exception{
        Map<String, Integer> map = new HashMap<>();

        Statement stmtJdbc = null;
        ResultSet rsJdbc = null;
        ResultSetMetaData rsmdJdbc = null;
        String strSql = null;
        int iCacheColCount = 0;

        try {
            stmtJdbc = connJdbc.createStatement();
            stmtJdbc.setQueryTimeout(4);
            strSql = "select top 1 * from " + strCacheSchema + "." + strCacheTable;
            rsJdbc = stmtJdbc.executeQuery(strSql);
            rsmdJdbc = rsJdbc.getMetaData();
            iCacheColCount = rsmdJdbc.getColumnCount();
            int ii;
            for(ii=0;ii<iCacheColCount;ii++){
                String columnName = rsmdJdbc.getColumnName(ii+1);
                Integer collumnType = rsmdJdbc.getColumnType(ii+1);
                map.put(columnName, collumnType);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if(rsJdbc != null){
                rsJdbc.close();
            }
            if(stmtJdbc != null){
                stmtJdbc.close();
            }
        }
        return map;
    }

}
