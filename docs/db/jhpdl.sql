
/*
//-----------------------------------------//
纯净数据湖的常用表结构与默认数据
HBase Shell 版
2020-02-28
HBase表结构、索引表表结构和数据内容补充
//-----------------------------------------//
//*/


/*
-- 创建命名空间
//*/
create_namespace 'JHPDL'


/*
-- 表结构字段描述字典表JHPDL:JH_DT_TABST
//*/
create 'JHPDL:JH_DT_TABST', 'CF'

/*
-- 列“TAB_RKTYPE”的值关联到'JHBAS:JH_DT_BAS'的描述
-- 后续“CF:T_xxxx”的值关联到'JHBAS:JH_DT_BAS'的描述
//*/
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_KEY', 'PAT'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_NAME', 'JH_PAT_INFO'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_DESC', '患者基本信息表'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:FLD_COUNT', "\x00\x2D"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0001', 'PAT0001'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0001', 'PATIENT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0001', '患者编号'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0002', 'PAT0002'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0003', 'PAT0003'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0003', 'PATIENT_NAME'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0003', '患者姓名'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0004', 'PAT0004'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0004', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0004', 'ALIAS_NAME'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0004', '别名'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0005', 'PAT0005'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0005', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_0005', "\x00\x0D"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0005', 'SEX'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0005', '性别'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0006', "\x00\x06"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0006', 'PAT0006'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0006', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0006', 'DATE_OF_BIRTH'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0006', '出生日期'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0007', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0007', 'PAT0007'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0007', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_0007', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0007', 'CHARGE_TYPE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0007', '费别'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0008', "\x00\x08"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0008', 'PAT0008'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0008', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_0008', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0008', 'INSURANCE_TYPE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0008', '医保类型'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0009', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0009', 'PAT0009'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0009', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0009', 'BIRTH_PLACE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0009', '出生地点'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_000A', "\x00\x10"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_000A', 'PAT0010'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_000A', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_000A', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_000A', 'MULTI_BIRTH_IND'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_000A', '双胞胎标记'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_000B', "\x00\x11"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_000B', 'PAT0011'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_000B', "\x00\x0A"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_000B', 'MULTI_BIRTH_ORDER'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_000B', '双胞胎顺序'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_000C', "\x00\x12"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_000C', 'PAT0012'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_000C', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_000C', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_000C', 'DEATH_IND'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_000C', '死亡标记'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_000D', "\x00\x13"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_000D', 'PAT0013'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_000D', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_000D', 'DEATH_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_000D', '死亡时间'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_000E', "\x00\x14"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_000E', 'PAT0014'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_000E', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_000E', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_000E', 'MARITAL_STATUS'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_000E', '婚姻状态'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_000F', "\x00\x15"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_000F', 'PAT0015'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_000F', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_000F', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_000F', 'ORGAN_DONOR_IND'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_000F', '器官捐献者标记'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0010', "\x00\x16"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0010', 'PAT0016'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0010', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0010', 'DISABILITY_CODE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0010', '残疾码'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0011', "\x00\x17"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0011', 'PAT0017'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0011', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_0011', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0011', 'NATION'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0011', '国籍'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0012', "\x00\x18"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0012', 'PAT0018'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0012', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_0012', "\x00\x0A"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0012', 'NATIONALITY'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0012', '民族'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0013', "\x00\x19"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0013', 'PAT0019'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0013', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0013', 'NATIVE_PROVINCE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0013', '籍贯'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0014', "\x00\x20"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0014', 'PAT0020'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0014', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_0014', "\x00\x08"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0014', 'FINANCIAL_SITUATION'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0014', '经济状况'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0015', "\x00\x21"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0015', 'PAT0021'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0015', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0015', 'WORK_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0015', '参加工作时间'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0016', "\x00\x22"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0016', 'PAT0022'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0016', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0016', 'BUSINESS_PHONE_PHONE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0016', '单位电话'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0017', "\x00\x23"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0017', 'PAT0023'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0017', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0017', 'HOME_PHONE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0017', '家庭电话'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0018', "\x00\x24"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0018', 'PAT0024'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0018', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0018', 'OTHER_PHONE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0018', '其他电话'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0019', "\x00\x25"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0019', 'PAT0025'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0019', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0019', 'MOBILE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0019', '手机'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_001A', "\x00\x26"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_001A', 'PAT0026'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_001A', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_001A', 'POSTCODE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_001A', '邮编'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_001B', "\x00\x27"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_001B', 'PAT0027'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_001B', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_001B', 'MAILING_ADDR_PROVINCE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_001B', '联系地址省份'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_001C', "\x00\x28"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_001C', 'PAT0028'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_001C', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_001C', 'MAILING_ADDR_CITY'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_001C', '联系地址地市'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_001D', "\x00\x29"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_001D', 'PAT0029'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_001D', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_001D', 'MAILING_ADDR_COUNTY'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_001D', '联系地址区县'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_001E', "\x00\x30"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_001E', 'PAT0030'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_001E', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_001E', 'MAILING_ADDR_STREET'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_001E', '联系地址街道'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_001F', "\x00\x31"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_001F', 'PAT0031'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_001F', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_001F', 'MAILING_ADDRESS'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_001F', '联系详细地址'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0020', "\x00\x32"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0020', 'PAT0032'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0020', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0020', 'FAMILY_ADDR_PROVINCE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0020', '户籍所在地省份'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0021', "\x00\x33"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0021', 'PAT0033'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0021', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0021', 'FAMILY_ADDR_CITY'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0021', '户籍所在地地市'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0022', "\x00\x34"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0022', 'PAT0034'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0022', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0022', 'FAMILY_ADDR_COUNTY'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0022', '户籍所在地区县'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0023', "\x00\x35"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0023', 'PAT0035'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0023', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0023', 'FAMILY_ADDRESS'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0023', '户籍详细地址'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0024', "\x00\x36"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0024', 'PAT0036'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0024', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0024', 'EMPLOYER_COMPANY'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0024', '工作单位名称'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0025', "\x00\x37"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0025', 'PAT0037'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0025', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0025', 'EMPLOYER_POSTCODE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0025', '工作单位邮编'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0026', "\x00\x38"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0026', 'PAT0038'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0026', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0026', 'EMPLOYER_ADDRESS'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0026', '工作单位地址'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0027', "\x00\x39"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0027', 'PAT0039'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0027', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0027', 'NEXT_OF_KIN'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0027', '联系人'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0028', "\x00\x40"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0028', 'PAT0040'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0028', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:K_0028', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0028', 'RELATIONSHIP'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0028', '联系人与患者关系'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_0029', "\x00\x41"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_0029', 'PAT0041'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_0029', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_0029', 'NEXT_OF_KIN_PHONE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_0029', '联系人电话'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_002A', "\x00\x42"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_002A', 'PAT0042'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_002A', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_002A', 'NEXT_OF_KIN_POST'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_002A', '联系人邮编'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_002B', "\x00\x43"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_002B', 'PAT0043'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_002B', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_002B', 'NEXT_OF_KIN_ADDR'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_002B', '联系人地址'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_002C', "\x00\x44"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_002C', 'PAT0044'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_002C', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_002C', 'REGISTRATION_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_002C', '首次登记时间'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:I_002D', "\x00\x45"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:N_002D', 'PAT0045'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:T_002D', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:C_002D', 'LAST_UPDATE'
put 'JHPDL:JH_DT_TABST', "\x00\x01", 'CF:D_002D', '最后更新时间'


put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:TAB_KEY', 'IHR'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:TAB_NAME', 'JH_IN_HOSP'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:TAB_DESC', '患者住院就诊记录表'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:FLD_COUNT', "\x00\x0B"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0001', 'IHR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0001', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0001', '住院就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0002', 'IHR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0003', 'IHR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0003', '就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0004', 'IHR0004'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0004', "\x00\x06"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0004', 'VISIT_NUM'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0004', '住院次数'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0005', 'IHR0005'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0005', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:K_0005', "\x00\x0D"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0005', 'ADMISSION_DEPT'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0005', '入院科室'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0006', "\x00\x06"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0006', 'IHR0006'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0006', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0006', 'ADMISSION_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0006', '入院时间'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0007', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0007', 'IHR0007'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0007', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:K_0007', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0007', 'CURRENT_DEPT'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0007', '所在科室'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0008', "\x00\x08"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0008', 'IHR0008'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0008', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:K_0008', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0008', 'CURRENT_WARD'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0008', '所在病区'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_0009', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_0009', 'IHR0009'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_0009', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:K_0009', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_0009', 'CURRENT_BED'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_0009', '所在床位'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_000A', "\x00\x0A"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_000A', 'IHR0010'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_000A', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:K_000A', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_000A', 'DISCHARGE_DEPT'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_000A', '出院科室'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:I_000B', "\x00\x0B"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:N_000B', 'IHR0011'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:T_000B', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:C_000B', 'DISCHARGE_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x02", 'CF:D_000B', '出院时间'


put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:TAB_KEY', 'IAR'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:TAB_NAME', 'JH_IN_ADT'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:TAB_DESC', '患者入出转记录表'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:FLD_COUNT', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:N_0001', 'IAR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:C_0001', 'IN_ADT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:D_0001', '入出转编号'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:N_0002', 'IAR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:N_0003', 'IAR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:C_0003', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:D_0003', '住院就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:N_0004', 'IAR0004'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:T_0004', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:K_0004', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:C_0004', 'ADT_TYPE'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:D_0004', '发生类型'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:N_0005', 'IAR0005'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:T_0005', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:C_0005', 'ADT_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:D_0005', '发生时间'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:I_0006', "\x00\x06"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:N_0006', 'IAR0006'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:T_0006', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:K_0006', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:C_0006', 'ADT_DEPT'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:D_0006', '发生科室'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:I_0007', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:N_0007', 'IAR0007'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:T_0007', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:K_0007', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:C_0007', 'ADT_WARD'
put 'JHPDL:JH_DT_TABST', "\x00\x03", 'CF:D_0007', '发生病区'


put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:TAB_KEY', 'IOR'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:TAB_NAME', 'JH_IN_ORDERS'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:TAB_DESC', '患者住院就诊记录表'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:FLD_COUNT', "\x00\x0B"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0001', 'IOR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0001', 'IN_ORDER_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0001', '医嘱项编号'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0002', 'IOR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0003', 'IOR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0003', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0003', '住院就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0004', 'IOR0004'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0004', "\x00\x06"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0004', 'ORDER_NO'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0004', '医嘱序号'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0005', 'IOR0005'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0005', "\x00\x0A"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0005', 'ORDER_SUB_NO'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0005', '医嘱子序号'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0006', "\x00\x06"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0006', 'IOR0006'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0006', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:K_0006', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0006', 'ORDER_CODE'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0006', '医嘱代码'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0007', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0007', 'IOR0007'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0007', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0007', 'ORDER_TEXT'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0007', '医嘱正文'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0008', "\x00\x08"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0008', 'IOR0008'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0008', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:K_0008', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0008', 'ORDER_CLASS'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0008', '医嘱类别'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_0009', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_0009', 'IOR0009'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_0009', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:K_0009', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_0009', 'REPEAT_INDICATOR'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_0009', '长期医嘱标志'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_000A', "\x00\x0A"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_000A', 'IOR0010'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_000A', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_000A', 'DOSAGE'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_000A', '剂量'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:I_000B', "\x00\x0B"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:N_000B', 'IOR0011'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:T_000B', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:C_000B', 'DOSAGE_UNITS'
put 'JHPDL:JH_DT_TABST', "\x00\x04", 'CF:D_000B', '剂量单位'


put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:TAB_KEY', 'LAP'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:TAB_NAME', 'JH_LAB_APPLY'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:TAB_DESC', '患者检验申请单（住院和门诊）'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:FLD_COUNT', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:N_0001', 'LAP0001'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:C_0001', 'LAB_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:D_0001', '检验申请单编号'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:N_0002', 'LAP0002'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:N_0003', 'LAP0003'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:D_0003', '就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:N_0004', 'LAP0004'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:T_0004', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:K_0004', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:C_0004', 'SPECIMEN'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:D_0004', '标本'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:N_0005', 'LAP0005'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:T_0005', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:C_0005', 'SPECIMEN_DESC'
put 'JHPDL:JH_DT_TABST', "\x00\x05", 'CF:D_0005', '标本说明'


put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:TAB_KEY', 'PAV'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:TAB_NAME', 'JH_PAT_VISIT'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:TAB_DESC', '通用就诊记录（不分门诊、住院、急诊等）'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:FLD_COUNT', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:N_0001', 'PAV0001'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:C_0001', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:D_0001', '就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:N_0002', 'PAV0002'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:N_0003', 'PAV0003'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:C_0003', 'PATIENT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:D_0003', '患者编号'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:N_0004', 'PAV0004'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:T_0004', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:C_0004', 'VISIT_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:D_0004', '就诊开始时间'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:N_0005', 'PAV0005'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:T_0005', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:K_0005', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:C_0005', 'VISIT_TYPE'
put 'JHPDL:JH_DT_TABST', "\x00\x06", 'CF:D_0005', '就诊类型'


put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:TAB_KEY', 'CHR'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:TAB_NAME', 'JH_CU_HOSP'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:TAB_DESC', '体检就诊记录'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:FLD_COUNT', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:N_0001', 'CHR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:C_0001', 'CHECK_UP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:D_0001', '体检就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:N_0002', 'CHR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:N_0003', 'CHR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:D_0003', '就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:N_0004', 'CHR0004'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:T_0004', "\x00\x07"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:C_0004', 'CHECK_UP_DATE'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:D_0004', '体检日期'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:N_0005', 'CHR0005'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:T_0005', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:K_0005', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:C_0005', 'CHECK_UP_TYPE'
put 'JHPDL:JH_DT_TABST', "\x00\x07", 'CF:D_0005', '体检类型'


put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:TAB_KEY', 'EHR'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:TAB_NAME', 'JH_EGY_HOSP'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:TAB_DESC', '急诊就诊记录'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:FLD_COUNT', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:N_0001', 'EHR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:C_0001', 'EMERGENCY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:D_0001', '急诊就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:N_0002', 'EHR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:N_0003', 'EHR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:D_0003', '就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:N_0004', 'EHR0004'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:T_0004', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:C_0004', 'EMEY_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x08", 'CF:D_0004', '到达急诊时间'


put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:TAB_KEY', 'SHR'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:TAB_NAME', 'JH_SL_HOSP'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:TAB_DESC', '专科就诊记录（如口腔、妇产科等）'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:FLD_COUNT', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:N_0001', 'SHR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:C_0001', 'SPECIALIST_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:D_0001', '专科就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:N_0002', 'SHR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:N_0003', 'SHR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:D_0003', '就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:I_0004', "\x00\x04"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:N_0004', 'SHR0004'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:T_0004', "\x00\x09"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:C_0004', 'SPECIALIST_TIME'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:D_0004', '专科就诊开始时间'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:I_0005', "\x00\x05"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:N_0005', 'SHR0005'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:T_0005', "\x00\x0C"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:K_0005', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:C_0005', 'SPECIALIST_TYPE'
put 'JHPDL:JH_DT_TABST', "\x00\x09", 'CF:D_0005', '专科类型'


put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:TAB_KEY', 'LAD'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:TAB_NAME', 'JH_LAB_APY_DTL'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:TAB_DESC', '检验申请单明细'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:N_0001', 'LAD0001'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:C_0001', 'LAB_APY_DTL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:D_0001', '检验申请明细编号'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:N_0002', 'LAD0002'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:N_0003', 'LAD0003'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:C_0003', 'LAB_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0A", 'CF:D_0003', '检验申请单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:TAB_KEY', 'LTR'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:TAB_NAME', 'JH_LAB_TRACE'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:TAB_DESC', '检验标本跟踪'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:N_0001', 'LTR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:C_0001', 'LAB_TRACE_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:D_0001', '检验标本追踪编号'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:N_0002', 'LTR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:N_0003', 'LTR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:C_0003', 'LAB_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0B", 'CF:D_0003', '检验申请单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:TAB_KEY', 'LRS'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:TAB_NAME', 'JH_LAB_RESULT'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:TAB_DESC', '检验结果'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:N_0001', 'LRS0001'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:C_0001', 'LAB_RESULT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:D_0001', '检验结果编号'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:N_0002', 'LRS0002'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:N_0003', 'LRS0003'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:C_0003', 'LAB_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0C", 'CF:D_0003', '检验申请单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:TAB_KEY', 'LRD'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:TAB_NAME', 'JH_LAB_RES_DTL'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:TAB_DESC', '检验结果明细'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:N_0001', 'LRD0001'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:C_0001', 'LAB_RES_DTL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:D_0001', '检验结果明细编号'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:N_0002', 'LRD0002'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:N_0003', 'LRD0003'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:C_0003', 'LAB_RESULT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0D", 'CF:D_0003', '检验结果编号'


put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:TAB_KEY', 'LRV'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:TAB_NAME', 'JH_LAB_RES_VIS'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:TAB_DESC', '细菌培养结果'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:N_0001', 'LRV0001'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:C_0001', 'LAB_RES_VIS_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:D_0001', '细菌培养结果编号'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:N_0002', 'LRV0002'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:N_0003', 'LRV0003'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:C_0003', 'LAB_RESULT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0E", 'CF:D_0003', '检验结果编号'


put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:TAB_KEY', 'LRC'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:TAB_NAME', 'JH_LAB_RES_CTL'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:TAB_DESC', '检验危急值记录'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:N_0001', 'LRC0001'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:C_0001', 'LAB_RES_CTL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:D_0001', '检验危急值编号'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:N_0002', 'LRC0002'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:N_0003', 'LRC0003'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:C_0003', 'LAB_RESULT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x0F", 'CF:D_0003', '检验结果编号'


put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:TAB_KEY', 'EAP'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:TAB_NAME', 'JH_EXAM_APPLY'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:TAB_DESC', '检查申请'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:N_0001', 'EAP0001'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:C_0001', 'EXAM_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:D_0001', '检查申请单编号'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:N_0002', 'EAP0002'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:N_0003', 'EAP0003'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x10", 'CF:D_0003', '就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:TAB_KEY', 'EAD'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:TAB_NAME', 'JH_EXAM_APY_DTL'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:TAB_DESC', '检查申请明细'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:N_0001', 'EAD0001'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:C_0001', 'EXAM_APY_DTL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:D_0001', '检查申请明细编号'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:N_0002', 'EAD0002'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:N_0003', 'EAD0003'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:C_0003', 'EXAM_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x11", 'CF:D_0003', '检查申请单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:TAB_KEY', 'EAS'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:TAB_NAME', 'JH_EXAM_APY_SCH'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:TAB_DESC', '检查预约排期'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:N_0001', 'EAS0001'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:C_0001', 'EXAM_APY_SCH_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:D_0001', '检查排期编号'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:N_0002', 'EAS0002'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:N_0003', 'EAS0003'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:C_0003', 'EXAM_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x12", 'CF:D_0003', '检查申请单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:TAB_KEY', 'ERT'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:TAB_NAME', 'JH_EXAM_REPORT'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:TAB_DESC', '检查报告'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:N_0001', 'ERT0001'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:C_0001', 'EXAM_REPORT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:D_0001', '检查报告编号'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:N_0002', 'ERT0002'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:N_0003', 'ERT0003'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:C_0003', 'EXAM_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x13", 'CF:D_0003', '检查申请单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:TAB_KEY', 'ERI'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:TAB_NAME', 'JH_EXAM_REP_IMG'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:TAB_DESC', '检查影像索引'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:N_0001', 'ERI0001'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:C_0001', 'EXAM_REP_IMG_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:D_0001', '检查影像编号'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:N_0002', 'ERI0002'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:N_0003', 'ERI0003'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:C_0003', 'EXAM_REPORT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x14", 'CF:D_0003', '检查报告编号'


put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:TAB_KEY', 'OAP'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:TAB_NAME', 'JH_OP_APPLY'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:TAB_DESC', '手术申请'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:N_0001', 'OAP0001'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:C_0001', 'OPER_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:D_0001', '手术申请单编号'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:N_0002', 'OAP0002'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:N_0003', 'OAP0003'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x15", 'CF:D_0003', '就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:TAB_KEY', 'OAS'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:TAB_NAME', 'JH_OP_SCH'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:TAB_DESC', '手术预约排期'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:N_0001', 'OAS0001'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:C_0001', 'OPER_APY_SCH_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:D_0001', '手术排期编号'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:N_0002', 'OAS0002'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:N_0003', 'OAS0003'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:C_0003', 'OPER_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x16", 'CF:D_0003', '手术申请单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:TAB_KEY', 'BAP'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:TAB_NAME', 'JH_BLD_APPLY'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:TAB_DESC', '输血申请'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:N_0001', 'BAP0001'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:C_0001', 'BLD_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:D_0001', '输血申请单编号'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:N_0002', 'BAP0002'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:N_0003', 'BAP0003'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x17", 'CF:D_0003', '就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:TAB_KEY', 'HAP'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:TAB_NAME', 'JH_PHLY_APPLY'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:TAB_DESC', '病理申请'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:N_0001', 'HAP0001'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:C_0001', 'PHLY_APPLY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:D_0001', '病理申请单编号'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:N_0002', 'HAP0002'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:N_0003', 'HAP0003'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x18", 'CF:D_0003', '就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:TAB_KEY', 'IDR'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:TAB_NAME', 'JH_IN_DIAG'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:TAB_DESC', '住院临床诊断记录'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:N_0001', 'IDR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:C_0001', 'IN_DIAG_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:D_0001', '住院临床诊断编号'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:N_0002', 'IDR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:N_0003', 'IDR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:C_0003', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x19", 'CF:D_0003', '住院就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:TAB_KEY', 'IPR'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:TAB_NAME', 'JH_IN_OPER'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:TAB_DESC', '住院临床手术记录'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:N_0001', 'IPR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:C_0001', 'IN_OPER_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:D_0001', '住院临床手术编号'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:N_0002', 'IPR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:N_0003', 'IPR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:C_0003', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1A", 'CF:D_0003', '住院就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:TAB_KEY', 'IFP'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:TAB_NAME', 'JH_FIRST_PAGE'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:TAB_DESC', '住院病案首页'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:N_0001', 'IFP0001'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:C_0001', 'FIRST_PAGE_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:D_0001', '住院病案首页编号'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:N_0002', 'IFP0002'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:N_0003', 'IFP0003'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:C_0003', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1B", 'CF:D_0003', '住院就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:TAB_KEY', 'IFD'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:TAB_NAME', 'JH_FP_DIAG'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:TAB_DESC', '首页编目诊断记录'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:N_0001', 'IFD0001'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:C_0001', 'FP_DIAG_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:D_0001', '编目诊断编号'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:N_0002', 'IFD0002'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:N_0003', 'IFD0003'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:C_0003', 'FIRST_PAGE_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1C", 'CF:D_0003', '住院病案首页编号'


put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:TAB_KEY', 'IFR'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:TAB_NAME', 'JH_FP_OPER'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:TAB_DESC', '首页编目手术记录'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:N_0001', 'IFR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:C_0001', 'FP_OPER_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:D_0001', '编目手术编号'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:N_0002', 'IFR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:N_0003', 'IFR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:C_0003', 'FIRST_PAGE_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1D", 'CF:D_0003', '住院病案首页编号'


put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:TAB_KEY', 'IFS'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:TAB_NAME', 'JH_FP_SUB'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:TAB_DESC', '病案首页附页'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:N_0001', 'IFS0001'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:C_0001', 'FP_SUB_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:D_0001', '首页附页编号'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:N_0002', 'IFS0002'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:N_0003', 'IFS0003'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:C_0003', 'FIRST_PAGE_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1E", 'CF:D_0003', '住院病案首页编号'


put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:TAB_KEY', 'IMR'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:TAB_NAME', 'JH_IN_MR'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:TAB_DESC', '住院电子病历记录'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:N_0001', 'IMR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:C_0001', 'IN_MR_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:D_0001', '住院病历记录编号'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:N_0002', 'IMR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:N_0003', 'IMR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:C_0003', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x1F", 'CF:D_0003', '住院就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:TAB_KEY', 'IMF'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:TAB_NAME', 'JH_IN_MRFILE'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:TAB_DESC', '住院病历文书内容'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:N_0001', 'IMF0001'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:C_0001', 'IN_MRFILE_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:D_0001', '住院病历文书编号'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:N_0002', 'IMF0002'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:N_0003', 'IMF0003'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:C_0003', 'IN_MR_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x20", 'CF:D_0003', '住院病历记录编号'


put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:TAB_KEY', 'IVS'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:TAB_NAME', 'JH_VITAL_SIGNS'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:TAB_DESC', '生命体征记录'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:N_0001', 'IVS0001'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:C_0001', 'VITAL_SIGNS_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:D_0001', '生命体征记录编号'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:N_0002', 'IVS0002'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:N_0003', 'IVS0003'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:C_0003', 'IN_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x21", 'CF:D_0003', '住院就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:TAB_KEY', 'PAF'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:TAB_NAME', 'JH_FOLLOW_UP'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:TAB_DESC', '随访记录'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:N_0001', 'PAF0001'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:C_0001', 'FOLLOW_UP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:D_0001', '随访记录编号'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:N_0002', 'PAF0002'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:N_0003', 'PAF0003'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x22", 'CF:D_0003', '就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:TAB_KEY', 'PFB'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:TAB_NAME', 'JH_FEE_BILL'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:TAB_DESC', '费用账单'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:N_0001', 'PFB0001'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:C_0001', 'FEE_BILL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:D_0001', '费用账单编号'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:N_0002', 'PFB0002'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:N_0003', 'PFB0003'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x23", 'CF:D_0003', '就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:TAB_KEY', 'PFD'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:TAB_NAME', 'JH_FEE_BL_DTL'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:TAB_DESC', '费用账单明细'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:N_0001', 'PFD0001'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:C_0001', 'FEE_BL_DTL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:D_0001', '费用账单明细编号'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:N_0002', 'PFD0002'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:N_0003', 'PFD0003'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:C_0003', 'FEE_BILL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x24", 'CF:D_0003', '费用账单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:TAB_KEY', 'PFP'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:TAB_NAME', 'JH_FEE_PAY'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:TAB_DESC', '费用结算记录'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:N_0001', 'PFP0001'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:C_0001', 'FEE_PAY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:D_0001', '费用结算编号'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:N_0002', 'PFP0002'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:N_0003', 'PFP0003'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:C_0003', 'FEE_BILL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x25", 'CF:D_0003', '费用账单编号'


put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:TAB_KEY', 'PFL'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:TAB_NAME', 'JH_FEE_PAY_DTL'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:TAB_DESC', '费用结算明细'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:N_0001', 'PFL0001'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:C_0001', 'FEE_PAY_DTL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:D_0001', '费用结算明细编号'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:N_0002', 'PFL0002'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:N_0003', 'PFL0003'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:C_0003', 'FEE_PAY_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x26", 'CF:D_0003', '费用结算编号'


put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:TAB_KEY', 'OHR'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:TAB_NAME', 'JH_OUT_HOSP'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:TAB_DESC', '门诊就诊记录'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:N_0001', 'OHR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:C_0001', 'OUT_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:D_0001', '门诊就诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:N_0002', 'OHR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:N_0003', 'OHR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:C_0003', 'PAT_VISIT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x27", 'CF:D_0003', '就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:TAB_KEY', 'OMR'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:TAB_NAME', 'JH_OUT_MR'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:TAB_DESC', '门诊电子病历记录'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:N_0001', 'OMR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:C_0001', 'OUT_MR_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:D_0001', '门诊病历记录编号'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:N_0002', 'OMR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:N_0003', 'OMR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:C_0003', 'OUT_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x28", 'CF:D_0003', '门诊就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:TAB_KEY', 'OOR'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:TAB_NAME', 'JH_OUT_ORDERS'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:TAB_DESC', '门诊处方'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:N_0001', 'OOR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:C_0001', 'OUT_ORDERS_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:D_0001', '门诊处方编号'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:N_0002', 'OOR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:N_0003', 'OOR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:C_0003', 'OUT_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x29", 'CF:D_0003', '门诊就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:TAB_KEY', 'OOD'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:TAB_NAME', 'JH_OUT_ORD_DTL'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:TAB_DESC', '门诊处方明细'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:N_0001', 'OOD0001'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:C_0001', 'OUT_ORD_DTL_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:D_0001', '处方明细编号'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:N_0002', 'OOD0002'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:N_0003', 'OOD0003'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:C_0003', 'OUT_ORDERS_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x2A", 'CF:D_0003', '门诊处方编号'


put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:TAB_KEY', 'OSR'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:TAB_NAME', 'JH_OUT_SHUNT'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:TAB_DESC', '门诊分诊'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:N_0001', 'OSR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:C_0001', 'OUT_SHUNT_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:D_0001', '门诊分诊编号'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:N_0002', 'OSR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:N_0003', 'OSR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:C_0003', 'OUT_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x2B", 'CF:D_0003', '门诊就诊编号'


put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:TAB_KEY', 'ORR'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:TAB_NAME', 'JH_OUT_RVN'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:TAB_DESC', '门诊预约记录'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:TAB_RKTYPE', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:FLD_COUNT', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:I_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:N_0001', 'ORR0001'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:T_0001', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:C_0001', 'OUT_RVN_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:D_0001', '门诊预约记录编号'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:I_0002', "\x00\x02"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:N_0002', 'ORR0002'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:T_0002', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:C_0002', 'HIS_KEY'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:D_0002', 'HIS系统主键'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:I_0003', "\x00\x03"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:N_0003', 'ORR0003'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:T_0003', "\x00\x01"
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:C_0003', 'OUT_HOSP_SN'
put 'JHPDL:JH_DT_TABST', "\x00\x2C", 'CF:D_0003', '门诊就诊编号'



/*
-- 样本:患者基本信息表 JH_PAT_INFO
//*/
create 'JHPDL:JH_PAT_INFO', 'CF'

put 'JHPDL:JH_PAT_INFO', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAT0001', 'P00.000.001'
put 'JHPDL:JH_PAT_INFO', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAT0002', '1234_1'
put 'JHPDL:JH_PAT_INFO', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAT0003', '张三'
put 'JHPDL:JH_PAT_INFO', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAT0005', '1#,#1#,#男'

/*
-- 样本:患者住院就诊记录表 JH_IN_HOSP
//*/
create 'JHPDL:JH_IN_HOSP', 'CF'

put 'JHPDL:JH_IN_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IHR0001', 'H00.000.001'
put 'JHPDL:JH_IN_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IHR0002', '12345_1'
put 'JHPDL:JH_IN_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IHR0003', 'V00.000.001'
put 'JHPDL:JH_IN_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IHR0004', "\x00\x01"


/*
-- 样本:患者入出转记录表 JH_IN_ADT
//*/
create 'JHPDL:JH_IN_ADT', 'CF'

put 'JHPDL:JH_IN_ADT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IAR0001', 'ZA0.000.000.001'
put 'JHPDL:JH_IN_ADT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IAR0002', '12345_1_33'
put 'JHPDL:JH_IN_ADT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IAR0003', 'H00.000.001'
put 'JHPDL:JH_IN_ADT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IAR0005', '2018-12-24 09:23:44'

/*
-- 样本:住院患者的开立医嘱主表 JH_IN_ORDERS
//*/
create 'JHPDL:JH_IN_ORDERS', 'CF'

put 'JHPDL:JH_IN_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IOR0001', 'ZD0.000.000.001'
put 'JHPDL:JH_IN_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IOR0002', '12345_1_33_2_1'
put 'JHPDL:JH_IN_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IOR0003', 'H00.000.001'
put 'JHPDL:JH_IN_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IOR0004', "\x00\x01"
put 'JHPDL:JH_IN_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IOR0005', "\x01"

/*
-- 样本:患者检验申请单 JH_LAB_APPLY
//*/
create 'JHPDL:JH_LAB_APPLY', 'CF'

put 'JHPDL:JH_LAB_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAP0001', 'LA0.000.000.001'
put 'JHPDL:JH_LAB_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAP0002', '88888_11'
put 'JHPDL:JH_LAB_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAP0003', 'V00.000.001'
put 'JHPDL:JH_LAB_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAP0004', '1#,#1#,#血'
put 'JHPDL:JH_LAB_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAP0005', '饭后一小时'

/*
-- 样本:通用就诊记录（不分门诊、住院、急诊等） JH_PAT_VISIT
//*/
create 'JHPDL:JH_PAT_VISIT', 'CF'

put 'JHPDL:JH_PAT_VISIT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAV0001', 'V00.000.001'
put 'JHPDL:JH_PAT_VISIT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAV0002', '1234_1'
put 'JHPDL:JH_PAT_VISIT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAV0003', 'P00.000.001'
put 'JHPDL:JH_PAT_VISIT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAV0004', '2020-01-01 00:00:00'
put 'JHPDL:JH_PAT_VISIT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAV0005', '3#,#4#,#门诊'

/*
-- 样本:体检记录 JH_CU_HOSP
//*/
create 'JHPDL:JH_CU_HOSP', 'CF'

put 'JHPDL:JH_CU_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:CHR0001', 'C00.000.001'
put 'JHPDL:JH_CU_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:CHR0002', '1234_1'
put 'JHPDL:JH_CU_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:CHR0003', 'V00.000.001'
put 'JHPDL:JH_CU_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:CHR0004', '2020-01-01 00:00:00'
put 'JHPDL:JH_CU_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:CHR0005', '3#,#4#,#团体'

/*
-- 样本:急诊记录 JH_EGY_HOSP
//*/
create 'JHPDL:JH_EGY_HOSP', 'CF'

put 'JHPDL:JH_EGY_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EHR0001', 'E00.000.001'
put 'JHPDL:JH_EGY_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EHR0002', '1234_1'
put 'JHPDL:JH_EGY_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EHR0003', 'V00.000.001'
put 'JHPDL:JH_EGY_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EHR0004', '2020-01-01 00:00:00'

/*
-- 样本:专科就诊记录（如口腔、妇产科等） JH_SL_HOSP
//*/
create 'JHPDL:JH_SL_HOSP', 'CF'

put 'JHPDL:JH_SL_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:SHR0001', 'S00.000.001'
put 'JHPDL:JH_SL_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:SHR0002', '1234_1'
put 'JHPDL:JH_SL_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:SHR0003', 'V00.000.001'
put 'JHPDL:JH_SL_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:SHR0004', '2020-01-01 00:00:00'
put 'JHPDL:JH_SL_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:SHR0005', '3#,#4#,#口腔'

/*
-- 样本:检验申请单明细 JH_LAB_APY_DTL
//*/
create 'JHPDL:JH_LAB_APY_DTL', 'CF'

put 'JHPDL:JH_LAB_APY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAD0001', 'LD0.000.000.001'
put 'JHPDL:JH_LAB_APY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAD0002', '1234_1'
put 'JHPDL:JH_LAB_APY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LAD0003', 'LA0.000.000.001'

/*
-- 样本:检验标本跟踪 JH_LAB_TRACE
//*/
create 'JHPDL:JH_LAB_TRACE', 'CF'

put 'JHPDL:JH_LAB_TRACE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LTR0001', 'LT0.000.000.001'
put 'JHPDL:JH_LAB_TRACE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LTR0002', '1234_1'
put 'JHPDL:JH_LAB_TRACE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LTR0003', 'LA0.000.000.001'

/*
-- 样本:检验结果 JH_LAB_RESULT
//*/
create 'JHPDL:JH_LAB_RESULT', 'CF'

put 'JHPDL:JH_LAB_RESULT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRS0001', 'LR0.000.000.001'
put 'JHPDL:JH_LAB_RESULT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRS0002', '1234_1'
put 'JHPDL:JH_LAB_RESULT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRS0003', 'LA0.000.000.001'

/*
-- 样本:检验结果明细 JH_LAB_RES_DTL
//*/
create 'JHPDL:JH_LAB_RES_DTL', 'CF'

put 'JHPDL:JH_LAB_RES_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRD0001', 'LL0.000.000.001'
put 'JHPDL:JH_LAB_RES_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRD0002', '1234_1'
put 'JHPDL:JH_LAB_RES_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRD0003', 'LR0.000.000.001'

/*
-- 样本:细菌培养结果 JH_LAB_RES_VIS
//*/
create 'JHPDL:JH_LAB_RES_VIS', 'CF'

put 'JHPDL:JH_LAB_RES_VIS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRV0001', 'LV0.000.000.001'
put 'JHPDL:JH_LAB_RES_VIS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRV0002', '1234_1'
put 'JHPDL:JH_LAB_RES_VIS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRV0003', 'LR0.000.000.001'

/*
-- 样本:检验危急值记录 JH_LAB_RES_CTL
//*/
create 'JHPDL:JH_LAB_RES_CTL', 'CF'

put 'JHPDL:JH_LAB_RES_CTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRC0001', 'LC0.000.000.001'
put 'JHPDL:JH_LAB_RES_CTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRC0002', '1234_1'
put 'JHPDL:JH_LAB_RES_CTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:LRC0003', 'LR0.000.000.001'

/*
-- 样本:检查申请 JH_EXAM_APPLY
//*/
create 'JHPDL:JH_EXAM_APPLY', 'CF'

put 'JHPDL:JH_EXAM_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAP0001', 'EA0.000.000.001'
put 'JHPDL:JH_EXAM_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAP0002', '1234_1'
put 'JHPDL:JH_EXAM_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAP0003', 'V00.000.001'

/*
-- 样本:检查申请明细 JH_EXAM_APY_DTL
//*/
create 'JHPDL:JH_EXAM_APY_DTL', 'CF'

put 'JHPDL:JH_EXAM_APY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAD0001', 'ED0.000.000.001'
put 'JHPDL:JH_EXAM_APY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAD0002', '1234_1'
put 'JHPDL:JH_EXAM_APY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAD0003', 'EA0.000.000.001'

/*
-- 样本:检查预约排期 JH_EXAM_APY_SCH
//*/
create 'JHPDL:JH_EXAM_APY_SCH', 'CF'

put 'JHPDL:JH_EXAM_APY_SCH', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAS0001', 'ES0.000.000.001'
put 'JHPDL:JH_EXAM_APY_SCH', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAS0002', '1234_1'
put 'JHPDL:JH_EXAM_APY_SCH', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:EAS0003', 'EA0.000.000.001'

/*
-- 样本:检查报告 JH_EXAM_REPORT
//*/
create 'JHPDL:JH_EXAM_REPORT', 'CF'

put 'JHPDL:JH_EXAM_REPORT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ERT0001', 'ER0.000.000.001'
put 'JHPDL:JH_EXAM_REPORT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ERT0002', '1234_1'
put 'JHPDL:JH_EXAM_REPORT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ERT0003', 'EA0.000.000.001'

/*
-- 样本:检查影像索引 JH_EXAM_REP_IMG
//*/
create 'JHPDL:JH_EXAM_REP_IMG', 'CF'

put 'JHPDL:JH_EXAM_REP_IMG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ERI0001', 'EP0.000.000.001'
put 'JHPDL:JH_EXAM_REP_IMG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ERI0002', '1234_1'
put 'JHPDL:JH_EXAM_REP_IMG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ERI0003', 'ER0.000.000.001'

/*
-- 样本:手术申请 JH_OP_APPLY
//*/
create 'JHPDL:JH_OP_APPLY', 'CF'

put 'JHPDL:JH_OP_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OAP0001', 'PA0.000.000.001'
put 'JHPDL:JH_OP_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OAP0002', '1234_1'
put 'JHPDL:JH_OP_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OAP0003', 'V00.000.001'

/*
-- 样本:手术预约排期 JH_OP_SCH
//*/
create 'JHPDL:JH_OP_SCH', 'CF'

put 'JHPDL:JH_OP_SCH', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OAS0001', 'PS0.000.000.001'
put 'JHPDL:JH_OP_SCH', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OAS0002', '1234_1'
put 'JHPDL:JH_OP_SCH', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OAS0003', 'PA0.000.000.001'

/*
-- 样本:输血申请 JH_BLD_APPLY
//*/
create 'JHPDL:JH_BLD_APPLY', 'CF'

put 'JHPDL:JH_BLD_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:BAP0001', 'BA0.000.000.001'
put 'JHPDL:JH_BLD_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:BAP0002', '1234_1'
put 'JHPDL:JH_BLD_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:BAP0003', 'V00.000.001'

/*
-- 样本:病理申请 JH_PHLY_APPLY
//*/
create 'JHPDL:JH_PHLY_APPLY', 'CF'

put 'JHPDL:JH_PHLY_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:HAP0001', 'HA0.000.000.001'
put 'JHPDL:JH_PHLY_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:HAP0002', '1234_1'
put 'JHPDL:JH_PHLY_APPLY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:HAP0003', 'V00.000.001'

/*
-- 样本:住院临床诊断记录 JH_IN_DIAG
//*/
create 'JHPDL:JH_IN_DIAG', 'CF'

put 'JHPDL:JH_IN_DIAG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IDR0001', 'ZG0.000.000.001'
put 'JHPDL:JH_IN_DIAG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IDR0002', '1234_1'
put 'JHPDL:JH_IN_DIAG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IDR0003', 'H00.000.001'

/*
-- 样本:住院临床手术记录 JH_IN_OPER
//*/
create 'JHPDL:JH_IN_OPER', 'CF'

put 'JHPDL:JH_IN_OPER', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IPR0001', 'ZP0.000.000.001'
put 'JHPDL:JH_IN_OPER', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IPR0002', '1234_1'
put 'JHPDL:JH_IN_OPER', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IPR0003', 'H00.000.001'

/*
-- 样本:住院病案首页 JH_FIRST_PAGE
//*/
create 'JHPDL:JH_FIRST_PAGE', 'CF'

put 'JHPDL:JH_FIRST_PAGE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFP0001', 'FR0.000.000.001'
put 'JHPDL:JH_FIRST_PAGE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFP0002', '1234_1'
put 'JHPDL:JH_FIRST_PAGE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFP0003', 'H00.000.001'

/*
-- 样本:首页编目诊断记录 JH_FP_DIAG
//*/
create 'JHPDL:JH_FP_DIAG', 'CF'

put 'JHPDL:JH_FP_DIAG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFD0001', 'FD0.000.000.001'
put 'JHPDL:JH_FP_DIAG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFD0002', '1234_1'
put 'JHPDL:JH_FP_DIAG', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFD0003', 'FR0.000.000.001'

/*
-- 样本:首页编目手术记录 JH_FP_OPER
//*/
create 'JHPDL:JH_FP_OPER', 'CF'

put 'JHPDL:JH_FP_OPER', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFR0001', 'FP0.000.000.001'
put 'JHPDL:JH_FP_OPER', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFR0002', '1234_1'
put 'JHPDL:JH_FP_OPER', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFR0003', 'FR0.000.000.001'

/*
-- 样本:病案首页附页 JH_FP_SUB
//*/
create 'JHPDL:JH_FP_SUB', 'CF'

put 'JHPDL:JH_FP_SUB', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFS0001', 'FS0.000.000.001'
put 'JHPDL:JH_FP_SUB', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFS0002', '1234_1'
put 'JHPDL:JH_FP_SUB', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IFS0003', 'FR0.000.000.001'

/*
-- 样本:住院电子病历记录 JH_IN_MR
//*/
create 'JHPDL:JH_IN_MR', 'CF'

put 'JHPDL:JH_IN_MR', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IMR0001', 'ZM0.000.000.001'
put 'JHPDL:JH_IN_MR', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IMR0002', '1234_1'
put 'JHPDL:JH_IN_MR', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IMR0003', 'H00.000.001'

/*
-- 样本:住院病历文书内容 JH_IN_MRFILE
//*/
create 'JHPDL:JH_IN_MRFILE', 'CF'

put 'JHPDL:JH_IN_MRFILE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IMF0001', 'ZF0.000.000.001'
put 'JHPDL:JH_IN_MRFILE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IMF0002', '1234_1'
put 'JHPDL:JH_IN_MRFILE', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IMF0003', 'ZM0.000.000.001'

/*
-- 样本:生命体征记录 JH_VITAL_SIGNS
//*/
create 'JHPDL:JH_VITAL_SIGNS', 'CF'

put 'JHPDL:JH_VITAL_SIGNS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IVS0001', 'ZV0.000.000.001'
put 'JHPDL:JH_VITAL_SIGNS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IVS0002', '1234_1'
put 'JHPDL:JH_VITAL_SIGNS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:IVS0003', 'H00.000.001'

/*
-- 样本:随访记录 JH_FOLLOW_UP
//*/
create 'JHPDL:JH_FOLLOW_UP', 'CF'

put 'JHPDL:JH_FOLLOW_UP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAF0001', 'PF0.000.000.001'
put 'JHPDL:JH_FOLLOW_UP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAF0002', '1234_1'
put 'JHPDL:JH_FOLLOW_UP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PAF0003', 'V00.000.001'

/*
-- 样本:费用账单 JH_FEE_BILL
//*/
create 'JHPDL:JH_FEE_BILL', 'CF'

put 'JHPDL:JH_FEE_BILL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFB0001', 'PB0.000.000.001'
put 'JHPDL:JH_FEE_BILL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFB0002', '1234_1'
put 'JHPDL:JH_FEE_BILL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFB0003', 'V00.000.001'

/*
-- 样本:费用账单明细 JH_FEE_BL_DTL
//*/
create 'JHPDL:JH_FEE_BL_DTL', 'CF'

put 'JHPDL:JH_FEE_BL_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFD0001', 'PD0.000.000.001'
put 'JHPDL:JH_FEE_BL_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFD0002', '1234_1'
put 'JHPDL:JH_FEE_BL_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFD0003', 'PB0.000.000.001'

/*
-- 样本:费用结算记录 JH_FEE_PAY
//*/
create 'JHPDL:JH_FEE_PAY', 'CF'

put 'JHPDL:JH_FEE_PAY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFP0001', 'PP0.000.000.001'
put 'JHPDL:JH_FEE_PAY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFP0002', '1234_1'
put 'JHPDL:JH_FEE_PAY', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFP0003', 'PB0.000.000.001'

/*
-- 样本:费用结算明细 JH_FEE_PAY_DTL
//*/
create 'JHPDL:JH_FEE_PAY_DTL', 'CF'

put 'JHPDL:JH_FEE_PAY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFL0001', 'PL0.000.000.001'
put 'JHPDL:JH_FEE_PAY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFL0002', '1234_1'
put 'JHPDL:JH_FEE_PAY_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:PFL0003', 'PP0.000.000.001'

/*
-- 样本:门诊就诊记录 JH_OUT_HOSP
//*/
create 'JHPDL:JH_OUT_HOSP', 'CF'

put 'JHPDL:JH_OUT_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OHR0001', 'M00.000.001'
put 'JHPDL:JH_OUT_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OHR0002', '1234_1'
put 'JHPDL:JH_OUT_HOSP', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OHR0003', 'V00.000.001'

/*
-- 样本:门诊电子病历记录 JH_OUT_MR
//*/
create 'JHPDL:JH_OUT_MR', 'CF'

put 'JHPDL:JH_OUT_MR', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OMR0001', 'MM0.000.000.001'
put 'JHPDL:JH_OUT_MR', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OMR0002', '1234_1'
put 'JHPDL:JH_OUT_MR', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OMR0003', 'MM0.000.000.001'

/*
-- 样本:门诊处方 JH_OUT_ORDERS
//*/
create 'JHPDL:JH_OUT_ORDERS', 'CF'

put 'JHPDL:JH_OUT_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OOR0001', 'MD0.000.000.001'
put 'JHPDL:JH_OUT_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OOR0002', '1234_1'
put 'JHPDL:JH_OUT_ORDERS', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OOR0003', 'MM0.000.000.001'

/*
-- 样本:门诊处方明细 JH_OUT_ORD_DTL
//*/
create 'JHPDL:JH_OUT_ORD_DTL', 'CF'

put 'JHPDL:JH_OUT_ORD_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OOD0001', 'ML0.000.000.001'
put 'JHPDL:JH_OUT_ORD_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OOD0002', '1234_1'
put 'JHPDL:JH_OUT_ORD_DTL', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OOD0003', 'MD0.000.000.001'

/*
-- 样本:门诊分诊 JH_OUT_SHUNT
//*/
create 'JHPDL:JH_OUT_SHUNT', 'CF'

put 'JHPDL:JH_OUT_SHUNT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OSR0001', 'MS0.000.000.001'
put 'JHPDL:JH_OUT_SHUNT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OSR0002', '1234_1'
put 'JHPDL:JH_OUT_SHUNT', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:OSR0003', 'MM0.000.000.001'

/*
-- 样本:门诊预约记录 JH_OUT_RVN
//*/
create 'JHPDL:JH_OUT_RVN', 'CF'

put 'JHPDL:JH_OUT_RVN', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ORR0001', 'MR0.000.000.001'
put 'JHPDL:JH_OUT_RVN', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ORR0002', '1234_1'
put 'JHPDL:JH_OUT_RVN', "\x00\x00\x00\x00\x00\x00\x00\x01", 'CF:ORR0003', 'MM0.000.000.001'



/*
修改 JHIDG 方面相关的表，从2020-01-17开始，不过有没有修改都把全部表的语句重新贴出
//*/

/*
-- 记录表治理的规则
//*/
CREATE TABLE IF NOT EXISTS `JH_RULE_TAB` (
  `TAB_RULE_ID` INTEGER NOT NULL AUTO_INCREMENT COMMENT '表治理规则唯一序号',
  `PDL_TAB_NAME` VARCHAR(100) NULL COMMENT '治理目标表名称；如“JHPDL:JH_PAT_INFO”',
  `DL_TAB_NAME` VARCHAR(100) NULL COMMENT '治理来源系统对应的表名；如“JHDL:JH_TABINFO_01”',
  `CUR_ROWKEY` BIGINT NULL COMMENT '当前治理到的Rowkey序号',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  
  PRIMARY KEY (`TAB_RULE_ID`),
  KEY `KEY_JH_RULE_TAB` (`TAB_RULE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='表的治理规则';
INSERT INTO `JH_RULE_TAB` (`TAB_RULE_ID`, `PDL_TAB_NAME`, `DL_TAB_NAME`, `CUR_ROWKEY`) VALUES
(1, 'JHPDL:JH_PAT_INFO', 'JHDL:03_PAT_MASTER_INDEX', 0),
(2, 'JHPDL:JH_IN_HOSP', 'JHDL:03_PAT_VISIT', 12);

/*
-- 记录字段治理规则
治理的方式详细说明：
1、内部编码，如“PAT0001”需要输入“PAT、P、二级”获得返回编码“P00.000.001”，扩展参数“PAT#,#P#,#2”
2、源表key复制，表示rowkey对应的源系统表key字段一比一复制
3、源表key合并，表示rowkey对应的源系统表key字段合并数据，扩展参数“2#,#_^_#,#patient_id#,#visit_id”
4、直接可以转换的字典类型，如“PAT0005”对应的字典在“JH_BAS_DT_M”的“MASTER_ID”为“1”，扩展参数“1”；增加字典映射表，映射“JH_BAS_DT_D”
5、JHPDL之间的KeyCode关联，单字段关联，如“03_PAT_VISIT”的“PATIENT_ID”对应“JH_PAT_INFO”的“PAT0002”
6、JHPDL特殊计算值，扩展参数“PAT”
7、源表非key复制，表示rowkey对应的源系统表非key字段一比一复制
8、源表非key合并，表示rowkey对应的源系统表非key字段合并数据，扩展参数“2#,#_^_#,#patient_id#,#visit_id”
9、辅助表复制，辅助表字段一比一复制
10、辅助表合并，辅助表字段合并数据，扩展参数“2#,#_^_#,#patient_id#,#visit_id”
11、需要拆分的字典类型，如原来的值为“Code#Value”等
12、JHPDL之间的KeyCode关联，多字段合并关联，如“03_PAT_VISIT”的“PATIENT_ID”和“PATIENT_ID”组成“JH_IN_HOSP”的“IHR0002”
 /*/
CREATE TABLE IF NOT EXISTS `JH_RULE_FLD` (
  `FLD_RULE_ID` INTEGER NOT NULL AUTO_INCREMENT COMMENT '字段治理规则唯一序号',
  `TAB_RULE_ID` INTEGER NOT NULL COMMENT '表治理规则唯一序号',
  `PDL_FLD_NAME` VARCHAR(50) NULL COMMENT '治理目标字段名称；如“PAT0001”',
  `DL_TAB_NAME` VARCHAR(100) NULL COMMENT '治理来源系统对应的表名；如“JHDL:JH_TABINFO_01”',
  `DL_FLD_NAME` VARCHAR(50) NULL COMMENT '治理来源对应的字段名；如“PATIENT_ID”',
  `RULE_TYPE_ID` SMALLINT NOT NULL COMMENT '治理的方式；1、内部编码 2、源表复制 3、源表合并',
  `RULE_TYPE_PARA` VARCHAR(200) NULL COMMENT '治理的方式扩展参数',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  
  PRIMARY KEY (`FLD_RULE_ID`),
  KEY `KEY_JH_RULE_FLD` (`FLD_RULE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录字段治理规则';

INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(1, 1, 'PAT0001', '', '', 1, 'PAT#,#P#,#2'),
(2, 1, 'PAT0002', 'JHDL:03_PAT_MASTER_INDEX', 'PATIENT_ID', 2, ''),
(3, 1, 'PAT0003', 'JHDL:03_PAT_MASTER_INDEX', 'PATIENT_NAME', 7, ''),
(4, 1, 'PAT0005', 'JHDL:03_PAT_MASTER_INDEX', 'SEX', 4, '1'),
(5, 2, 'IHR0001', '', '', 1, 'IHR#,#H#,#2'),
(6, 2, 'IHR0002', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 3, '2#,#_^_#,#PATIENT_ID#,#VISIT_ID'),
(7, 2, 'IHR0003', 'JHPDL:JH_PAT_INFO', 'PAT0001', 5, 'JH_INX_PAT#,#PAT0002#,#PATIENT_ID'),
(8, 2, 'IHR0004', 'JHDL:03_PAT_VISIT', 'VISIT_ID', 7, '');

(1, 1, 'PAT0001', '', '', 1, 'PAT#,#P#,#2'),
(2, 1, 'PAT0002', 'JHDL:03_PAT_MASTER_INDEX', 'PATIENT_ID', 2, ''),
(3, 1, 'PAT0003', 'JHDL:03_PAT_MASTER_INDEX', 'NAME', 7, ''),
(4, 1, 'PAT0005', 'JHDL:03_PAT_MASTER_INDEX', 'SEX', 4, '1'),
(5, 3, 'IHR0001', '', '', '1', 'IHR#,#H#,#2'),
(6, 3, 'IHR0002', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 3, '2#,#_^_#,#PATIENT_ID#,#VISIT_ID'),
(7, 3, 'IHR0003', 'JHPDL:JH_PAT_VISIT', 'PAV0001', 12, 'JH_INX_PAV_PAT#,#PAV0002#,#2#,#_^_#,#PATIENT_ID#,#VISIT_ID'),
(8, 3, 'IHR0004', 'JHDL:03_PAT_VISIT', 'VISIT_ID', 7, ''),
(9, 2, 'PAV0001', '', '', 1, 'PAV#,#V#,#2'),
(10, 2, 'PAV0002', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 3, '2#,#_^_#,#PATIENT_ID#,#VISIT_ID'),
(11, 2, 'PAV0003', 'JHPDL:JH_PAT_INFO', 'PAT0001', 5, 'JH_INX_PAT#,#PAT0002#,#PATIENT_ID');


/*
治理规则规则例子，仅供参考，不做执行脚本
-- 9、辅助表复制
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(3, 1, 'PAT0003', 'JHDL:03_PAT_MASTER_INDEX', 'PATIENT_NAME', 9, '');

-- 10、辅助表合并
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(6, 2, 'IHR0002', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 10, '2#,#_^_#,#PATIENT_ID#,#VISIT_ID');

-- 11、需要拆分的字典类型
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(1, 1, 'TST0001', 'JHDL:03_PA_PERSON', 'PAPER_STNAME', 11, '1#,#_#_#,#1');

-- 12、JHPDL之间的KeyCode关联，多字段合并关联
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(1, 1, 'TST0001', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 12, 'JH_INX_PAV_PAT#,#PAV0002#,#2#,#_^_#,#PATIENT_ID#,#VISIT_ID');

-- 13、常量值
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(1, 1, 'TST0001', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 10, '2#,#_^_#,#PATIENT_ID#,#VISIT_ID');

-- 14、JDHL之间一对一关联，关联表小数据量场景
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(1, 1, 'TST0001', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 10, '2#,#_^_#,#PATIENT_ID#,#VISIT_ID');

-- 15、JDHL之间一对一关联，关联表大数据量场景
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(1, 1, 'TST0001', 'JHDL:03_PAT_VISIT', 'PATIENT_ID', 10, '2#,#_^_#,#PATIENT_ID#,#VISIT_ID');

-- 16、单字段简单函数转换
INSERT INTO `JH_RULE_FLD` (`FLD_RULE_ID`, `TAB_RULE_ID`, `PDL_FLD_NAME`, `DL_TAB_NAME`, `DL_FLD_NAME`, `RULE_TYPE_ID`, `RULE_TYPE_PARA`) VALUES
(1, 1, 'TST0001', 'JHDL:03_PA_PERSON', 'PAPER_STNAME', 11, '1#,#_#_#,#1');
*/


/*
-- 纯净湖索引表创建配置表
-- 需要创建的纯净湖索引表配置主表
-- 2020-02-25 有修改数据内容
 /*/
CREATE TABLE IF NOT EXISTS `JH_INX_MASTER` (
  `INX_MASTER_ID` SMALLINT NOT NULL AUTO_INCREMENT COMMENT '唯一号，递增',
  `INX_TAB_NAME` VARCHAR(50) NOT NULL COMMENT '纯净湖索引表名，如“JH_INX_PAT”',
  `INX_SRC_HBASE` VARCHAR(100) NOT NULL COMMENT '条目来源表，如“JHPDL:JH_PAT_INFO”',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  
  PRIMARY KEY (`INX_MASTER_ID`),
  KEY `KEY_JH_INX_MASTER` (`INX_MASTER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='纯净湖索引表配置主表';
INSERT INTO `JH_INX_MASTER` (`INX_MASTER_ID`, `INX_TAB_NAME`, `INX_SRC_HBASE`) VALUES
(1, 'JH_INX_PAT', 'JHPDL:JH_PAT_INFO'),
(2, 'JH_INX_PAV_PAT', 'JHPDL:JH_PAT_VISIT'),
(3, 'JH_INX_IHR_PAV', 'JHPDL:JH_IN_HOSP'),
(4, 'JH_INX_IAR_IHR', 'JHPDL:JH_IN_ADT'),
(5, 'JH_INX_IOR_IHR', 'JHPDL:JH_IN_ORDERS'),
(6, 'JH_INX_LAP_PAV', 'JHPDL:JH_LAB_APPLY'),
(7, 'JH_INX_CHR_PAV', 'JHPDL:JH_CU_HOSP'),
(8, 'JH_INX_EHR_PAV', 'JHPDL:JH_EGY_HOSP'),
(9, 'JH_INX_SHR_PAV', 'JHPDL:JH_SL_HOSP'),
(10, 'JH_INX_LAD_LAP', 'JHPDL:JH_LAB_APY_DTL'),
(11, 'JH_INX_LTR_LAP', 'JHPDL:JH_LAB_TRACE'),
(12, 'JH_INX_LRS_LAP', 'JHPDL:JH_LAB_RESULT'),
(13, 'JH_INX_LRD_LRS', 'JHPDL:JH_LAB_RES_DTL'),
(14, 'JH_INX_LRV_LRS', 'JHPDL:JH_LAB_RES_VIS'),
(15, 'JH_INX_LRC_LRS', 'JHPDL:JH_LAB_RES_CTL'),
(16, 'JH_INX_EAP_PAV', 'JHPDL:JH_EXAM_APPLY'),
(17, 'JH_INX_EAD_EAP', 'JHPDL:JH_EXAM_APY_DTL'),
(18, 'JH_INX_EAS_EAP', 'JHPDL:JH_EXAM_APY_SCH'),
(19, 'JH_INX_ERT_EAP', 'JHPDL:JH_EXAM_REPORT'),
(20, 'JH_INX_ERI_ERT', 'JHPDL:JH_EXAM_REP_IMG'),
(21, 'JH_INX_OAP_PAV', 'JHPDL:JH_OP_APPLY'),
(22, 'JH_INX_OAS_OAP', 'JHPDL:JH_OP_SCH'),
(23, 'JH_INX_BAP_PAV', 'JHPDL:JH_BLD_APPLY'),
(24, 'JH_INX_HAP_PAV', 'JHPDL:JH_PHLY_APPLY'),
(25, 'JH_INX_IDR_IHR', 'JHPDL:JH_IN_DIAG'),
(26, 'JH_INX_IPR_IHR', 'JHPDL:JH_IN_OPER'),
(27, 'JH_INX_IFP_IHR', 'JHPDL:JH_FIRST_PAGE'),
(28, 'JH_INX_IFD_IFP', 'JHPDL:JH_FP_DIAG'),
(29, 'JH_INX_IFR_IFP', 'JHPDL:JH_FP_OPER'),
(30, 'JH_INX_IFS_IFP', 'JHPDL:JH_FP_SUB'),
(31, 'JH_INX_IMR_IHR', 'JHPDL:JH_IN_MR'),
(32, 'JH_INX_IMF_IMR', 'JHPDL:JH_IN_MRFILE'),
(33, 'JH_INX_IVS_IHR', 'JHPDL:JH_VITAL_SIGNS'),
(34, 'JH_INX_PAF_PAV', 'JHPDL:JH_FOLLOW_UP'),
(35, 'JH_INX_PFB_PAV', 'JHPDL:JH_FEE_BILL'),
(36, 'JH_INX_PFD_PFB', 'JHPDL:JH_FEE_BL_DTL'),
(37, 'JH_INX_PFP_PFB', 'JHPDL:JH_FEE_PAY'),
(38, 'JH_INX_PFL_PFP', 'JHPDL:JH_FEE_PAY_DTL'),
(39, 'JH_INX_OHR_PAV', 'JHPDL:JH_OUT_HOSP'),
(40, 'JH_INX_OMR_OHR', 'JHPDL:JH_OUT_MR'),
(41, 'JH_INX_OOR_OHR', 'JHPDL:JH_OUT_ORDERS'),
(42, 'JH_INX_OOD_OOR', 'JHPDL:JH_OUT_ORD_DTL'),
(43, 'JH_INX_OSR_OHR', 'JHPDL:JH_OUT_SHUNT'),
(44, 'JH_INX_ORR_OHR', 'JHPDL:JH_OUT_RVN');
/*
.........   数据内容调整
 /*/


/*
-- 纯净湖索引表创建配置表
-- 需要创建的纯净湖索引表配置详细表
-- 2020-02-25 有修改数据内容
 /*/
CREATE TABLE IF NOT EXISTS `JH_INX_DETAIL` (
  `INX_DETAIL_ID` SMALLINT NOT NULL AUTO_INCREMENT COMMENT '唯一号，递增',
  `INX_MASTER_ID` SMALLINT NOT NULL COMMENT '配置主表唯一号',
  `INX_FLD_NAME` VARCHAR(50) NOT NULL COMMENT '索引表字段名称，如“PAT0001”',
  `INX_FLD_TYPE` VARCHAR(50) NOT NULL COMMENT '索引表字段类型，如“SMALLINT”、“VARCHAR”等',
  `INX_FLD_LNK_TAB` VARCHAR(100) NOT NULL COMMENT '字段关联索引表名，如“JH_INX_PAT”，字段属于条目来源表的为空',
  `INX_FLD_LINK` VARCHAR(100) NULL COMMENT '关联关系，如“IHR0003#,#PAT0001”',
  `INX_FLD_ROWKEY` TINYINT NOT NULL COMMENT '字段来源是否hbase表的rowkey（0,1）',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  
  PRIMARY KEY (`INX_DETAIL_ID`),
  KEY `KEY_JH_INX_DETAIL` (`INX_DETAIL_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='纯净湖索引表配置详细表';
INSERT INTO `JH_INX_DETAIL` (`INX_DETAIL_ID`, `INX_MASTER_ID`, `INX_FLD_NAME`, `INX_FLD_TYPE`, `INX_FLD_LNK_TAB`, `INX_FLD_LINK`, `INX_FLD_ROWKEY`) VALUES
(1, 1, 'PAT_ROWKEY', 'BIGINT', '', '', 1),
(2, 1, 'PAT0001', 'VARCHAR', '', '', 0),
(3, 1, 'PAT0002', 'VARCHAR', '', '', 0),
(4, 2, 'PAV_ROWKEY', 'BIGINT', '', '', 1),
(5, 2, 'PAV0001', 'VARCHAR', '', '', 0),
(6, 2, 'PAV0002', 'VARCHAR', '', '', 0),
(7, 2, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAT', 'PAV0003#,#PAT0001', 0),
(8, 2, 'PAT0001', 'VARCHAR', 'JH_INX_PAT', 'PAV0003#,#PAT0001', 0),
(9, 2, 'PAT0002', 'VARCHAR', 'JH_INX_PAT', 'PAV0003#,#PAT0001', 0),
(10, 3, 'IHR_ROWKEY', 'BIGINT', '', '', 1),
(11, 3, 'IHR0001', 'VARCHAR', '', '', 0),
(12, 3, 'IHR0002', 'VARCHAR', '', '', 0),
(13, 3, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'IHR0003#,#PAV0001', 0),
(14, 3, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'IHR0003#,#PAV0001', 0),
(15, 3, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'IHR0003#,#PAV0001', 0),
(16, 3, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'IHR0003#,#PAV0001', 0),
(17, 3, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'IHR0003#,#PAV0001', 0),
(18, 3, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'IHR0003#,#PAV0001', 0),
(19, 4, 'IAR_ROWKEY', 'BIGINT', '', '', 1),
(20, 4, 'IAR0001', 'VARCHAR', '', '', 0),
(21, 4, 'IAR0002', 'VARCHAR', '', '', 0),
(22, 4, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(23, 4, 'IHR0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(24, 4, 'IHR0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(25, 4, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(26, 4, 'PAV0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(27, 4, 'PAV0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(28, 4, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(29, 4, 'PAT0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(30, 4, 'PAT0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IAR0003#,#IHR0001', 0),
(31, 5, 'IOR_ROWKEY', 'BIGINT', '', '', 1),
(32, 5, 'IOR0001', 'VARCHAR', '', '', 0),
(33, 5, 'IOR0002', 'VARCHAR', '', '', 0),
(34, 5, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(35, 5, 'IHR0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(36, 5, 'IHR0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(37, 5, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(38, 5, 'PAV0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(39, 5, 'PAV0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(40, 5, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(41, 5, 'PAT0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(42, 5, 'PAT0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IOR0003#,#IHR0001', 0),
(43, 6, 'LAP_ROWKEY', 'BIGINT', '', '', 1),
(44, 6, 'LAP0001', 'VARCHAR', '', '', 0),
(45, 6, 'LAP0002', 'VARCHAR', '', '', 0),
(46, 6, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'LAP0003#,#PAV0001', 0),
(47, 6, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'LAP0003#,#PAV0001', 0),
(48, 6, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'LAP0003#,#PAV0001', 0),
(49, 6, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'LAP0003#,#PAV0001', 0),
(50, 6, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'LAP0003#,#PAV0001', 0),
(51, 6, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'LAP0003#,#PAV0001', 0),
(52, 7, 'CHR_ROWKEY', 'BIGINT', '', '', 1),
(53, 7, 'CHR0001', 'VARCHAR', '', '', 0),
(54, 7, 'CHR0002', 'VARCHAR', '', '', 0),
(55, 7, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'CHR0003#,#PAV0001', 0),
(56, 7, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'CHR0003#,#PAV0001', 0),
(57, 7, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'CHR0003#,#PAV0001', 0),
(58, 7, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'CHR0003#,#PAV0001', 0),
(59, 7, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'CHR0003#,#PAV0001', 0),
(60, 7, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'CHR0003#,#PAV0001', 0),
(61, 8, 'EHR_ROWKEY', 'BIGINT', '', '', 1),
(62, 8, 'EHR0001', 'VARCHAR', '', '', 0),
(63, 8, 'EHR0002', 'VARCHAR', '', '', 0),
(64, 8, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'EHR0003#,#PAV0001', 0),
(65, 8, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'EHR0003#,#PAV0001', 0),
(66, 8, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'EHR0003#,#PAV0001', 0),
(67, 8, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'EHR0003#,#PAV0001', 0),
(68, 8, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'EHR0003#,#PAV0001', 0),
(69, 8, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'EHR0003#,#PAV0001', 0),
(70, 9, 'SHR_ROWKEY', 'BIGINT', '', '', 1),
(71, 9, 'SHR0001', 'VARCHAR', '', '', 0),
(72, 9, 'SHR0002', 'VARCHAR', '', '', 0),
(73, 9, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'SHR0003#,#PAV0001', 0),
(74, 9, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'SHR0003#,#PAV0001', 0),
(75, 9, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'SHR0003#,#PAV0001', 0),
(76, 9, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'SHR0003#,#PAV0001', 0),
(77, 9, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'SHR0003#,#PAV0001', 0),
(78, 9, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'SHR0003#,#PAV0001', 0),
(79, 10, 'LAD_ROWKEY', 'BIGINT', '', '', 1),
(80, 10, 'LAD0001', 'VARCHAR', '', '', 0),
(81, 10, 'LAD0002', 'VARCHAR', '', '', 0),
(82, 10, 'LAP_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(83, 10, 'LAP0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(84, 10, 'LAP0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(85, 10, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(86, 10, 'PAV0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(87, 10, 'PAV0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(88, 10, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(89, 10, 'PAT0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(90, 10, 'PAT0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LAD0003#,#LAP0001', 0),
(91, 11, 'LTR_ROWKEY', 'BIGINT', '', '', 1),
(92, 11, 'LTR0001', 'VARCHAR', '', '', 0),
(93, 11, 'LTR0002', 'VARCHAR', '', '', 0),
(94, 11, 'LAP_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(95, 11, 'LAP0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(96, 11, 'LAP0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(97, 11, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(98, 11, 'PAV0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(99, 11, 'PAV0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(100, 11, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(101, 11, 'PAT0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(102, 11, 'PAT0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LTR0003#,#LAP0001', 0),
(103, 12, 'LRS_ROWKEY', 'BIGINT', '', '', 1),
(104, 12, 'LRS0001', 'VARCHAR', '', '', 0),
(105, 12, 'LRS0002', 'VARCHAR', '', '', 0),
(106, 12, 'LAP_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(107, 12, 'LAP0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(108, 12, 'LAP0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(109, 12, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(110, 12, 'PAV0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(111, 12, 'PAV0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(112, 12, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(113, 12, 'PAT0001', 'VARCHAR', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(114, 12, 'PAT0002', 'VARCHAR', 'JH_INX_LAP_PAV', 'LRS0003#,#LAP0001', 0),
(115, 13, 'LRD_ROWKEY', 'BIGINT', '', '', 1),
(116, 13, 'LRD0001', 'VARCHAR', '', '', 0),
(117, 13, 'LRD0002', 'VARCHAR', '', '', 0),
(118, 13, 'LRS_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(119, 13, 'LRS0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(120, 13, 'LRS0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(121, 13, 'LAP_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(122, 13, 'LAP0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(123, 13, 'LAP0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(124, 13, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(125, 13, 'PAV0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(126, 13, 'PAV0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(127, 13, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(128, 13, 'PAT0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(129, 13, 'PAT0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRD0003#,#LRS0001', 0),
(130, 14, 'LRV_ROWKEY', 'BIGINT', '', '', 1),
(131, 14, 'LRV0001', 'VARCHAR', '', '', 0),
(132, 14, 'LRV0002', 'VARCHAR', '', '', 0),
(133, 14, 'LRS_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(134, 14, 'LRS0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(135, 14, 'LRS0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(136, 14, 'LAP_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(137, 14, 'LAP0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(138, 14, 'LAP0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(139, 14, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(140, 14, 'PAV0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(141, 14, 'PAV0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(142, 14, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(143, 14, 'PAT0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(144, 14, 'PAT0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRV0003#,#LRS0001', 0),
(145, 15, 'LRC_ROWKEY', 'BIGINT', '', '', 1),
(146, 15, 'LRC0001', 'VARCHAR', '', '', 0),
(147, 15, 'LRC0002', 'VARCHAR', '', '', 0),
(148, 15, 'LRS_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(149, 15, 'LRS0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(150, 15, 'LRS0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(151, 15, 'LAP_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(152, 15, 'LAP0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(153, 15, 'LAP0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(154, 15, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(155, 15, 'PAV0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(156, 15, 'PAV0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(157, 15, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(158, 15, 'PAT0001', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(159, 15, 'PAT0002', 'VARCHAR', 'JH_INX_LRS_LAP', 'LRC0003#,#LRS0001', 0),
(160, 16, 'EAP_ROWKEY', 'BIGINT', '', '', 1),
(161, 16, 'EAP0001', 'VARCHAR', '', '', 0),
(162, 16, 'EAP0002', 'VARCHAR', '', '', 0),
(163, 16, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'EAP0003#,#PAV0001', 0),
(164, 16, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'EAP0003#,#PAV0001', 0),
(165, 16, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'EAP0003#,#PAV0001', 0),
(166, 16, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'EAP0003#,#PAV0001', 0),
(167, 16, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'EAP0003#,#PAV0001', 0),
(168, 16, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'EAP0003#,#PAV0001', 0),
(169, 17, 'EAD_ROWKEY', 'BIGINT', '', '', 1),
(170, 17, 'EAD0001', 'VARCHAR', '', '', 0),
(171, 17, 'EAD0002', 'VARCHAR', '', '', 0),
(172, 17, 'EAP_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(173, 17, 'EAP0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(174, 17, 'EAP0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(175, 17, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(176, 17, 'PAV0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(177, 17, 'PAV0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(178, 17, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(179, 17, 'PAT0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(180, 17, 'PAT0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAD0003#,#EAP0001', 0),
(181, 18, 'EAS_ROWKEY', 'BIGINT', '', '', 1),
(182, 18, 'EAS0001', 'VARCHAR', '', '', 0),
(183, 18, 'EAS0002', 'VARCHAR', '', '', 0),
(184, 18, 'EAP_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(185, 18, 'EAP0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(186, 18, 'EAP0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(187, 18, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(188, 18, 'PAV0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(189, 18, 'PAV0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(190, 18, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(191, 18, 'PAT0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(192, 18, 'PAT0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'EAS0003#,#EAP0001', 0),
(193, 19, 'ERT_ROWKEY', 'BIGINT', '', '', 1),
(194, 19, 'ERT0001', 'VARCHAR', '', '', 0),
(195, 19, 'ERT0002', 'VARCHAR', '', '', 0),
(196, 19, 'EAP_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(197, 19, 'EAP0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(198, 19, 'EAP0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(199, 19, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(200, 19, 'PAV0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(201, 19, 'PAV0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(202, 19, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(203, 19, 'PAT0001', 'VARCHAR', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(204, 19, 'PAT0002', 'VARCHAR', 'JH_INX_EAP_PAV', 'ERT0003#,#EAP0001', 0),
(205, 20, 'ERI_ROWKEY', 'BIGINT', '', '', 1),
(206, 20, 'ERI0001', 'VARCHAR', '', '', 0),
(207, 20, 'ERI0002', 'VARCHAR', '', '', 0),
(208, 20, 'ERT_ROWKEY', 'BIGINT', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(209, 20, 'ERT0001', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(210, 20, 'ERT0002', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(211, 20, 'EAP_ROWKEY', 'BIGINT', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(212, 20, 'EAP0001', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(213, 20, 'EAP0002', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(214, 20, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(215, 20, 'PAV0001', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(216, 20, 'PAV0002', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(217, 20, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(218, 20, 'PAT0001', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(219, 20, 'PAT0002', 'VARCHAR', 'JH_INX_ERT_EAP', 'ERI0003#,#ERT0001', 0),
(220, 21, 'OAP_ROWKEY', 'BIGINT', '', '', 1),
(221, 21, 'OAP0001', 'VARCHAR', '', '', 0),
(222, 21, 'OAP0002', 'VARCHAR', '', '', 0),
(223, 21, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'OAP0003#,#PAV0001', 0),
(224, 21, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'OAP0003#,#PAV0001', 0),
(225, 21, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'OAP0003#,#PAV0001', 0),
(226, 21, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'OAP0003#,#PAV0001', 0),
(227, 21, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'OAP0003#,#PAV0001', 0),
(228, 21, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'OAP0003#,#PAV0001', 0),
(229, 22, 'OAS_ROWKEY', 'BIGINT', '', '', 1),
(230, 22, 'OAS0001', 'VARCHAR', '', '', 0),
(231, 22, 'OAS0002', 'VARCHAR', '', '', 0),
(232, 22, 'OAP_ROWKEY', 'BIGINT', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(233, 22, 'OAP0001', 'VARCHAR', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(234, 22, 'OAP0002', 'VARCHAR', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(235, 22, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(236, 22, 'PAV0001', 'VARCHAR', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(237, 22, 'PAV0002', 'VARCHAR', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(238, 22, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(239, 22, 'PAT0001', 'VARCHAR', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(240, 22, 'PAT0002', 'VARCHAR', 'JH_INX_OAP_PAV', 'OAS0003#,#OAP0001', 0),
(241, 23, 'BAP_ROWKEY', 'BIGINT', '', '', 1),
(242, 23, 'BAP0001', 'VARCHAR', '', '', 0),
(243, 23, 'BAP0002', 'VARCHAR', '', '', 0),
(244, 23, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'BAP0003#,#PAV0001', 0),
(245, 23, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'BAP0003#,#PAV0001', 0),
(246, 23, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'BAP0003#,#PAV0001', 0),
(247, 23, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'BAP0003#,#PAV0001', 0),
(248, 23, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'BAP0003#,#PAV0001', 0),
(249, 23, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'BAP0003#,#PAV0001', 0),
(250, 24, 'HAP_ROWKEY', 'BIGINT', '', '', 1),
(251, 24, 'HAP0001', 'VARCHAR', '', '', 0),
(252, 24, 'HAP0002', 'VARCHAR', '', '', 0),
(253, 24, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'HAP0003#,#PAV0001', 0),
(254, 24, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'HAP0003#,#PAV0001', 0),
(255, 24, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'HAP0003#,#PAV0001', 0),
(256, 24, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'HAP0003#,#PAV0001', 0),
(257, 24, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'HAP0003#,#PAV0001', 0),
(258, 24, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'HAP0003#,#PAV0001', 0),
(259, 25, 'IDR_ROWKEY', 'BIGINT', '', '', 1),
(260, 25, 'IDR0001', 'VARCHAR', '', '', 0),
(261, 25, 'IDR0002', 'VARCHAR', '', '', 0),
(262, 25, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(263, 25, 'IHR0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(264, 25, 'IHR0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(265, 25, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(266, 25, 'PAV0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(267, 25, 'PAV0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(268, 25, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(269, 25, 'PAT0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(270, 25, 'PAT0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IDR0003#,#IHR0001', 0),
(271, 26, 'IPR_ROWKEY', 'BIGINT', '', '', 1),
(272, 26, 'IPR0001', 'VARCHAR', '', '', 0),
(273, 26, 'IPR0002', 'VARCHAR', '', '', 0),
(274, 26, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(275, 26, 'IHR0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(276, 26, 'IHR0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(277, 26, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(278, 26, 'PAV0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(279, 26, 'PAV0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(280, 26, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(281, 26, 'PAT0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(282, 26, 'PAT0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IPR0003#,#IHR0001', 0),
(283, 27, 'IFP_ROWKEY', 'BIGINT', '', '', 1),
(284, 27, 'IFP0001', 'VARCHAR', '', '', 0),
(285, 27, 'IFP0002', 'VARCHAR', '', '', 0),
(286, 27, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(287, 27, 'IHR0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(288, 27, 'IHR0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(289, 27, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(290, 27, 'PAV0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(291, 27, 'PAV0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(292, 27, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(293, 27, 'PAT0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(294, 27, 'PAT0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IFP0003#,#IHR0001', 0),
(295, 28, 'IFD_ROWKEY', 'BIGINT', '', '', 1),
(296, 28, 'IFD0001', 'VARCHAR', '', '', 0),
(297, 28, 'IFD0002', 'VARCHAR', '', '', 0),
(298, 28, 'IFP_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(299, 28, 'IFP0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(300, 28, 'IFP0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(301, 28, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(302, 28, 'IHR0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(303, 28, 'IHR0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(304, 28, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(305, 28, 'PAV0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(306, 28, 'PAV0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(307, 28, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(308, 28, 'PAT0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(309, 28, 'PAT0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFD0003#,#IFP0001', 0),
(310, 29, 'IFR_ROWKEY', 'BIGINT', '', '', 1),
(311, 29, 'IFR0001', 'VARCHAR', '', '', 0),
(312, 29, 'IFR0002', 'VARCHAR', '', '', 0),
(313, 29, 'IFP_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(314, 29, 'IFP0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(315, 29, 'IFP0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(316, 29, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(317, 29, 'IHR0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(318, 29, 'IHR0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(319, 29, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(320, 29, 'PAV0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(321, 29, 'PAV0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(322, 29, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(323, 29, 'PAT0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(324, 29, 'PAT0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFR0003#,#IFP0001', 0),
(325, 30, 'IFS_ROWKEY', 'BIGINT', '', '', 1),
(326, 30, 'IFS0001', 'VARCHAR', '', '', 0),
(327, 30, 'IFS0002', 'VARCHAR', '', '', 0),
(328, 30, 'IFP_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(329, 30, 'IFP0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(330, 30, 'IFP0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(331, 30, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(332, 30, 'IHR0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(333, 30, 'IHR0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(334, 30, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(335, 30, 'PAV0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(336, 30, 'PAV0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(337, 30, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(338, 30, 'PAT0001', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(339, 30, 'PAT0002', 'VARCHAR', 'JH_INX_IFP_IHR', 'IFS0003#,#IFP0001', 0),
(340, 31, 'IMR_ROWKEY', 'BIGINT', '', '', 1),
(341, 31, 'IMR0001', 'VARCHAR', '', '', 0),
(342, 31, 'IMR0002', 'VARCHAR', '', '', 0),
(343, 31, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(344, 31, 'IHR0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(345, 31, 'IHR0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(346, 31, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(347, 31, 'PAV0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(348, 31, 'PAV0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(349, 31, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(350, 31, 'PAT0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(351, 31, 'PAT0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IMR0003#,#IHR0001', 0),
(352, 32, 'IMF_ROWKEY', 'BIGINT', '', '', 1),
(353, 32, 'IMF0001', 'VARCHAR', '', '', 0),
(354, 32, 'IMF0002', 'VARCHAR', '', '', 0),
(355, 32, 'IMR_ROWKEY', 'BIGINT', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(356, 32, 'IMR0001', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(357, 32, 'IMR0002', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(358, 32, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(359, 32, 'IHR0001', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(360, 32, 'IHR0002', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(361, 32, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(362, 32, 'PAV0001', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(363, 32, 'PAV0002', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(364, 32, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(365, 32, 'PAT0001', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(366, 32, 'PAT0002', 'VARCHAR', 'JH_INX_IMR_IHR', 'IMF0003#,#IMR0001', 0),
(367, 33, 'IVS_ROWKEY', 'BIGINT', '', '', 1),
(368, 33, 'IVS0001', 'VARCHAR', '', '', 0),
(369, 33, 'IVS0002', 'VARCHAR', '', '', 0),
(370, 33, 'IHR_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(371, 33, 'IHR0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(372, 33, 'IHR0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(373, 33, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(374, 33, 'PAV0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(375, 33, 'PAV0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(376, 33, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(377, 33, 'PAT0001', 'VARCHAR', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(378, 33, 'PAT0002', 'VARCHAR', 'JH_INX_IHR_PAV', 'IVS0003#,#IHR0001', 0),
(379, 34, 'PAF_ROWKEY', 'BIGINT', '', '', 1),
(380, 34, 'PAF0001', 'VARCHAR', '', '', 0),
(381, 34, 'PAF0002', 'VARCHAR', '', '', 0),
(382, 34, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'PAF0003#,#PAV0001', 0),
(383, 34, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'PAF0003#,#PAV0001', 0),
(384, 34, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'PAF0003#,#PAV0001', 0),
(385, 34, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'PAF0003#,#PAV0001', 0),
(386, 34, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'PAF0003#,#PAV0001', 0),
(387, 34, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'PAF0003#,#PAV0001', 0),
(388, 35, 'PFB_ROWKEY', 'BIGINT', '', '', 1),
(389, 35, 'PFB0001', 'VARCHAR', '', '', 0),
(390, 35, 'PFB0002', 'VARCHAR', '', '', 0),
(391, 35, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'PFB0003#,#PAV0001', 0),
(392, 35, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'PFB0003#,#PAV0001', 0),
(393, 35, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'PFB0003#,#PAV0001', 0),
(394, 35, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'PFB0003#,#PAV0001', 0),
(395, 35, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'PFB0003#,#PAV0001', 0),
(396, 35, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'PFB0003#,#PAV0001', 0),
(397, 36, 'PFD_ROWKEY', 'BIGINT', '', '', 1),
(398, 36, 'PFD0001', 'VARCHAR', '', '', 0),
(399, 36, 'PFD0002', 'VARCHAR', '', '', 0),
(400, 36, 'PFB_ROWKEY', 'BIGINT', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(401, 36, 'PFB0001', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(402, 36, 'PFB0002', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(403, 36, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(404, 36, 'PAV0001', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(405, 36, 'PAV0002', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(406, 36, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(407, 36, 'PAT0001', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(408, 36, 'PAT0002', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFD0003#,#PFB0001', 0),
(409, 37, 'PFP_ROWKEY', 'BIGINT', '', '', 1),
(410, 37, 'PFP0001', 'VARCHAR', '', '', 0),
(411, 37, 'PFP0002', 'VARCHAR', '', '', 0),
(412, 37, 'PFB_ROWKEY', 'BIGINT', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(413, 37, 'PFB0001', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(414, 37, 'PFB0002', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(415, 37, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(416, 37, 'PAV0001', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(417, 37, 'PAV0002', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(418, 37, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(419, 37, 'PAT0001', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(420, 37, 'PAT0002', 'VARCHAR', 'JH_INX_PFB_PAV', 'PFP0003#,#PFB0001', 0),
(421, 38, 'PFL_ROWKEY', 'BIGINT', '', '', 1),
(422, 38, 'PFL0001', 'VARCHAR', '', '', 0),
(423, 38, 'PFL0002', 'VARCHAR', '', '', 0),
(424, 38, 'PFP_ROWKEY', 'BIGINT', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(425, 38, 'PFP0001', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(426, 38, 'PFP0002', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(427, 38, 'PFB_ROWKEY', 'BIGINT', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(428, 38, 'PFB0001', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(429, 38, 'PFB0002', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(430, 38, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(431, 38, 'PAV0001', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(432, 38, 'PAV0002', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(433, 38, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(434, 38, 'PAT0001', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(435, 38, 'PAT0002', 'VARCHAR', 'JH_INX_PFP_PFB', 'PFL0003#,#PFP0001', 0),
(436, 39, 'OHR_ROWKEY', 'BIGINT', '', '', 1),
(437, 39, 'OHR0001', 'VARCHAR', '', '', 0),
(438, 39, 'OHR0002', 'VARCHAR', '', '', 0),
(439, 39, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'OHR0003#,#PAV0001', 0),
(440, 39, 'PAV0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'OHR0003#,#PAV0001', 0),
(441, 39, 'PAV0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'OHR0003#,#PAV0001', 0),
(442, 39, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_PAV_PAT', 'OHR0003#,#PAV0001', 0),
(443, 39, 'PAT0001', 'VARCHAR', 'JH_INX_PAV_PAT', 'OHR0003#,#PAV0001', 0),
(444, 39, 'PAT0002', 'VARCHAR', 'JH_INX_PAV_PAT', 'OHR0003#,#PAV0001', 0),
(445, 40, 'OMR_ROWKEY', 'BIGINT', '', '', 1),
(446, 40, 'OMR0001', 'VARCHAR', '', '', 0),
(447, 40, 'OMR0002', 'VARCHAR', '', '', 0),
(448, 40, 'OHR_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(449, 40, 'OHR0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(450, 40, 'OHR0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(451, 40, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(452, 40, 'PAV0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(453, 40, 'PAV0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(454, 40, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(455, 40, 'PAT0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(456, 40, 'PAT0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OMR0003#,#OHR0001', 0),
(457, 41, 'OOR_ROWKEY', 'BIGINT', '', '', 1),
(458, 41, 'OOR0001', 'VARCHAR', '', '', 0),
(459, 41, 'OOR0002', 'VARCHAR', '', '', 0),
(460, 41, 'OHR_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(461, 41, 'OHR0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(462, 41, 'OHR0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(463, 41, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(464, 41, 'PAV0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(465, 41, 'PAV0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(466, 41, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(467, 41, 'PAT0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(468, 41, 'PAT0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OOR0003#,#OHR0001', 0),
(469, 42, 'OOD_ROWKEY', 'BIGINT', '', '', 1),
(470, 42, 'OOD0001', 'VARCHAR', '', '', 0),
(471, 42, 'OOD0002', 'VARCHAR', '', '', 0),
(472, 42, 'OOR_ROWKEY', 'BIGINT', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(473, 42, 'OOR0001', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(474, 42, 'OOR0002', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(475, 42, 'OHR_ROWKEY', 'BIGINT', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(476, 42, 'OHR0001', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(477, 42, 'OHR0002', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(478, 42, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(479, 42, 'PAV0001', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(480, 42, 'PAV0002', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(481, 42, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(482, 42, 'PAT0001', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(483, 42, 'PAT0002', 'VARCHAR', 'JH_INX_OOR_OHR', 'OOD0003#,#OOR0001', 0),
(484, 43, 'OSR_ROWKEY', 'BIGINT', '', '', 1),
(485, 43, 'OSR0001', 'VARCHAR', '', '', 0),
(486, 43, 'OSR0002', 'VARCHAR', '', '', 0),
(487, 43, 'OHR_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(488, 43, 'OHR0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(489, 43, 'OHR0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(490, 43, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(491, 43, 'PAV0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(492, 43, 'PAV0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(493, 43, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(494, 43, 'PAT0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(495, 43, 'PAT0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'OSR0003#,#OHR0001', 0),
(496, 44, 'ORR_ROWKEY', 'BIGINT', '', '', 1),
(497, 44, 'ORR0001', 'VARCHAR', '', '', 0),
(498, 44, 'ORR0002', 'VARCHAR', '', '', 0),
(499, 44, 'OHR_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(500, 44, 'OHR0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(501, 44, 'OHR0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(502, 44, 'PAV_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(503, 44, 'PAV0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(504, 44, 'PAV0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(505, 44, 'PAT_ROWKEY', 'BIGINT', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(506, 44, 'PAT0001', 'VARCHAR', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0),
(507, 44, 'PAT0002', 'VARCHAR', 'JH_INX_OHR_PAV', 'ORR0003#,#OHR0001', 0);

/*
.........   非ROWKEY、0001、0002字段索引表配置详细在这里配置，以10001开始填写序号
//*/
INSERT INTO `JH_INX_DETAIL` (`INX_DETAIL_ID`, `INX_MASTER_ID`, `INX_FLD_NAME`, `INX_FLD_TYPE`, `INX_FLD_LNK_TAB`, `INX_FLD_LINK`, `INX_FLD_ROWKEY`) VALUES
(10001, 1, 'PAT0003', 'VARCHAR', '', '', 0),
(10002, 2, 'PAV0004', 'VARCHAR', '', '', 0),
(10003, 2, 'PAV0005', 'VARCHAR', '', '', 0),
(10004, 3, 'IHR0006', 'VARCHAR', '', '', 0),
(10005, 3, 'IHR0011', 'VARCHAR', '', '', 0);

/*
.........   数据内容未完整
//*/

/*
......... 需要删除原来的表“JH_INX_IHR_PAT”和“JH_INX_IAR_IHR”重新按照新模式建立索引表
//*/

/*
-- 索引表，患者基本信息
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_PAT` (
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT0003` VARCHAR(50) NULL COMMENT '患者姓名；PATIENT_NAME',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  
  PRIMARY KEY (`PAT_ROWKEY`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='患者基本信息的索引表';


/*
-- 索引表，通用就诊
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_PAV_PAT` (
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '患者基本表，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV0004` VARCHAR(20) NULL COMMENT '就诊开始时间；VISIT_TIME',
  `PAV0005` VARCHAR(20) NULL COMMENT '就诊类型；VISIT_TYPE',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`PAV_ROWKEY`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`),
  KEY `IDX_PAV0004` (`PAV0004`),
  KEY `IDX_PAV0005` (`PAV0005`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='患者基本与通用就诊次关联索引表';



/*
-- 索引表，住院就诊
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IHR_PAV` (
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '通用就诊次表，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '患者基本表，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR0006` VARCHAR(20) NULL COMMENT '入院时间；ADMISSION_TIME',
  `IHR0011` VARCHAR(20) NULL COMMENT '出院时间；DISCHARGE_TIME',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IHR_ROWKEY`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`),
  KEY `IDX_IHR0006` (`IHR0006`),
  KEY `IDX_IHR0011` (`IHR0011`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='住院就诊与通用就诊次关联索引表';


/*
-- 索引表，入出转记录
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IAR_IHR` (
  `IAR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IAR0001` VARCHAR(20) NULL COMMENT '入出转编号；IN_ADT_SN',
  `IAR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IAR_ROWKEY`),
  KEY `KEY_JH_INX_IAR_IHR` (`IAR_ROWKEY`),
  KEY `IDX_IAR0001` (`IAR0001`),
  KEY `IDX_IAR0002` (`IAR0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='入出转记录与住院就诊关联索引表';

/*
-- 索引表，住院医嘱
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IOR_IHR` (
  `IOR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IOR0001` VARCHAR(20) NULL COMMENT '医嘱项编号；IN_ORDER_SN',
  `IOR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IOR_ROWKEY`),
  KEY `KEY_JH_INX_IOR_IHR` (`IOR_ROWKEY`),
  KEY `IDX_IOR0001` (`IOR0001`),
  KEY `IDX_IOR0002` (`IOR0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='住院医嘱与住院就诊关联索引表';

/*
-- 索引表，检验申请
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_LAP_PAV` (
  `LAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAP0001` VARCHAR(20) NULL COMMENT '检验申请单编号；LAB_APPLY_SN',
  `LAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`LAP_ROWKEY`),
  KEY `KEY_JH_INX_LAP_PAV` (`LAP_ROWKEY`),
  KEY `IDX_LAP0001` (`LAP0001`),
  KEY `IDX_LAP0002` (`LAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检验申请与通用就诊次关联索引表';

/*
-- 索引表，体检
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_CHR_PAV` (
  `CHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `CHR0001` VARCHAR(20) NULL COMMENT '体检就诊编号；CHECK_UP_SN',
  `CHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`CHR_ROWKEY`),
  KEY `KEY_JH_INX_CHR_PAV` (`CHR_ROWKEY`),
  KEY `IDX_CHR0001` (`CHR0001`),
  KEY `IDX_CHR0002` (`CHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='体检与通用就诊次关联索引表';

/*
-- 索引表，急诊
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_EHR_PAV` (
  `EHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EHR0001` VARCHAR(20) NULL COMMENT '急诊就诊编号；EMERGENCY_SN',
  `EHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`EHR_ROWKEY`),
  KEY `KEY_JH_INX_EHR_PAV` (`EHR_ROWKEY`),
  KEY `IDX_EHR0001` (`EHR0001`),
  KEY `IDX_EHR0002` (`EHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='急诊与通用就诊次关联索引表';

/*
-- 索引表，专科就诊
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_SHR_PAV` (
  `SHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `SHR0001` VARCHAR(20) NULL COMMENT '专科就诊编号；SPECIALIST_SN',
  `SHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`SHR_ROWKEY`),
  KEY `KEY_JH_INX_SHR_PAV` (`SHR_ROWKEY`),
  KEY `IDX_SHR0001` (`SHR0001`),
  KEY `IDX_SHR0002` (`SHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='专科就诊与通用就诊次关联索引表';

/*
-- 索引表，检验申请明细
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_LAD_LAP` (
  `LAD_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAD0001` VARCHAR(20) NULL COMMENT '检验申请明细编号；LAB_APY_DTL_SN',
  `LAD0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAP0001` VARCHAR(20) NULL COMMENT '检验申请单编号；LAB_APPLY_SN',
  `LAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`LAD_ROWKEY`),
  KEY `KEY_JH_INX_LAD_LAP` (`LAD_ROWKEY`),
  KEY `IDX_LAD0001` (`LAD0001`),
  KEY `IDX_LAD0002` (`LAD0002`),
  KEY `KEY_JH_INX_LAP_PAV` (`LAP_ROWKEY`),
  KEY `IDX_LAP0001` (`LAP0001`),
  KEY `IDX_LAP0002` (`LAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检验申请明细与检验申请关联索引表';

/*
-- 索引表，检验标本跟踪
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_LTR_LAP` (
  `LTR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LTR0001` VARCHAR(20) NULL COMMENT '检验标本追踪编号；LAB_TRACE_SN',
  `LTR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAP0001` VARCHAR(20) NULL COMMENT '检验申请单编号；LAB_APPLY_SN',
  `LAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`LTR_ROWKEY`),
  KEY `KEY_JH_INX_LTR_LAP` (`LTR_ROWKEY`),
  KEY `IDX_LTR0001` (`LTR0001`),
  KEY `IDX_LTR0002` (`LTR0002`),
  KEY `KEY_JH_INX_LAP_PAV` (`LAP_ROWKEY`),
  KEY `IDX_LAP0001` (`LAP0001`),
  KEY `IDX_LAP0002` (`LAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检验标本跟踪与检验申请关联索引表';

/*
-- 索引表，检验结果
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_LRS_LAP` (
  `LRS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LRS0001` VARCHAR(20) NULL COMMENT '检验结果编号；LAB_RESULT_SN',
  `LRS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAP0001` VARCHAR(20) NULL COMMENT '检验申请单编号；LAB_APPLY_SN',
  `LAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`LRS_ROWKEY`),
  KEY `KEY_JH_INX_LRS_LAP` (`LRS_ROWKEY`),
  KEY `IDX_LRS0001` (`LRS0001`),
  KEY `IDX_LRS0002` (`LRS0002`),
  KEY `KEY_JH_INX_LAP_PAV` (`LAP_ROWKEY`),
  KEY `IDX_LAP0001` (`LAP0001`),
  KEY `IDX_LAP0002` (`LAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检验结果与检验申请关联索引表';

/*
-- 索引表，检验结果明细
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_LRD_LRS` (
  `LRD_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LRD0001` VARCHAR(20) NULL COMMENT '检验结果明细编号；LAB_RES_DTL_SN',
  `LRD0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LRS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LRS0001` VARCHAR(20) NULL COMMENT '检验结果编号；LAB_RESULT_SN',
  `LRS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAP0001` VARCHAR(20) NULL COMMENT '检验申请单编号；LAB_APPLY_SN',
  `LAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`LRD_ROWKEY`),
  KEY `KEY_JH_INX_LRD_LRS` (`LRD_ROWKEY`),
  KEY `IDX_LRD0001` (`LRD0001`),
  KEY `IDX_LRD0002` (`LRD0002`),
  KEY `KEY_JH_INX_LRS_LAP` (`LRS_ROWKEY`),
  KEY `IDX_LRS0001` (`LRS0001`),
  KEY `IDX_LRS0002` (`LRS0002`),
  KEY `KEY_JH_INX_LAP_PAV` (`LAP_ROWKEY`),
  KEY `IDX_LAP0001` (`LAP0001`),
  KEY `IDX_LAP0002` (`LAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检验结果明细与检验结果关联索引表';

/*
-- 索引表，细菌培养结果
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_LRV_LRS` (
  `LRV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LRV0001` VARCHAR(20) NULL COMMENT '细菌培养结果编号；LAB_RES_VIS_SN',
  `LRV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LRS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LRS0001` VARCHAR(20) NULL COMMENT '检验结果编号；LAB_RESULT_SN',
  `LRS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAP0001` VARCHAR(20) NULL COMMENT '检验申请单编号；LAB_APPLY_SN',
  `LAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`LRV_ROWKEY`),
  KEY `KEY_JH_INX_LRV_LRS` (`LRV_ROWKEY`),
  KEY `IDX_LRV0001` (`LRV0001`),
  KEY `IDX_LRV0002` (`LRV0002`),
  KEY `KEY_JH_INX_LRS_LAP` (`LRS_ROWKEY`),
  KEY `IDX_LRS0001` (`LRS0001`),
  KEY `IDX_LRS0002` (`LRS0002`),
  KEY `KEY_JH_INX_LAP_PAV` (`LAP_ROWKEY`),
  KEY `IDX_LAP0001` (`LAP0001`),
  KEY `IDX_LAP0002` (`LAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='细菌培养结果与检验结果关联索引表';

/*
-- 索引表，检验危急值
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_LRC_LRS` (
  `LRC_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LRC0001` VARCHAR(20) NULL COMMENT '检验危急值编号；LAB_RES_CTL_SN',
  `LRC0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LRS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LRS0001` VARCHAR(20) NULL COMMENT '检验结果编号；LAB_RESULT_SN',
  `LRS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `LAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `LAP0001` VARCHAR(20) NULL COMMENT '检验申请单编号；LAB_APPLY_SN',
  `LAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`LRC_ROWKEY`),
  KEY `KEY_JH_INX_LRC_LRS` (`LRC_ROWKEY`),
  KEY `IDX_LRC0001` (`LRC0001`),
  KEY `IDX_LRC0002` (`LRC0002`),
  KEY `KEY_JH_INX_LRS_LAP` (`LRS_ROWKEY`),
  KEY `IDX_LRS0001` (`LRS0001`),
  KEY `IDX_LRS0002` (`LRS0002`),
  KEY `KEY_JH_INX_LAP_PAV` (`LAP_ROWKEY`),
  KEY `IDX_LAP0001` (`LAP0001`),
  KEY `IDX_LAP0002` (`LAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检验危急值与检验结果关联索引表';

/*
-- 索引表，检查申请
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_EAP_PAV` (
  `EAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EAP0001` VARCHAR(20) NULL COMMENT '检查申请单编号；EXAM_APPLY_SN',
  `EAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`EAP_ROWKEY`),
  KEY `KEY_JH_INX_EAP_PAV` (`EAP_ROWKEY`),
  KEY `IDX_EAP0001` (`EAP0001`),
  KEY `IDX_EAP0002` (`EAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检查申请与通用就诊次关联索引表';

/*
-- 索引表，检查申请明细
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_EAD_EAP` (
  `EAD_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EAD0001` VARCHAR(20) NULL COMMENT '检查申请明细编号；EXAM_APY_DTL_SN',
  `EAD0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `EAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EAP0001` VARCHAR(20) NULL COMMENT '检查申请单编号；EXAM_APPLY_SN',
  `EAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`EAD_ROWKEY`),
  KEY `KEY_JH_INX_EAD_EAP` (`EAD_ROWKEY`),
  KEY `IDX_EAD0001` (`EAD0001`),
  KEY `IDX_EAD0002` (`EAD0002`),
  KEY `KEY_JH_INX_EAP_PAV` (`EAP_ROWKEY`),
  KEY `IDX_EAP0001` (`EAP0001`),
  KEY `IDX_EAP0002` (`EAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检查申请明细与检查申请关联索引表';

/*
-- 索引表，检查预约排期
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_EAS_EAP` (
  `EAS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EAS0001` VARCHAR(20) NULL COMMENT '检查排期编号；EXAM_APY_SCH_SN',
  `EAS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `EAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EAP0001` VARCHAR(20) NULL COMMENT '检查申请单编号；EXAM_APPLY_SN',
  `EAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`EAS_ROWKEY`),
  KEY `KEY_JH_INX_EAS_EAP` (`EAS_ROWKEY`),
  KEY `IDX_EAS0001` (`EAS0001`),
  KEY `IDX_EAS0002` (`EAS0002`),
  KEY `KEY_JH_INX_EAP_PAV` (`EAP_ROWKEY`),
  KEY `IDX_EAP0001` (`EAP0001`),
  KEY `IDX_EAP0002` (`EAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检查预约排期与检查申请关联索引表';

/*
-- 索引表，检查报告
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_ERT_EAP` (
  `ERT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `ERT0001` VARCHAR(20) NULL COMMENT '检查报告编号；EXAM_REPORT_SN',
  `ERT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `EAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EAP0001` VARCHAR(20) NULL COMMENT '检查申请单编号；EXAM_APPLY_SN',
  `EAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ERT_ROWKEY`),
  KEY `KEY_JH_INX_ERT_EAP` (`ERT_ROWKEY`),
  KEY `IDX_ERT0001` (`ERT0001`),
  KEY `IDX_ERT0002` (`ERT0002`),
  KEY `KEY_JH_INX_EAP_PAV` (`EAP_ROWKEY`),
  KEY `IDX_EAP0001` (`EAP0001`),
  KEY `IDX_EAP0002` (`EAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检查报告与检查申请关联索引表';

/*
-- 索引表，检查影像索引
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_ERI_ERT` (
  `ERI_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `ERI0001` VARCHAR(20) NULL COMMENT '检查影像编号；EXAM_REP_IMG_SN',
  `ERI0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `ERT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `ERT0001` VARCHAR(20) NULL COMMENT '检查报告编号；EXAM_REPORT_SN',
  `ERT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `EAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `EAP0001` VARCHAR(20) NULL COMMENT '检查申请单编号；EXAM_APPLY_SN',
  `EAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ERI_ROWKEY`),
  KEY `KEY_JH_INX_ERI_ERT` (`ERI_ROWKEY`),
  KEY `IDX_ERI0001` (`ERI0001`),
  KEY `IDX_ERI0002` (`ERI0002`),
  KEY `KEY_JH_INX_ERT_EAP` (`ERT_ROWKEY`),
  KEY `IDX_ERT0001` (`ERT0001`),
  KEY `IDX_ERT0002` (`ERT0002`),
  KEY `KEY_JH_INX_EAP_PAV` (`EAP_ROWKEY`),
  KEY `IDX_EAP0001` (`EAP0001`),
  KEY `IDX_EAP0002` (`EAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检查影像索引与检查报告关联索引表';

/*
-- 索引表，手术申请
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_OAP_PAV` (
  `OAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OAP0001` VARCHAR(20) NULL COMMENT '手术申请单编号；OPER_APPLY_SN',
  `OAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`OAP_ROWKEY`),
  KEY `KEY_JH_INX_OAP_PAV` (`OAP_ROWKEY`),
  KEY `IDX_OAP0001` (`OAP0001`),
  KEY `IDX_OAP0002` (`OAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='手术申请与通用就诊次关联索引表';

/*
-- 索引表，手术预约排期
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_OAS_OAP` (
  `OAS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OAS0001` VARCHAR(20) NULL COMMENT '手术排期编号；OPER_APY_SCH_SN',
  `OAS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `OAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OAP0001` VARCHAR(20) NULL COMMENT '手术申请单编号；OPER_APPLY_SN',
  `OAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`OAS_ROWKEY`),
  KEY `KEY_JH_INX_OAS_OAP` (`OAS_ROWKEY`),
  KEY `IDX_OAS0001` (`OAS0001`),
  KEY `IDX_OAS0002` (`OAS0002`),
  KEY `KEY_JH_INX_OAP_PAV` (`OAP_ROWKEY`),
  KEY `IDX_OAP0001` (`OAP0001`),
  KEY `IDX_OAP0002` (`OAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='手术预约排期与手术申请关联索引表';

/*
-- 索引表，输血申请
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_BAP_PAV` (
  `BAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `BAP0001` VARCHAR(20) NULL COMMENT '输血申请单编号；BLD_APPLY_SN',
  `BAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`BAP_ROWKEY`),
  KEY `KEY_JH_INX_BAP_PAV` (`BAP_ROWKEY`),
  KEY `IDX_BAP0001` (`BAP0001`),
  KEY `IDX_BAP0002` (`BAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='输血申请与通用就诊次关联索引表';

/*
-- 索引表，病理申请
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_HAP_PAV` (
  `HAP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `HAP0001` VARCHAR(20) NULL COMMENT '病理申请单编号；PHLY_APPLY_SN',
  `HAP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`HAP_ROWKEY`),
  KEY `KEY_JH_INX_HAP_PAV` (`HAP_ROWKEY`),
  KEY `IDX_HAP0001` (`HAP0001`),
  KEY `IDX_HAP0002` (`HAP0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='病理申请与通用就诊次关联索引表';

/*
-- 索引表，住院临床诊断
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IDR_IHR` (
  `IDR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IDR0001` VARCHAR(20) NULL COMMENT '住院临床诊断编号；IN_DIAG_SN',
  `IDR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IDR_ROWKEY`),
  KEY `KEY_JH_INX_IDR_IHR` (`IDR_ROWKEY`),
  KEY `IDX_IDR0001` (`IDR0001`),
  KEY `IDX_IDR0002` (`IDR0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='住院临床诊断与住院就诊关联索引表';

/*
-- 索引表，住院临床手术
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IPR_IHR` (
  `IPR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IPR0001` VARCHAR(20) NULL COMMENT '住院临床手术编号；IN_OPER_SN',
  `IPR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IPR_ROWKEY`),
  KEY `KEY_JH_INX_IPR_IHR` (`IPR_ROWKEY`),
  KEY `IDX_IPR0001` (`IPR0001`),
  KEY `IDX_IPR0002` (`IPR0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='住院临床手术与住院就诊关联索引表';

/*
-- 索引表，住院病案首页
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IFP_IHR` (
  `IFP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IFP0001` VARCHAR(20) NULL COMMENT '住院病案首页编号；FIRST_PAGE_SN',
  `IFP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IFP_ROWKEY`),
  KEY `KEY_JH_INX_IFP_IHR` (`IFP_ROWKEY`),
  KEY `IDX_IFP0001` (`IFP0001`),
  KEY `IDX_IFP0002` (`IFP0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='住院病案首页与住院就诊关联索引表';

/*
-- 索引表，首页编目诊断
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IFD_IFP` (
  `IFD_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IFD0001` VARCHAR(20) NULL COMMENT '编目诊断编号；FP_DIAG_SN',
  `IFD0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IFP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IFP0001` VARCHAR(20) NULL COMMENT '住院病案首页编号；FIRST_PAGE_SN',
  `IFP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IFD_ROWKEY`),
  KEY `KEY_JH_INX_IFD_IFP` (`IFD_ROWKEY`),
  KEY `IDX_IFD0001` (`IFD0001`),
  KEY `IDX_IFD0002` (`IFD0002`),
  KEY `KEY_JH_INX_IFP_IHR` (`IFP_ROWKEY`),
  KEY `IDX_IFP0001` (`IFP0001`),
  KEY `IDX_IFP0002` (`IFP0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页编目诊断与住院病案首页关联索引表';

/*
-- 索引表，首页编目手术
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IFR_IFP` (
  `IFR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IFR0001` VARCHAR(20) NULL COMMENT '编目手术编号；FP_OPER_SN',
  `IFR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IFP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IFP0001` VARCHAR(20) NULL COMMENT '住院病案首页编号；FIRST_PAGE_SN',
  `IFP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IFR_ROWKEY`),
  KEY `KEY_JH_INX_IFR_IFP` (`IFR_ROWKEY`),
  KEY `IDX_IFR0001` (`IFR0001`),
  KEY `IDX_IFR0002` (`IFR0002`),
  KEY `KEY_JH_INX_IFP_IHR` (`IFP_ROWKEY`),
  KEY `IDX_IFP0001` (`IFP0001`),
  KEY `IDX_IFP0002` (`IFP0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页编目手术与住院病案首页关联索引表';

/*
-- 索引表，病案首页附页
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IFS_IFP` (
  `IFS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IFS0001` VARCHAR(20) NULL COMMENT '首页附页编号；FP_SUB_SN',
  `IFS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IFP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IFP0001` VARCHAR(20) NULL COMMENT '住院病案首页编号；FIRST_PAGE_SN',
  `IFP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IFS_ROWKEY`),
  KEY `KEY_JH_INX_IFS_IFP` (`IFS_ROWKEY`),
  KEY `IDX_IFS0001` (`IFS0001`),
  KEY `IDX_IFS0002` (`IFS0002`),
  KEY `KEY_JH_INX_IFP_IHR` (`IFP_ROWKEY`),
  KEY `IDX_IFP0001` (`IFP0001`),
  KEY `IDX_IFP0002` (`IFP0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='病案首页附页与住院病案首页关联索引表';

/*
-- 索引表，住院电子病历
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IMR_IHR` (
  `IMR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IMR0001` VARCHAR(20) NULL COMMENT '住院病历记录编号；IN_MR_SN',
  `IMR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IMR_ROWKEY`),
  KEY `KEY_JH_INX_IMR_IHR` (`IMR_ROWKEY`),
  KEY `IDX_IMR0001` (`IMR0001`),
  KEY `IDX_IMR0002` (`IMR0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='住院电子病历与住院就诊关联索引表';

/*
-- 索引表，住院病历文书
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IMF_IMR` (
  `IMF_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IMF0001` VARCHAR(20) NULL COMMENT '住院病历文书编号；IN_MRFILE_SN',
  `IMF0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IMR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IMR0001` VARCHAR(20) NULL COMMENT '住院病历记录编号；IN_MR_SN',
  `IMR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IMF_ROWKEY`),
  KEY `KEY_JH_INX_IMF_IMR` (`IMF_ROWKEY`),
  KEY `IDX_IMF0001` (`IMF0001`),
  KEY `IDX_IMF0002` (`IMF0002`),
  KEY `KEY_JH_INX_IMR_IHR` (`IMR_ROWKEY`),
  KEY `IDX_IMR0001` (`IMR0001`),
  KEY `IDX_IMR0002` (`IMR0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='住院病历文书与住院电子病历关联索引表';

/*
-- 索引表，生命体征记录
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_IVS_IHR` (
  `IVS_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IVS0001` VARCHAR(20) NULL COMMENT '生命体征记录编号；VITAL_SIGNS_SN',
  `IVS0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `IHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `IHR0001` VARCHAR(20) NULL COMMENT '住院就诊编号；IN_HOSP_SN',
  `IHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`IVS_ROWKEY`),
  KEY `KEY_JH_INX_IVS_IHR` (`IVS_ROWKEY`),
  KEY `IDX_IVS0001` (`IVS0001`),
  KEY `IDX_IVS0002` (`IVS0002`),
  KEY `KEY_JH_INX_IHR_PAV` (`IHR_ROWKEY`),
  KEY `IDX_IHR0001` (`IHR0001`),
  KEY `IDX_IHR0002` (`IHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='生命体征记录与住院就诊关联索引表';

/*
-- 索引表，随访记录
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_PAF_PAV` (
  `PAF_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAF0001` VARCHAR(20) NULL COMMENT '随访记录编号；FOLLOW_UP_SN',
  `PAF0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`PAF_ROWKEY`),
  KEY `KEY_JH_INX_PAF_PAV` (`PAF_ROWKEY`),
  KEY `IDX_PAF0001` (`PAF0001`),
  KEY `IDX_PAF0002` (`PAF0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='随访记录与通用就诊次关联索引表';

/*
-- 索引表，费用账单
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_PFB_PAV` (
  `PFB_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFB0001` VARCHAR(20) NULL COMMENT '费用账单编号；FEE_BILL_SN',
  `PFB0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`PFB_ROWKEY`),
  KEY `KEY_JH_INX_PFB_PAV` (`PFB_ROWKEY`),
  KEY `IDX_PFB0001` (`PFB0001`),
  KEY `IDX_PFB0002` (`PFB0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='费用账单与通用就诊次关联索引表';

/*
-- 索引表，费用账单明细
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_PFD_PFB` (
  `PFD_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFD0001` VARCHAR(20) NULL COMMENT '费用账单明细编号；FEE_BL_DTL_SN',
  `PFD0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PFB_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFB0001` VARCHAR(20) NULL COMMENT '费用账单编号；FEE_BILL_SN',
  `PFB0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`PFD_ROWKEY`),
  KEY `KEY_JH_INX_PFD_PFB` (`PFD_ROWKEY`),
  KEY `IDX_PFD0001` (`PFD0001`),
  KEY `IDX_PFD0002` (`PFD0002`),
  KEY `KEY_JH_INX_PFB_PAV` (`PFB_ROWKEY`),
  KEY `IDX_PFB0001` (`PFB0001`),
  KEY `IDX_PFB0002` (`PFB0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='费用账单明细与费用账单关联索引表';

/*
-- 索引表，费用结算记录
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_PFP_PFB` (
  `PFP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFP0001` VARCHAR(20) NULL COMMENT '费用结算编号；FEE_PAY_SN',
  `PFP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PFB_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFB0001` VARCHAR(20) NULL COMMENT '费用账单编号；FEE_BILL_SN',
  `PFB0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`PFP_ROWKEY`),
  KEY `KEY_JH_INX_PFP_PFB` (`PFP_ROWKEY`),
  KEY `IDX_PFP0001` (`PFP0001`),
  KEY `IDX_PFP0002` (`PFP0002`),
  KEY `KEY_JH_INX_PFB_PAV` (`PFB_ROWKEY`),
  KEY `IDX_PFB0001` (`PFB0001`),
  KEY `IDX_PFB0002` (`PFB0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='费用结算记录与费用账单关联索引表';

/*
-- 索引表，费用结算明细
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_PFL_PFP` (
  `PFL_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFL0001` VARCHAR(20) NULL COMMENT '费用结算明细编号；FEE_PAY_DTL_SN',
  `PFL0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PFP_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFP0001` VARCHAR(20) NULL COMMENT '费用结算编号；FEE_PAY_SN',
  `PFP0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PFB_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PFB0001` VARCHAR(20) NULL COMMENT '费用账单编号；FEE_BILL_SN',
  `PFB0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`PFL_ROWKEY`),
  KEY `KEY_JH_INX_PFL_PFP` (`PFL_ROWKEY`),
  KEY `IDX_PFL0001` (`PFL0001`),
  KEY `IDX_PFL0002` (`PFL0002`),
  KEY `KEY_JH_INX_PFP_PFB` (`PFP_ROWKEY`),
  KEY `IDX_PFP0001` (`PFP0001`),
  KEY `IDX_PFP0002` (`PFP0002`),
  KEY `KEY_JH_INX_PFB_PAV` (`PFB_ROWKEY`),
  KEY `IDX_PFB0001` (`PFB0001`),
  KEY `IDX_PFB0002` (`PFB0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='费用结算明细与费用结算记录关联索引表';

/*
-- 索引表，门诊就诊
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_OHR_PAV` (
  `OHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OHR0001` VARCHAR(20) NULL COMMENT '门诊就诊编号；OUT_HOSP_SN',
  `OHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`OHR_ROWKEY`),
  KEY `KEY_JH_INX_OHR_PAV` (`OHR_ROWKEY`),
  KEY `IDX_OHR0001` (`OHR0001`),
  KEY `IDX_OHR0002` (`OHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门诊就诊与通用就诊次关联索引表';

/*
-- 索引表，门诊电子病历
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_OMR_OHR` (
  `OMR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OMR0001` VARCHAR(20) NULL COMMENT '门诊病历记录编号；OUT_MR_SN',
  `OMR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `OHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OHR0001` VARCHAR(20) NULL COMMENT '门诊就诊编号；OUT_HOSP_SN',
  `OHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`OMR_ROWKEY`),
  KEY `KEY_JH_INX_OMR_OHR` (`OMR_ROWKEY`),
  KEY `IDX_OMR0001` (`OMR0001`),
  KEY `IDX_OMR0002` (`OMR0002`),
  KEY `KEY_JH_INX_OHR_PAV` (`OHR_ROWKEY`),
  KEY `IDX_OHR0001` (`OHR0001`),
  KEY `IDX_OHR0002` (`OHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门诊电子病历与门诊就诊关联索引表';

/*
-- 索引表，门诊处方
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_OOR_OHR` (
  `OOR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OOR0001` VARCHAR(20) NULL COMMENT '门诊处方编号；OUT_ORDERS_SN',
  `OOR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `OHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OHR0001` VARCHAR(20) NULL COMMENT '门诊就诊编号；OUT_HOSP_SN',
  `OHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`OOR_ROWKEY`),
  KEY `KEY_JH_INX_OOR_OHR` (`OOR_ROWKEY`),
  KEY `IDX_OOR0001` (`OOR0001`),
  KEY `IDX_OOR0002` (`OOR0002`),
  KEY `KEY_JH_INX_OHR_PAV` (`OHR_ROWKEY`),
  KEY `IDX_OHR0001` (`OHR0001`),
  KEY `IDX_OHR0002` (`OHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门诊处方与门诊就诊关联索引表';

/*
-- 索引表，门诊处方明细
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_OOD_OOR` (
  `OOD_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OOD0001` VARCHAR(20) NULL COMMENT '处方明细编号；OUT_ORD_DTL_SN',
  `OOD0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `OOR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OOR0001` VARCHAR(20) NULL COMMENT '门诊处方编号；OUT_ORDERS_SN',
  `OOR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `OHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OHR0001` VARCHAR(20) NULL COMMENT '门诊就诊编号；OUT_HOSP_SN',
  `OHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`OOD_ROWKEY`),
  KEY `KEY_JH_INX_OOD_OOR` (`OOD_ROWKEY`),
  KEY `IDX_OOD0001` (`OOD0001`),
  KEY `IDX_OOD0002` (`OOD0002`),
  KEY `KEY_JH_INX_OOR_OHR` (`OOR_ROWKEY`),
  KEY `IDX_OOR0001` (`OOR0001`),
  KEY `IDX_OOR0002` (`OOR0002`),
  KEY `KEY_JH_INX_OHR_PAV` (`OHR_ROWKEY`),
  KEY `IDX_OHR0001` (`OHR0001`),
  KEY `IDX_OHR0002` (`OHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门诊处方明细与门诊处方关联索引表';

/*
-- 索引表，门诊分诊
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_OSR_OHR` (
  `OSR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OSR0001` VARCHAR(20) NULL COMMENT '门诊分诊编号；OUT_SHUNT_SN',
  `OSR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `OHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OHR0001` VARCHAR(20) NULL COMMENT '门诊就诊编号；OUT_HOSP_SN',
  `OHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`OSR_ROWKEY`),
  KEY `KEY_JH_INX_OSR_OHR` (`OSR_ROWKEY`),
  KEY `IDX_OSR0001` (`OSR0001`),
  KEY `IDX_OSR0002` (`OSR0002`),
  KEY `KEY_JH_INX_OHR_PAV` (`OHR_ROWKEY`),
  KEY `IDX_OHR0001` (`OHR0001`),
  KEY `IDX_OHR0002` (`OHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门诊分诊与门诊就诊关联索引表';

/*
-- 索引表，门诊预约
//*/
CREATE TABLE IF NOT EXISTS `JH_INX_ORR_OHR` (
  `ORR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `ORR0001` VARCHAR(20) NULL COMMENT '门诊预约记录编号；OUT_RVN_SN',
  `ORR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `OHR_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `OHR0001` VARCHAR(20) NULL COMMENT '门诊就诊编号；OUT_HOSP_SN',
  `OHR0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAV_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAV0001` VARCHAR(20) NULL COMMENT '就诊编号；PAT_VISIT_SN',
  `PAV0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `PAT_ROWKEY` BIGINT NOT NULL COMMENT '索引表唯一号，对应hbase表的rowkey',
  `PAT0001` VARCHAR(20) NULL COMMENT '患者编号；PATIENT_SN',
  `PAT0002` VARCHAR(100) NULL COMMENT 'HIS系统主键；HIS_KEY',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ORR_ROWKEY`),
  KEY `KEY_JH_INX_ORR_OHR` (`ORR_ROWKEY`),
  KEY `IDX_ORR0001` (`ORR0001`),
  KEY `IDX_ORR0002` (`ORR0002`),
  KEY `KEY_JH_INX_OHR_PAV` (`OHR_ROWKEY`),
  KEY `IDX_OHR0001` (`OHR0001`),
  KEY `IDX_OHR0002` (`OHR0002`),
  KEY `KEY_JH_INX_PAV_PAT` (`PAV_ROWKEY`),
  KEY `IDX_PAV0001` (`PAV0001`),
  KEY `IDX_PAV0002` (`PAV0002`),
  KEY `KEY_JH_INX_PAT` (`PAT_ROWKEY`),
  KEY `IDX_PAT0001` (`PAT0001`),
  KEY `IDX_PAT0002` (`PAT0002`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门诊预约与门诊就诊关联索引表';


