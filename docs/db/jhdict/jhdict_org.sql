/*
 Navicat Premium Data Transfer

 Source Server         : *************_mysql8
 Source Server Type    : MySQL
 Source Server Version : 80017
 Source Host           : *************
 Source Database       : jhdict

 Target Server Type    : MySQL
 Target Server Version : 80017
 File Encoding         : utf-8

 Date: 01/16/2020 09:46:31 AM
*/

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
--  Table structure for `JH_BAS_DT_D`
-- ----------------------------
DROP TABLE IF EXISTS `JH_BAS_DT_D`;
CREATE TABLE `JH_BAS_DT_D` (
  `DETAIL_ID` int(11) NOT NULL COMMENT '字典明细唯一序号',
  `MASTER_ID` smallint(6) NOT NULL COMMENT '字典唯一序号',
  `DETAIL_KEY` varchar(50) NOT NULL COMMENT '字典项主键',
  `DETAIL_VALUE` varchar(200) NOT NULL COMMENT '字典项值（内容）',
  `DETAIL_DESC` varchar(500) NOT NULL COMMENT '字典项描述',
  `SHOW_ORDER` int(11) NOT NULL COMMENT '显示顺序',
  `PYM` varchar(50) DEFAULT NULL COMMENT '拼音码',
  `WBM` varchar(50) DEFAULT NULL COMMENT '五笔码',
  `ITEM_VALUE` varchar(50) DEFAULT NULL COMMENT '关联标准值域的值',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`DETAIL_ID`),
  KEY `KEY_JH_BAS_DT_D_DETAIL_ID` (`DETAIL_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自定义字典类明细表';

-- ----------------------------
--  Records of `JH_BAS_DT_D`
-- ----------------------------
BEGIN;
INSERT INTO `JH_BAS_DT_D` VALUES ('1', '1', '0', '未知的性别', '不清楚性别', '1', 'WZDXB', 'FTRNK', '0', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('2', '1', '1', '男性', '男性', '2', 'NX', 'LN', '1', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('3', '1', '2', '女性', '女性', '3', 'NX', 'VN', '2', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('4', '1', '9', '未说明的性别', '未告知性别', '4', 'WSMDXB', 'FYJRNK', '9', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('5', '1', '11', '变性者', '变性人', '5', 'BXZ', 'NNN', '0', '2020-01-06 16:18:18', '2020-01-06 16:18:18');
COMMIT;

-- ----------------------------
--  Table structure for `JH_BAS_DT_M`
-- ----------------------------
DROP TABLE IF EXISTS `JH_BAS_DT_M`;
CREATE TABLE `JH_BAS_DT_M` (
  `MASTER_ID` smallint(6) NOT NULL COMMENT '字典唯一序号',
  `DICT_NAME` varchar(20) NOT NULL COMMENT '字典名（类型）',
  `DICT_DESC` varchar(50) NOT NULL COMMENT '字典项描述',
  `PYM` varchar(50) DEFAULT NULL COMMENT '拼音码',
  `WBM` varchar(50) DEFAULT NULL COMMENT '五笔码',
  `CV_ID` int(11) DEFAULT NULL COMMENT '关联的值域唯一序号',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`MASTER_ID`),
  KEY `KEY_JH_BAS_DT_M_MASTER_ID` (`MASTER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自定义字典类主表';

-- ----------------------------
--  Records of `JH_BAS_DT_M`
-- ----------------------------
BEGIN;
INSERT INTO `JH_BAS_DT_M` VALUES ('1', 'Sex', '性别类型', 'XB', 'NK', '2', '2020-01-06 16:18:18', '2020-01-06 16:18:18');
COMMIT;

-- ----------------------------
--  Table structure for `JH_BAS_DT_T`
-- ----------------------------
DROP TABLE IF EXISTS `JH_BAS_DT_T`;
CREATE TABLE `JH_BAS_DT_T` (
  `TYPE_ID` smallint(6) NOT NULL COMMENT '类型唯一序号',
  `TYPE_NAME` varchar(20) NOT NULL COMMENT '类型名称',
  `TYPE_DESC` varchar(50) NOT NULL COMMENT '类型描述',
  `TYPE_LEN` smallint(6) NOT NULL COMMENT '类型存储长度，字节数量',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`TYPE_ID`),
  KEY `KEY_JH_BAS_DT_T_TYPE_ID` (`TYPE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据中台HBase内用到的列数据类型';

-- ----------------------------
--  Records of `JH_BAS_DT_T`
-- ----------------------------
BEGIN;
INSERT INTO `JH_BAS_DT_T` VALUES ('1', 'String', '字符串（变长）', '0', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('2', 'Integer', '整型（32位）', '4', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('3', 'Long', '长整型（64位）', '8', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('4', 'Float', '单精度浮点型（32位）', '4', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('5', 'Double', '双精度浮点型（64位）', '8', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('6', 'Short', '短整型（16位）', '2', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('7', 'Date', '日期型（yyyy-mm-dd）', '10', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('8', 'Time', '时间型（hh:nn:ss）', '8', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('9', 'DateTime', '日期时间型（yyyy-mm-dd hh:nn:ss）', '19', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('10', 'Byte', '字节（8位）', '1', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('11', 'Blob', '字节流（变长）', '0', '2020-01-06 16:18:17', '2020-01-06 16:18:17'), ('12', '自定义字典', '字典类型，字符串（变长）', '0', '2020-01-06 16:18:17', '2020-01-06 16:18:17');
COMMIT;

-- ----------------------------
--  Table structure for `JH_BAS_WS`
-- ----------------------------
DROP TABLE IF EXISTS `JH_BAS_WS`;
CREATE TABLE `JH_BAS_WS` (
  `WS_ID` smallint(6) NOT NULL COMMENT '标准唯一序号',
  `WS_NAME` varchar(200) NOT NULL COMMENT '标准名称',
  `WS_CODE` varchar(50) DEFAULT NULL COMMENT '标准编号',
  `WS_VER` varchar(20) DEFAULT NULL COMMENT '标准版本',
  `WS_TYPE` varchar(30) DEFAULT NULL COMMENT '标准类型；国家卫生行业标准、国家标准、团体标准、地方标准、HL7、企业标准、其它标准',
  `WS_DESC` varchar(200) DEFAULT NULL COMMENT '描述与说明',
  `WS_TABLE` varchar(200) DEFAULT NULL COMMENT '关联表',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`WS_ID`),
  KEY `KEY_JH_BAS_WS_ID` (`WS_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='各类标准描述表';

-- ----------------------------
--  Records of `JH_BAS_WS`
-- ----------------------------
BEGIN;
INSERT INTO `JH_BAS_WS` VALUES ('1', '数据中台字典', 'JHDC', '2020', '企业标准', '嘉和美康企业标准', '', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('2', '卫生信息数据元目录', 'WS 363', '2011', '国家卫生行业标准', '国家卫生信息数据元目录DE系列', 'JH_BAS_WS_DE', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('3', '卫生信息数据元值域代码', 'WS 364', '2011', '国家卫生行业标准', '国家卫生信息数据元值域代码CV系列', 'JH_BAS_WS_CV', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('4', '疾病分类与代码（ICD10）', 'GB／T 14396', '2016（全国）', '国家标准', '疾病分类与代码（ICD10）2016（全国版）', 'JH_BAS_WS_CV', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('5', '手术、操作分类与代码（ICD9）', 'T／CHIA 001', '2017（全国）', '团体标准', '手术、操作分类与代码（ICD9）2017（全国版）', 'JH_BAS_WS_CV', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('6', '人的性别代码', 'GB／T 2261.1', '2003', '国家标准', '个人基本信息分类与代码 第1部分:人的性别代码', 'JH_BAS_WS_CV', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('7', 'ICD-11', '', '2019', '其它标准', 'ICD-11（预留位置）', '', '2020-01-06 16:18:18', '2020-01-06 16:18:18');
COMMIT;

-- ----------------------------
--  Table structure for `JH_BAS_WS_CV`
-- ----------------------------
DROP TABLE IF EXISTS `JH_BAS_WS_CV`;
CREATE TABLE `JH_BAS_WS_CV` (
  `CV_ID` int(11) NOT NULL COMMENT '值域唯一序号',
  `WS_ID` smallint(6) NOT NULL COMMENT '标准唯一序号',
  `CV_CODE` varchar(50) NOT NULL COMMENT '值域编码',
  `CV_NAME` varchar(50) DEFAULT NULL COMMENT '值域名称',
  `PYM` varchar(50) DEFAULT NULL COMMENT '拼音码',
  `WBM` varchar(50) DEFAULT NULL COMMENT '五笔码',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`CV_ID`),
  KEY `KEY_JH_BAS_WS_CV_ID` (`CV_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据元值域CV系列记录表';

-- ----------------------------
--  Records of `JH_BAS_WS_CV`
-- ----------------------------
BEGIN;
INSERT INTO `JH_BAS_WS_CV` VALUES ('1', '3', 'CV05.01.039', '5岁以下儿童死因分类代码', '5SYXET', 'MCGQUG', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('2', '6', 'JHCV.02.01', '人的性别代码', 'XBDM', 'NKWD', '2020-01-06 16:18:18', '2020-01-06 16:18:18');
COMMIT;

-- ----------------------------
--  Table structure for `JH_BAS_WS_DE`
-- ----------------------------
DROP TABLE IF EXISTS `JH_BAS_WS_DE`;
CREATE TABLE `JH_BAS_WS_DE` (
  `DE_ID` int(11) NOT NULL COMMENT '数据元唯一序号',
  `WS_ID` smallint(6) NOT NULL COMMENT '标准唯一序号',
  `DE_CODE` varchar(50) NOT NULL COMMENT '数据元标识符',
  `DE_NAME` varchar(50) DEFAULT NULL COMMENT '数据元名称',
  `DE_DEF` varchar(50) DEFAULT NULL COMMENT '定义',
  `DE_VAL_TYPE` varchar(20) DEFAULT NULL COMMENT '数据元值的数据类型',
  `DE_FORMAT` varchar(20) DEFAULT NULL COMMENT '表示格式',
  `DE_VAL_LET` varchar(200) DEFAULT NULL COMMENT '数据元允许值',
  `PYM` varchar(50) DEFAULT NULL COMMENT '拼音码',
  `WBM` varchar(50) DEFAULT NULL COMMENT '五笔码',
  `CV_ID` int(11) DEFAULT NULL COMMENT '关联的值域唯一序号',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`DE_ID`),
  KEY `KEY_JH_BAS_WS_DE_ID` (`DE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据元DE系列记录表';

-- ----------------------------
--  Records of `JH_BAS_WS_DE`
-- ----------------------------
BEGIN;
INSERT INTO `JH_BAS_WS_DE` VALUES ('1', '2', 'DE05.01.001.00', '5岁以下儿童死因分类代码', '5岁以下儿童死亡原因在特定编码体系中的代码', 'S3', 'N2', 'WS 364.10 卫生信息数据元值域代码 第10部分：医学诊断 CV05.01.039 5岁以下儿童死因分类代码表', '5SYXET', 'MCGQUG', '1', '2020-01-06 16:18:18', '2020-01-06 16:18:18'), ('2', '2', 'DE02.01.040.00', '患者性别', '个体生理性别在特定编码体系中的代码', 'S3', 'AN', 'GB／T 2261.1', 'HZXB', 'KFNK', '2', '2020-01-06 16:18:18', '2020-01-06 16:18:18');
COMMIT;

-- ----------------------------
--  Table structure for `JH_BAS_WS_ITEM`
-- ----------------------------
DROP TABLE IF EXISTS `JH_BAS_WS_ITEM`;
CREATE TABLE `JH_BAS_WS_ITEM` (
  `ITEM_ID` int(11) NOT NULL COMMENT '值明细唯一序号',
  `CV_ID` int(11) NOT NULL COMMENT '值域唯一序号',
  `ITEM_VALUE` varchar(50) NOT NULL COMMENT '值',
  `ITEM_MEAN` varchar(100) DEFAULT NULL COMMENT '值含义',
  `ITEM_DESC` varchar(200) DEFAULT NULL COMMENT '说明',
  `PYM` varchar(50) DEFAULT NULL COMMENT '拼音码',
  `WBM` varchar(50) DEFAULT NULL COMMENT '五笔码',
  `SHOW_ORDER` int(11) DEFAULT NULL COMMENT '显示顺序',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ITEM_ID`),
  KEY `KEY_JH_BAS_WS_ITEM_ID` (`ITEM_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据元允许值明细项表';

-- ----------------------------
--  Records of `JH_BAS_WS_ITEM`
-- ----------------------------
BEGIN;
INSERT INTO `JH_BAS_WS_ITEM` VALUES ('1', '1', '01', '痢疾', '', 'LJ', 'UU', '1', '2020-01-06 16:18:19', '2020-01-06 16:18:19'), ('2', '1', '02', '败血症', '', 'BXZ', 'MTU', '2', '2020-01-06 16:18:19', '2020-01-06 16:18:19'), ('3', '1', '03', '麻疹', '', 'MZ', 'OU', '3', '2020-01-06 16:18:19', '2020-01-06 16:18:19'), ('4', '2', '0', '未知的性别', '', 'WZDXB', 'FTRNK', '1', '2020-01-06 16:18:19', '2020-01-06 16:18:19'), ('5', '2', '1', '男性', '', 'NX', 'LN', '2', '2020-01-06 16:18:19', '2020-01-06 16:18:19'), ('6', '2', '2', '女性', '', 'NX', 'VN', '3', '2020-01-06 16:18:19', '2020-01-06 16:18:19'), ('7', '2', '9', '未说明的性别', '', 'WSMDXB', 'FYJRNK', '4', '2020-01-06 16:18:19', '2020-01-06 16:18:19');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
