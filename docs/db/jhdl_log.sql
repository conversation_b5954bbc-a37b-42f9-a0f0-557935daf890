/*
Navicat MySQL Data Transfer

Source Server         : *************_jhsa_mysql8.0.17
Source Server Version : 80017
Source Host           : *************:3308
Source Database       : JHDL

Target Server Type    : MYSQL
Target Server Version : 80017
File Encoding         : 65001

Date: 2020-01-06 10:44:33
*/

SET FOREIGN_KEY_CHECKS=0;

# ##############
# Table structure for JH_LOG_DEL_01
# ##############
DROP TABLE IF EXISTS `JH_LOG_UPD_01`;
CREATE TABLE `JH_LOG_UPD_01` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `TAB_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '表名称',
  `ROWKEY` bigint(20) DEFAULT NULL COMMENT 'ROWKEY值',
  `PRE_ROWKEY` bigint(20) DEFAULT NULL,
  `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# ##############
# Table structure for JH_LOG_INS_01
# ##############
DROP TABLE IF EXISTS `JH_LOG_INS_01`;
CREATE TABLE `JH_LOG_INS_01` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `TAB_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '表名称',
  `ROWKEY` bigint(20) NOT NULL COMMENT 'ROWKEY值',
  `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

# ##############
# Table structure for JH_LOG_UPD_01
# ##############
DROP TABLE IF EXISTS `JH_LOG_DEL_01`;
CREATE TABLE `JH_LOG_DEL_01` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `TAB_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '表名称',
  `ROWKEY` bigint(20) DEFAULT NULL COMMENT 'ROWKEY值',
  `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

######################################################################

# ##############
# Table structure for JH_OPER_S_LOG
# ##############
DROP TABLE IF EXISTS `JH_OPER_S_LOG`;
CREATE TABLE `JH_OPER_S_LOG` (
  `ID` bigint(20) unsigned zerofill NOT NULL AUTO_INCREMENT,
  `TAB_NAME` varchar(255) DEFAULT NULL,
  `OPER` varchar(2) DEFAULT NULL,
  `KEY_VALUE` varchar(1000) DEFAULT NULL,
  `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '

更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


# ##############
# Table structure for JH_OPER_S_LOG
# ##############
DROP TABLE IF EXISTS `JH_OPER_S_LOG`;
CREATE TABLE `JH_OPER_S_LOG` (
  `ID` bigint(20) unsigned zerofill NOT NULL AUTO_INCREMENT,
  `TAB_NAME` varchar(255) DEFAULT NULL,
  `OPER` varchar(2) DEFAULT NULL,
  `KEY_VALUE` varchar(1000) DEFAULT NULL,
  `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '

更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `JH_OPER_K_LOG`;
CREATE TABLE `JH_OPER_K_LOG` (
  `ID` bigint(20) unsigned zerofill NOT NULL AUTO_INCREMENT,
  `TAB_NAME` varchar(255) DEFAULT NULL,
  `OPER` varchar(2) DEFAULT NULL,
  `KEY_VALUE` varchar(1000) DEFAULT NULL,
  `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '

更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



#无法在Cache数据库中查询到匹配CdcInfo日志的记录[可能该数据在cdc收到此日志时已被删除]
SELECT * FROM JH_OPER_S_LOG;
#All K operation records
SELECT * FROM JH_OPER_K_LOG;

#count
select count(ID) from JH_OPER_S_LOG;
#count
select count(ID) from JH_OPER_K_LOG;

#detail
select s.TAB_NAME,s.KEY_VALUE,s.UPDATED_AT 'S-TIME',k.UPDATED_AT 'K-TIME' from JH_OPER_S_LOG s ,JH_OPER_K_LOG k
 where s.TAB_NAME=k.TAB_NAME
   and s.KEY_VALUE=k.KEY_VALUE;

#group by tableName order by count
select s.TAB_NAME,s.KEY_VALUE,count(*) from JH_OPER_S_LOG s ,JH_OPER_K_LOG k
 where s.TAB_NAME=k.TAB_NAME
   and s.KEY_VALUE=k.KEY_VALUE
  group by s.TAB_NAME,s.KEY_VALUE
order by count(*) desc;



DROP TABLE IF EXISTS `JH_SEND2IDG_MSG`;
CREATE TABLE `JH_SEND2IDG_MSG` (
                                 `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                 `TAB_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '表名称',
                                 `ROWKEY` bigint(20) DEFAULT NULL COMMENT 'ROWKEY值',
                                 `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;