
/*
//-----------------------------------------//
数据湖的常用表结构与默认数据
HBase Shell 版
//-----------------------------------------//
//*/





/*
-- 创建命名空间
//*/
create_namespace 'JHDL'

/*
-- 表结构字段描述字典表JH_DT_TABST
//*/
create 'JHDL:JH_DT_TABST', 'CF'

/*
-- 列“tab_rktype”的值关联到'jhbas:jh_dT_bas'的描述
-- 后续“CF:T_xxxx”的值关联到'jhbas:jh_dT_bas'的描述
//*/
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_KEY', 'SI'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_NAME', 'JH_SYSINFO'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_DESC', '业务系统基本信息表'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:TAB_RKTYPE', "\x00\x06"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:FLD_COUNT', "\x00\x09"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0001', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0001', 'SI0001'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0001', '系统编号'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0001', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0002', "\x00\x02"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0002', 'SI0002'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0002', '系统名称'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0002', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0003', "\x00\x03"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0003', 'SI0003'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0003', '系统描述'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0003', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0004', "\x00\x04"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0004', 'SI0004'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0004', '数据库类型'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0004', "\x00\x0C"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0005', "\x00\x05"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0005', 'SI0005'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0005', '访问地址'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0005', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0006', "\x00\x06"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0006', 'SI0006'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0006', '访问端口'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0006', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0007', "\x00\x07"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0007', 'SI0007'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0007', '访问用户'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0007', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0008', "\x00\x08"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0008', 'SI0008'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0008', '访问密码'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0008', "\x00\x01"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:I_0009', "\x00\x09"
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:N_0009', 'SI0009'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:D_0009', '命名空间'
put 'JHDL:JH_DT_TABST', "\x00\x01", 'CF:T_0009', "\x00\x01"


/*
-- 记录业务系统情况表JH_SYSINFO
//*/
create 'JHDL:JH_SYSINFO', 'CF'

put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0001', '0000'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0002', '数据湖'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0003', '系统信息样本数据'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0004', '2#,#10#,#HBase 2.x'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0005', 'jhmk01,jhmk02,jhmk03'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0006', '2181'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0007', 'JHDL'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0008', 'JHDL'
put 'JHDL:JH_SYSINFO', "\x00\x00", 'CF:SI0009', 'JHDL'

put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0001', '03'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0002', '电子病历系统'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0003', '嘉和美康电子病历系统'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0004', '2#,#2#,#Oracle 10g后'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0005', '192.168.101.121'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0006', '1521'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0007', 'medrec'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0008', 'medrec'
put 'JHDL:JH_SYSINFO', "\x00\x03", 'CF:SI0009', 'JHDL'
 */
 */

