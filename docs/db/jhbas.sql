
/*
//-----------------------------------------//
公共数据库（基于MySQL全产品通用）的常用表结构与默认数据
//-----------------------------------------//
//*/

/*
-- 数据中台HBase内用到的列数据类型
//*/
CREATE TABLE IF NOT EXISTS `JH_BAS_DT_HB` (
  `TYPE_ID` SMALLINT NOT NULL COMMENT '类型序号',
  `TYPE_NAME` VARCHAR(20) NOT NULL COMMENT '类型名称',
  `TYPE_DESC` VARCHAR(50) NOT NULL COMMENT '类型描述',
  `TYPE_LEN` SMALLINT NOT NULL COMMENT '类型存储长度，字节数量',
  PRIMARY KEY (`TYPE_ID`),
  KEY `KEY_JH_BAS_DT_HB` (`TYPE_ID`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='数据中台HBase内用到的列数据类型';

/*
-- 类型占用的字节长度“1”表示占用1 byte，对于变长数据类型，如字符串设置为零表示可变
//*/
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(1, 'String', '字符串（变长）', 0);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(2, 'Integer', '整型（32位）', 4);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(3, 'Long', '长整型（64位）', 8);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(4, 'Float', '单精度浮点型（32位）', 4);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(5, 'Double', '双精度浮点型（64位）', 8);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(6, 'Short', '短整型（16位）', 2);
/*
-- 日期类型暂时使用字符串方式保存，所以占用字节 10 byte
//*/
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(7, 'Date', '日期型（yyyy-mm-dd）', 10);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(8, 'Time', '时间型（hh:nn:ss）', 8);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(9, 'DateTime', '日期时间型（yyyy-mm-dd hh:nn:ss）', 19);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(10, 'Byte', '字节（8位）', 1);
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(11, 'Blob', '字节流（变长）', 0);
/*
-- 自定义字典类，字典类型数据，转成字符串“name#,#key#,#value”来表示，其中“name”是字典名或类型、“key”是字典主键值、
-- “#,#”为分割符、“value”为字典描述；具体从mysql的jh_dt_bas中获取
-- 注意具体字典来源获取那个字典，这里不做说明
//*/
INSERT INTO `JH_BAS_DT_HB` (`TYPE_ID`, `TYPE_NAME`, `TYPE_DESC`, `TYPE_LEN`) 
VALUES(12, '自定义字典', '字典类型，字符串（变长）', 0);




/*
-- 数据中台用到的自定义字典类型主表，对应“JH_BAS_DT_HB”中“自定义字典”的类型主表
//*/
CREATE TABLE IF NOT EXISTS `JH_BAS_DT_M` (
  `AUTO_ID` SMALLINT NOT NULL COMMENT '序号',
  `DICT_NAME` VARCHAR(20) NOT NULL COMMENT '字典名（类型）',
  `DICT_DESC` VARCHAR(50) NOT NULL COMMENT '字典项描述',
  PRIMARY KEY (`AUTO_ID`),
  KEY `KEY_JH_BAS_DT_M` (`AUTO_ID`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='自定义字典类主表';

INSERT INTO `JH_BAS_DT_M` (`AUTO_ID`, `DICT_NAME`, `DICT_DESC`) VALUES
(1, 'Sex', '性别类型');
INSERT INTO `JH_BAS_DT_M` (`AUTO_ID`, `DICT_NAME`, `DICT_DESC`) VALUES
(2, 'DbType', '数据库类型');


/*
-- 数据中台用到的自定义字典类型明细项目表，对应“JH_BAS_DT_M”的明细项
//*/
CREATE TABLE IF NOT EXISTS `JH_BAS_DT_D` (
  `AUTO_ID` INTEGER NOT NULL COMMENT '序号',
  `DICT_ID` SMALLINT NOT NULL COMMENT '字典主表序号（JH_BAS_DT_M的AUTO_ID）',
  `ITEM_KEY` VARCHAR(50) NOT NULL COMMENT '字典项主键',
  `ITEM_VALUE` VARCHAR(200) NOT NULL COMMENT '字典项值（内容）',
  `ITEM_DESC` VARCHAR(500) NOT NULL COMMENT '字典项描述',
  `SHOW_ORDER` INTEGER NOT NULL COMMENT '显示顺序',
  PRIMARY KEY (`AUTO_ID`),
  KEY `KEY_JH_BAS_DT_D` (`AUTO_ID`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='自定义字典类明细表';

INSERT INTO `JH_BAS_DT_D` (`AUTO_ID`, `DICT_ID`, `ITEM_KEY`, `ITEM_VALUE`, `ITEM_DESC`, `SHOW_ORDER`) VALUES
(1, 1, '1', '男', '男性', 1),
(2, 1, '2', '女', '女性', 2),
(3, 1, '3', '不确定', '无法确定男女', 3),
(4, 1, '9', '未填', '未填写信息', 4),
(5, 2, '1', 'Oracle 9i前', 'Oracle 9i（含）以前的版本', 1),
(6, 2, '2', 'Oracle 10g后', 'Oracle 10g（含）以后的版本', 2),
(7, 2, '3', 'MySQL 6前', 'MySql 6（含）以前的版本', 3),
(8, 2, '4', 'MySQL 7后', 'MySql 7（含）以后的版本', 4),
(9, 2, '5', 'SQL 2005前', 'MS SQL Server 2005（含）以前的版本', 5),
(10, 2, '6', 'SQL 2008后', 'MS SQL Server 2008（含）以后的版本', 6),
(11, 2, '7', 'Cache 2010前', 'Cache 2010（含）以前的版本', 7),
(12, 2, '8', 'Cache 2016后', 'Cache 2016（含）以后的版本', 8),
(13, 2, '9', 'HBase 1.x', 'HBase 1.x（含）以前的版本', 9),
(14, 2, '10', 'HBase 2.x', 'HBase 2.0（含）以后的版本', 10);


/*
-- 数据中台用到的自定义字典类型明细项目扩展表，对应“JH_BAS_DT_D”的明细项中仍有无法描述的内容
//*/
CREATE TABLE IF NOT EXISTS `JH_BAS_DT_EX` (
  `AUTO_ID` INTEGER NOT NULL COMMENT '序号',
  `ITEM_ID` INTEGER NOT NULL COMMENT '字典明细序号（JH_BAS_DT_D的AUTO_ID）',
  `EXT_KEY` VARCHAR(50) NOT NULL COMMENT '字典扩展项主键',
  `EXT_VALUE` VARCHAR(200) NOT NULL COMMENT '字典扩展项值（内容）',
  `EXT_DESC` VARCHAR(500) NOT NULL COMMENT '字典扩展项描述',
  PRIMARY KEY (`AUTO_ID`),
  KEY `KEY_JH_BAS_DT_EX` (`AUTO_ID`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='自定义字典类明细扩展表';

INSERT INTO `JH_BAS_DT_EX` (`AUTO_ID`, `ITEM_ID`, `EXT_KEY`, `EXT_VALUE`, `EXT_DESC`) VALUES
(1, 5, 'jar', 'ojdbc8.jar', '需要用到的jar包（用“:”分开多个文件）'),
(2, 6, 'jar', 'ojdbc8.jar', '需要用到的jar包（用“:”分开多个文件）'),
(3, 7, 'jar', 'mysql-connector-java-5.1.39-bin.jar', '需要用到的jar包（用“:”分开多个文件）'),
(4, 8, 'jar', 'mysql-connector-java-5.1.39-bin.jar', '需要用到的jar包（用“:”分开多个文件）'),
(5, 9, 'jar', 'sqljdbc42.jar', '需要用到的jar包（用“:”分开多个文件）'),
(6, 10, 'jar', 'sqljdbc42.jar', '需要用到的jar包（用“:”分开多个文件）'),
(7, 11, 'jar', 'cachedb.jar:cachejdbc.jar', '需要用到的jar包（用“:”分开多个文件）'),
(8, 12, 'jar', 'cachedb.jar:cachejdbc.jar', '需要用到的jar包（用“:”分开多个文件）'),
(9, 11, 'namespace', '1', '需要提供命名空间'),
(10, 12, 'namespace', '1', '需要提供命名空间');


