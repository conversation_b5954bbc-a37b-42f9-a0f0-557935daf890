<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.jhdl</groupId>
		<artifactId>jhdl-common</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath/>
	</parent>
	<groupId>com.jhids</groupId>
	<artifactId>jhids-parent</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>jhids-parent</name>
	<packaging>pom</packaging>
	<description>JiaHe Intelligent Data Service System</description>

	<modules>
        <module>jhids-emr</module><!--集成微服务-->
	</modules>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<jhdl.common.version>0.0.1</jhdl.common.version>
	</properties>

</project>
