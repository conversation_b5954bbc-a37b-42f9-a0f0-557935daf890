version: "3"
services:
  jhidc-is:
    image: java:8
    restart: always
    hostname: "jhidc-is"
    container_name: jhidc-is
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /opt/jhdcp/container/jhidc/jhidc-is:/usr/jhidc-is
      - /opt/jhdcp/container/jhidc/jhidc-is/logs:/data/container/jhidc/jhidc-is/logs
      - /etc/localtime:/etc/localtime
    working_dir: /usr/jhidc-is
    network_mode: "host"
    ports:
      - "9054:9054"
    extra_hosts:
      - "jhmk01:************"
      - "jhmk02:************"
      - "jhmk03:************"
      - "jhmk04:************"
      - "jhmk05:************"
      - "jhmk06:************"
      - "jhmk07:************"
      - "jhmk08:************"
      - "jhmk09:************"
      - "jhmk10:************"
      - "10-160-60-13:************"
      - "10-160-60-17:************"
      - "10-160-60-19:************"
      - "10-160-60-20:************"
      - "10-160-60-14:************"
      - "10-160-60-15:************"
      - "10-160-60-18:************"
      - "10-160-60-16:************"
      - "10-160-60-12:************"
      - "10-160-60-11:************"
      - "10-160-60-13.:************"
      - "10-160-60-17.:************"
      - "10-160-60-19.:************"
      - "10-160-60-20.:************"
      - "10-160-60-14.:************"
      - "10-160-60-15.:************"
      - "10-160-60-18.:************"
      - "10-160-60-16.:************"
      - "10-160-60-12.:************"
      - "10-160-60-11.:************"
      - "casserver:10.160.60.28"
      - "jhidc-is:127.0.0.1"
    command: java -Djava.security.egd=file:/dev/./urandom -jar -Dfile.encoding=UTF-8 -Dspring.profiles.active=xhyy -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -Xms4096m -Xmx4096m jhidc-is-0.0.1-SNAPSHOT.jar
