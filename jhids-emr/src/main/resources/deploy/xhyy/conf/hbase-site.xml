<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<property>
<name>master.rmi.RMIClientSocketFactory.class</name>
<value>com.huawei.bigdata.om.controller.api.extern.monitor.RmiClientLocalhostSocketFactory</value>
</property>
<property>
<name>hbase.server.thread.wakefrequency</name>
<value>10000</value>
</property>
<property>
<name>hbase.master.loadbalance.bytable</name>
<value>true</value>
</property>
<property>
<name>hbase.master.executor.closeregion.threads</name>
<value>25</value>
</property>
<property>
<name>dfs.replication</name>
<value>3</value>
</property>
<property>
<name>hbase.master.executor.serverops.threads</name>
<value>50</value>
</property>
<property>
<name>hbase.master.port</name>
<value>21300</value>
</property>
<property>
<name>hbase.regionserver.keytab.file</name>
<value>/opt/huawei/Bigdata/FusionInsight_HD_6.5.1.6/install/FusionInsight-HBase-1.3.1/keytabs/HBase/hbase.keytab</value>
</property>
<property>
<name>hbase.hmaster.ip.lists</name>
<value>10-160-60-12,10-160-60-13</value>
</property>
<property>
<name>hbase.master.executor.openregion.threads</name>
<value>25</value>
</property>
<property>
<name>hbase.fileStream.cleaner.ttl.thread.wakefrequency</name>
<value>86400</value>
</property>
<property>
<name>hbase.master.keytab.file</name>
<value>/opt/huawei/Bigdata/FusionInsight_HD_6.5.1.6/install/FusionInsight-HBase-1.3.1/keytabs/HBase/hbase.keytab</value>
</property>
<property>
<name>hbase.server</name>
<value>HBase</value>
</property>
<property>
<name>hbase.dfs.client.read.shortcircuit.buffer.size</name>
<value>131072</value>
</property>
<property>
<name>master.rmi.registry.ip</name>
<value>127.0.0.1</value>
</property>
<property>
<name>hbase.fileStream.cleaner.tmp.thread.wakefrequency</name>
<value>3600</value>
</property>
<property>
<name>hbase.zookeeper.quorum</name>
<value>10-160-60-11,10-160-60-12,10-160-60-13</value>
</property>
<property>
<name>hbase.client.write.buffer</name>
<value>2097152</value>
</property>
<property>
<name>hbase.large.object.threshold.max</name>
<value>10485760</value>
</property>
<property>
<name>hbase.MetaMigrationConvertingToPB</name>
<value>false</value>
</property>
<property>
<name>hbase.filestream.rootdir</name>
<value>/hbaseFileStream</value>
</property>
<property>
<name>hbase.rpc.protection</name>
<value>authentication</value>
</property>
<property>
<name>hbase.crypto.keyprovider.parameters.uri</name>
<value></value>
</property>
<property>
<name>hbase.rootdir</name>
<value>hdfs://hacluster/hbase</value>
</property>
<property>
<name>hbase.coprocessor.enabled</name>
<value>true</value>
</property>
<property>
<name>hfile.format.version</name>
<value>3</value>
</property>
<property>
<name>hbase.regionserver.kerberos.principal</name>
<value>hbase/<EMAIL></value>
</property>
<property>
<name>hbase.regionserver.hfilecleaner.large.queue.size</name>
<value>10240</value>
</property>
<property>
<name>hbase.coprocessor.user.enabled</name>
<value>true</value>
</property>
<property>
<name>hbase.security.authentication</name>
<value>kerberos</value>
</property>
<property>
<name>hbase.master.cleaner.interval</name>
<value>60000</value>
</property>
<property>
<name>hbase.splitlog.manager.timeout</name>
<value>600000</value>
</property>
<property>
<name>hbase.sessioncontrol.enable</name>
<value>false</value>
</property>
<property>
<name>hbase.snapshot.enabled</name>
<value>true</value>
</property>
<property>
<name>hbase.external.data.rootdir</name>
<value>hdfs://hacluster/hbase/extdata</value>
</property>
<property>
<name>hbase.disable.table.max.retry.attempts</name>
<value>24</value>
</property>
<property>
<name>zookeeper.session.timeout</name>
<value>90000</value>
</property>
<property>
<name>dfs.domain.socket.path</name>
<value>/var/run/FusionInsight-HDFS/dn_socket</value>
</property>
<property>
<name>phoenix.coprocessor.maxServerCacheTimeToLiveMs</name>
<value>1800000</value>
</property>
<property>
<name>jmx.include.hosts</name>
<value>.*</value>
</property>
<property>
<name>hbase.master.ui.readonly</name>
<value>true</value>
</property>
<property>
<name>hbase.master.loadbalancer.class</name>
<value>org.apache.hadoop.hbase.master.balancer.StochasticLoadBalancer</value>
</property>
<property>
<name>hbase.quota.refresh.period</name>
<value>300000</value>
</property>
<property>
<name>hbase.auth.key.update.interval</name>
<value>86400000</value>
</property>
<property>
<name>hbase.master.kerberos.principal</name>
<value>hbase/<EMAIL></value>
</property>
<property>
<name>hbase.coprocessor.abortonerror</name>
<value>true</value>
</property>
<property>
<name>hbase.master.balancer.stochastic.maxRunningTime</name>
<value>300000</value>
</property>
<property>
<name>hbase.regionserver.hfilecleaner.small.thread.count</name>
<value>1</value>
</property>
<property>
<name>hbase.use.secondary.index</name>
<value>true</value>
</property>
<property>
<name>hbase.crypto.master.key.name</name>
<value>omm</value>
</property>
<property>
<name>hbase.huawei.restore.tmpdir</name>
<value>hdfs://hacluster/hbase/extdata/restore/tmp</value>
</property>
<property>
<name>hbase.ipc.server.callqueue.handler.factor</name>
<value>0.1</value>
</property>
<property>
<name>hbase.master.wait.on.regionservers.timeout</name>
<value>30000</value>
</property>
<property>
<name>hbase.fileStream.cleaner.ttl.expire.time</name>
<value>604800</value>
</property>
<property>
<name>hbase.ssl.enabled</name>
<value>true</value>
</property>
<property>
<name>hbase.superuser</name>
<value>hbase,@supergroup,@zkclient,@System_administrator_186</value>
</property>
<property>
<name>hbase.master.logcleaner.ttl</name>
<value>600000</value>
</property>
<property>
<name>hbase.master.namespace.init.timeout</name>
<value>3600000</value>
</property>
<property>
<name>hbase.sessioncontrol.maxconnectionsinperiod</name>
<value>65535</value>
</property>
<property>
<name>hbase.regionserver.handler.count</name>
<value>50</value>
</property>
<property>
<name>hbase.master.ipc.address</name>
<value>10-160-60-12</value>
</property>
<property>
<name>hbase.sessioncontrol.maxconnections</name>
<value>65535</value>
</property>
<property>
<name>hbase.crypto.keyprovider.parameters</name>
<value>?encryptedtext=</value>
</property>
<property>
<name>dfs.client.read.shortcircuit.streams.cache.expiry.ms</name>
<value>100000</value>
</property>
<property>
<name>hbase.crypto.cipherprovider</name>
<value>com.huawei.hadoop.hbase.io.crypto.HuaweiCipherProvider</value>
</property>
<property>
<name>phoenix.default.column.encoded.bytes.attrib</name>
<value>0</value>
</property>
<property>
<name>hbase.region.transition.threshold</name>
<value>300000</value>
</property>
<property>
<name>hbase.bulkload.staging.dir</name>
<value>/user/${user.name}/hbase-staging</value>
</property>
<property>
<name>hbase.sessioncontrol.maxconnectionsperuser</name>
<value>65535</value>
</property>
<property>
<name>hbase.crypto.wal.algorithm</name>
<value>AES</value>
</property>
<property>
<name>hbase.coprocessor.master.classes</name>
<value>org.apache.hadoop.hbase.hindex.server.master.HIndexMasterCoprocessor,com.huawei.hadoop.hbase.backup.services.RecoveryCoprocessor,org.apache.hadoop.hbase.security.access.AccessController,org.apache.hadoop.hbase.security.access.ReadOnlyClusterEnabler,org.apache.hadoop.hbase.coprocessor.RollingUpgradeMasterObserver</value>
</property>
<property>
<name>hbase.huawei.backup.output</name>
<value>/user/hbase/hbaseBackup/hbase</value>
</property>
<property>
<name>hbase.crypto.key.algorithm</name>
<value>AES</value>
</property>
<property>
<name>hbase.region.double.assignment.auto.recovery.period</name>
<value>300000</value>
</property>
<property>
<name>default.hbase.superuser</name>
<value>hbase,@supergroup,@zkclient,@System_administrator_186</value>
</property>
<property>
<name>hbase.regionserver.thread.hfilecleaner.throttle</name>
<value>67108864</value>
</property>
<property>
<name>hbase.crypto.keyprovider.parameters.encryptedtext</name>
<value></value>
</property>
<property>
<name>hbase.config.crypt.class</name>
<value>com.huawei.hadoop.datasight.security.FMHbaseCryptAdapter</value>
</property>
<property>
<name>hbase.data.rootdir</name>
<value>/hbase</value>
</property>
<property>
<name>hbase.client.retries.number</name>
<value>35</value>
</property>
<property>
<name>hbase.quota.enabled</name>
<value>false</value>
</property>
<property>
<name>hbase.master.initializationmonitor.timeout</name>
<value>3600000</value>
</property>
<property>
<name>hbase.replication.cluster.id</name>
<value></value>
</property>
<property>
<name>hbase.server.alias</name>
<value>HBase</value>
</property>
<property>
<name>hbase.regionserver.hfilecleaner.small.queue.size</name>
<value>10240</value>
</property>
<property>
<name>hbase.snapshot.master.timeout.millis</name>
<value>300000</value>
</property>
<property>
<name>hbase.sessioncontrol.limitperiod</name>
<value>60</value>
</property>
<property>
<name>hbase.huawei.restore.output</name>
<value>hdfs://hacluster/hbase/extdata/hbaseRestore</value>
</property>
<property>
<name>dfs.client.read.shortcircuit.streams.cache.size</name>
<value>512</value>
</property>
<property>
<name>hbase.region.assignment.auto.recovery.period</name>
<value>1800000</value>
</property>
<property>
<name>zookeeper.znode.parent</name>
<value>/hbase</value>
</property>
<property>
<name>hbase.master.info.port</name>
<value>21301</value>
</property>
<property>
<name>hbase.master.initializationmonitor.haltontimeout</name>
<value>false</value>
</property>
<property>
<name>hbase.cluster.distributed</name>
<value>true</value>
</property>
<property>
<name>master.rmi.connector.port</name>
<value>21306</value>
</property>
<property>
<name>hbase.coprocessor.user.region.classes</name>
<value></value>
</property>
<property>
<name>hbase.fs.tmp.dir</name>
<value>/tmp</value>
</property>
<property>
<name>hbase.client.rpc.codec</name>
<value>org.apache.hadoop.hbase.codec.KeyValueCodecWithTags</value>
</property>
<property>
<name>hbase.security.authorization</name>
<value>true</value>
</property>
<property>
<name>hbase.ipc.server.callqueue.scan.ratio</name>
<value>0</value>
</property>
<property>
<name>hbase.coprocessor.master.enable.jmx</name>
<value>true</value>
</property>
<property>
<name>hbase.regionserver.hlog.writer.impl</name>
<value>org.apache.hadoop.hbase.regionserver.wal.SecureProtobufLogWriter</value>
</property>
<property>
<name>master.rmi.registry.host</name>
<value>127.0.0.1</value>
</property>
<property>
<name>hbase.fileStream.cleaner.tmp.expire.time</name>
<value>86400</value>
</property>
<property>
<name>zookeeper.huawei.backup.parent</name>
<value>hwbackup/hbase</value>
</property>
<property>
<name>hbase.crypto.keyprovider</name>
<value>org.apache.hadoop.hbase.io.crypto.KeyStoreKeyProvider</value>
</property>
<property>
<name>hbase.master.dns.nameserver</name>
<value>default</value>
</property>
<property>
<name>hbase.bulk.assignment.waiton.empty.rit</name>
<value>300000</value>
</property>
<property>
<name>hbase.http.filter.initializers</name>
<value>com.huawei.hadoop.hbase.adapter.sso.FlowCtrlFilter,com.huawei.hadoop.hbase.adapter.sso.XSSFilterInitializer,com.huawei.hadoop.hbase.adapter.sso.InternalSpnegoFilter,com.huawei.hadoop.hbase.adapter.sso.CASClientFilter,com.huawei.hadoop.hbase.adapter.sso.LogoutFilterInitializer</value>
</property>
<property>
<name>hbase.client.pause</name>
<value>100</value>
</property>
<property>
<name>hbase.master.info.bindAddress</name>
<value>10-160-60-12</value>
</property>
<property>
<name>master.rmi.registry.port</name>
<value>21306</value>
</property>
<property>
<name>dfs.client.read.shortcircuit</name>
<value>true</value>
</property>
<property>
<name>hbase.master.preload.tabledescriptors</name>
<value>false</value>
</property>
<property>
<name>phoenix.coprocessor.maxMetaDataCacheTimeToLiveMs</name>
<value>1800000</value>
</property>
<property>
<name>hbase.ipc.server.callqueue.read.ratio</name>
<value>0</value>
</property>
<property>
<name>hbase.master.procedure.threads</name>
<value>20</value>
</property>
<property>
<name>hbase.regionserver.hfilecleaner.large.thread.count</name>
<value>1</value>
</property>
<property>
<name>hbase.bulk.assignment.threadpool.size</name>
<value>20</value>
</property>
<property>
<name>hbase.master.dns.interface</name>
<value>default</value>
</property>
<property>
<name>hbase.master.wait.on.regionservers.mintostart</name>
<value>1</value>
</property>
<property>
<name>hbase.region.double.assignment.auto.recovery.enabled</name>
<value>true</value>
</property>
<property>
<name>hbase.huawei.restore.output.v2</name>
<value>hdfs://hacluster/hbase/extdata/restore/output</value>
</property>
<property>
<name>hbase.master.acl.init.timeout</name>
<value>300000</value>
</property>
<property>
<name>hbase.client.keyvalue.maxsize</name>
<value>104857600</value>
</property>
<property>
<name>hbase.security.exec.permission.checks</name>
<value>true</value>
</property>
<property>
<name>hbase.client.scanner.caching</name>
<value>100</value>
</property>
<property>
<name>hbase.regionserver.hlog.reader.impl</name>
<value>org.apache.hadoop.hbase.regionserver.wal.SecureProtobufLogReader</value>
</property>
<property>
<name>hbase.rpc.timeout</name>
<value>60000</value>
</property>
<property>
<name>hbase.zookeeper.property.clientPort</name>
<value>24002</value>
</property>
<property>
<name>hbase.regionserver.wal.encryption</name>
<value>false</value>
</property>
<property>
<name>hbase.replication.bulkload.enabled</name>
<value>false</value>
</property>
<property>
<name>hbase.region.assignment.auto.recovery.enabled</name>
<value>true</value>
</property>
<property>
<name>hbase.fileStream.cleaner.ttl.enable</name>
<value>false</value>
</property>
</configuration>
