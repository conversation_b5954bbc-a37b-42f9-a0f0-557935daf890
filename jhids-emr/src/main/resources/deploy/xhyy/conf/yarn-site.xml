<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<property>
<name>yarn.resourcemanager.zk-address</name>
<value>10-160-60-11:24002,10-160-60-12:24002,10-160-60-13:24002</value>
</property>
<property>
<name>yarn.client.failover-proxy-provider</name>
<value>org.apache.hadoop.yarn.client.ConfiguredRMFailoverProxyProvider</value>
</property>
<property>
<name>mapreduce.shuffle.port</name>
<value>26008</value>
</property>
<property>
<name>yarn.application.classpath</name>
<value>$HADOOP_CONF_DIR,$HADOOP_COMMON_HOME/share/hadoop/common/*,$HADOOP_COMMON_HOME/share/hadoop/common/lib/*,$HADOOP_HDFS_HOME/share/hadoop/hdfs/*,$HADOOP_HDFS_HOME/share/hadoop/hdfs/lib/*,$HADOOP_YARN_HOME/share/hadoop/mapreduce/*,$HADOOP_YARN_HOME/share/hadoop/mapreduce/lib/*,$HADOOP_YARN_HOME/share/hadoop/yarn/*,$HADOOP_YARN_HOME/share/hadoop/yarn/lib/*</value>
</property>
<property>
<name>yarn.resourcemanager.ha.id</name>
<value>24</value>
</property>
<property>
<name>yarn.resourcemanager.address.24</name>
<value>10-160-60-12:26004</value>
</property>
<property>
<name>yarn.resourcemanager.address.25</name>
<value>10-160-60-13:26004</value>
</property>
<property>
<name>yarn.nodemanager.linux-container-executor.cgroups.mount-path</name>
<value></value>
</property>
<property>
<name>yarn.nodemanager.address</name>
<value>10-160-60-14:26009</value>
</property>
<property>
<name>yarn.resourcemanager.ha.enabled</name>
<value>true</value>
</property>
<property>
<name>yarn.nodemanager.localizer.address</name>
<value></value>
</property>
<property>
<name>yarn.resourcemanager.cluster-id</name>
<value>yarncluster</value>
</property>
<property>
<name>yarn.resourcemanager.hostname.24</name>
<value>10-160-60-12</value>
</property>
<property>
<name>yarn.resourcemanager.hostname.25</name>
<value>10-160-60-13</value>
</property>
<property>
<name>yarn.nodemanager.aux-services</name>
<value>mapreduce_shuffle</value>
</property>
<property>
<name>yarn.resourcemanager.zk-timeout-ms</name>
<value></value>
</property>
<property>
<name>yarn.resourcemanager.am.max-attempts</name>
<value>2</value>
</property>
<property>
<name>yarn.resourcemanager.ha.automatic-failover.enabled</name>
<value>true</value>
</property>
<property>
<name>yarn.nodemanager.webapp.address</name>
<value>10-160-60-14:26006</value>
</property>
<property>
<name>yarn.nodemanager.aux-services.mapreduce_shuffle.class</name>
<value>org.apache.hadoop.mapred.HWShuffleHandler</value>
</property>
<property>
<name>yarn.resourcemanager.resource-tracker.address.24</name>
<value>10-160-60-12:26003</value>
</property>
<property>
<name>yarn.resourcemanager.resource-tracker.address.25</name>
<value>10-160-60-13:26003</value>
</property>
<property>
<name>yarn.resourcemanager.store.class</name>
<value>org.apache.hadoop.yarn.server.resourcemanager.recovery.AsyncZKRMStateStore</value>
</property>
<property>
<name>yarn.resourcemanager.admin.address.25</name>
<value>10-160-60-13:26005</value>
</property>
<property>
<name>yarn.resourcemanager.admin.address.24</name>
<value>10-160-60-12:26005</value>
</property>
<property>
<name>yarn.http.policy</name>
<value>HTTPS_ONLY</value>
</property>
<property>
<name>yarn.nodemanager.log.retain-seconds</name>
<value>10800</value>
</property>
<property>
<name>yarn.resourcemanager.zk-state-store.parent-path</name>
<value>/rmstore</value>
</property>
<property>
<name>yarn.resourcemanager.ha.rm-ids</name>
<value>24,25</value>
</property>
<property>
<name>yarn.resourcemanager.webapp.address.25</name>
<value>10-160-60-13:26000</value>
</property>
<property>
<name>yarn.resourcemanager.webapp.address.24</name>
<value>10-160-60-12:26000</value>
</property>
<property>
<name>yarn.nodemanager.resource.memory-mb</name>
<value></value>
</property>
<property>
<name>clientPort</name>
<value>24002</value>
</property>
<property>
<name>yarn.scheduler.minimum-allocation-mb</name>
<value>512</value>
</property>
<property>
<name>yarn.resourcemanager.zk-num-retries</name>
<value></value>
</property>
<property>
<name>yarn.resourcemanager.principal</name>
<value>mapred/<EMAIL></value>
</property>
<property>
<name>yarn.nodemanager.port</name>
<value>26009</value>
</property>
<property>
<name>yarn.resourcemanager.nodes.include-path</name>
<value></value>
</property>
<property>
<name>yarn.resourcemanager.zk-retry-interval-ms</name>
<value></value>
</property>
<property>
<name>yarn.nodemanager.webapp.port</name>
<value>26006</value>
</property>
<property>
<name>yarn.web-proxy.port</name>
<value></value>
</property>
<property>
<name>yarn.scheduler.maximum-allocation-mb</name>
<value>65536</value>
</property>
<property>
<name>yarn.resourcemanager.scheduler.address.24</name>
<value>10-160-60-12:26002</value>
</property>
<property>
<name>yarn.resourcemanager.scheduler.address.25</name>
<value>10-160-60-13:26002</value>
</property>
<property>
<name>yarn.resourcemanager.ha.automatic-failover.embedded</name>
<value>true</value>
</property>
<property>
<name>yarn.resourcemanager.recovery.enabled</name>
<value>true</value>
</property>
<property>
<name>yarn.nodemanager.vmem-pmem-ratio</name>
<value>5.0</value>
</property>
</configuration>
