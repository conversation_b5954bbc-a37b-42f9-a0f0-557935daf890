<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<property>
<name>dfs.namenode.rpc-address.hacluster.9</name>
<value>10-160-60-12:25000</value>
</property>
<property>
<name>dfs.nameservices.mappings</name>
<value>[{"name":"hacluster","roleInstances":["10","9"],"relationInstances":["************","************","************"]}]</value>
</property>
<property>
<name>dfs.namenode.kerberos.principal.pattern</name>
<value>*</value>
</property>
<property>
<name>dfs.client.failover.proxy.provider.hacluster</name>
<value>org.apache.hadoop.hdfs.server.namenode.ha.AdaptiveFailoverProxyProvider</value>
</property>
<property>
<name>dfs.client.block.write.replace-datanode-on-failure.enable</name>
<value>true</value>
</property>
<property>
<name>dfs.client.io-weight</name>
<value>10</value>
</property>
<property>
<name>dfs.nameservices</name>
<value>hacluster</value>
</property>
<property>
<name>dfs.client.failover.proxy.provider.nsfed</name>
<value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
</property>
<property>
<name>fs.defaultFS.for.router-based-federation</name>
<value></value>
</property>
<property>
<name>dfs.client.block.write.replace-datanode-on-failure.policy</name>
<value>ALWAYS</value>
</property>
<property>
<name>dfs.client.retry.window.base</name>
<value>5</value>
</property>
<property>
<name>dfs.webhdfs.enabled</name>
<value>true</value>
</property>
<property>
<name>dfs.client.file-block-storage-locations.timeout.millis</name>
<value>600000</value>
</property>
<property>
<name>dfs.client.close.ack-timeout</name>
<value>900000</value>
</property>
<property>
<name>dfs.ha.namenodes.hacluster</name>
<value>10,9</value>
</property>
<property>
<name>dfs.client.block.write.replace-datanode-on-failure.min-replication</name>
<value>1</value>
</property>
<property>
<name>dfs.namenode.https.port</name>
<value>25003</value>
</property>
<property>
<name>ipc.client.connect.max.retries.on.timeouts</name>
<value>15</value>
</property>
<property>
<name>dfs.web.authentication.kerberos.principal</name>
<value>HTTP/<EMAIL></value>
</property>
<property>
<name>dfs.client.socketcache.capacity</name>
<value>16</value>
</property>
<property>
<name>dfs.blocksize</name>
<value>134217728</value>
</property>
<property>
<name>dfs.client.socket-timeout</name>
<value>600000</value>
</property>
<property>
<name>dfs.datanode.address</name>
<value>10-160-60-14:25009</value>
</property>
<property>
<name>dfs.ha.namenode.id</name>
<value>9</value>
</property>
<property>
<name>dfs.namenode.rpc.port</name>
<value>25000</value>
</property>
<property>
<name>dfs.datanode.data.dir.perm</name>
<value>700</value>
</property>
<property>
<name>dfs.client.failover.observer.auto-msync-period.hacluster</name>
<value>0ms</value>
</property>
<property>
<name>dfs.client.socketcache.expiryMsec</name>
<value>3000</value>
</property>
<property>
<name>dfs.client.datanode-restart.timeout</name>
<value>60</value>
</property>
<property>
<name>dfs.client.failover.random.order</name>
<value>true</value>
</property>
<property>
<name>dfs.client-write-packet-size</name>
<value>262144</value>
</property>
<property>
<name>dfs.datanode.socket.write.timeout</name>
<value>600000</value>
</property>
<property>
<name>dfs.namenode.rpc-address.hacluster.10</name>
<value>10-160-60-13:25000</value>
</property>
<property>
<name>fs.permissions.umask-mode</name>
<value>066</value>
</property>
<property>
<name>dfs.datanode.socket.reuse.keepalive</name>
<value>-1</value>
</property>
<property>
<name>dfs.client.failover.max.attempts</name>
<value>10</value>
</property>
<property>
<name>dfs.datanode.http.address</name>
<value>10-160-60-14:25010</value>
</property>
<property>
<name>dfs.client.read.striped.threadpool.size</name>
<value>256</value>
</property>
</configuration>
