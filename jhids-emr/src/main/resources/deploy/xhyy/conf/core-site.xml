<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<property>
<name>hadoop.security.groups.cache.secs</name>
<value>60</value>
</property>
<property>
<name>hbase.master.mob.ttl.cleaner.period</name>
<value>86400</value>
</property>
<property>
<name>hadoop.proxyuser.mapred.hosts</name>
<value>*</value>
</property>
<property>
<name>fs.nas.task.nodes</name>
<value></value>
</property>
<property>
<name>hbase.kerberos.principal</name>
<value>hbase/<EMAIL></value>
</property>
<property>
<name>cas.conf.serverName.on</name>
<value>https://************:20026/HBase/HMaster/30</value>
</property>
<property>
<name>hadoop.security.auth_to_local</name>
<value>RULE:[1:$1]
RULE:[2:$1]
DEFAULT</value>
</property>
<property>
<name>ipc.ping.interval</name>
<value>60000</value>
</property>
<property>
<name>hbase.mob.file.cache.size</name>
<value>1000</value>
</property>
<property>
<name>fs.obs.auth.agency-mapping.localpath</name>
<value>/opt/Bigdata/agency-mapping.properties</value>
</property>
<property>
<name>fs.obs.security.provider</name>
<value>com.huawei.mrs.MrsObsCredentialsProvider</value>
</property>
<property>
<name>hadoop.proxyuser.spark.hosts</name>
<value>*</value>
</property>
<property>
<name>fs.obs.socket.recv.buffer</name>
<value>65536</value>
</property>
<property>
<name>hadoop.proxyuser.spark.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.proxyuser.loader.groups</name>
<value>*</value>
</property>
<property>
<name>dfs.encrypt.data.transfer.cipher.suites</name>
<value>AES/CTR/NoPadding</value>
</property>
<property>
<name>hadoop.proxyuser._default_proxy_user_.hosts</name>
<value>*</value>
</property>
<property>
<name>hadoop.proxyuser.spark2x.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.http.kerberos.internal.spnego.principal</name>
<value>hbase/<EMAIL></value>
</property>
<property>
<name>hadoop.proxyuser.mapred.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.security.logout.ui.enable</name>
<value>true</value>
</property>
<property>
<name>hadoop.security.group.mapping</name>
<value>org.apache.hadoop.security.ShellBasedUnixGroupsMapping</value>
</property>
<property>
<name>hadoop.ssl.keystores.factory.class</name>
<value>org.apache.hadoop.security.ssl.FileBasedKeyStoresFactory</value>
</property>
<property>
<name>hadoop.proxyuser.miner.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.security.crypto.buffer.size</name>
<value>8192</value>
</property>
<property>
<name>ipc.client.rpc-timeout.ms</name>
<value>300000</value>
</property>
<property>
<name>hadoop.proxyuser.miner.hosts</name>
<value>*</value>
</property>
<property>
<name>hbase.mob.file.compaction.chore.period</name>
<value>604800</value>
</property>
<property>
<name>fs.obs.socket.send.buffer</name>
<value>65536</value>
</property>
<property>
<name>hadoop.proxyuser.pollux.hosts</name>
<value>*</value>
</property>
<property>
<name>hadoop.proxyuser.omm.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.ssl.enabled.protocols</name>
<value>TLSv1.1,TLSv1.2</value>
</property>
<property>
<name>hadoop.proxyuser.spark2x.hosts</name>
<value>*</value>
</property>
<property>
<name>hbase.mob.file.compaction.threads.max</name>
<value>1</value>
</property>
<property>
<name>hadoop.exclude.ciphers.ext</name>
<value>TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA,SSL_DHE_RSA_WITH_3DES_EDE_CBC_SHA,SSL_RSA_WITH_3DES_EDE_CBC_SHA,TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_3DES_EDE_CBC_SHA,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384</value>
</property>
<property>
<name>fs.obs.secret.key</name>
<value></value>
</property>
<property>
<name>hadoop.http.authentication.center.listener</name>
<value>org.jasig.cas.client.session.SingleSignOutHttpSessionListener</value>
</property>
<property>
<name>fs.viewfs.mounttable.ClusterX.homedir</name>
<value>/</value>
</property>
<property>
<name>hbase.mob.file.compaction.mergeable.threshold</name>
<value>201326592</value>
</property>
<property>
<name>fs.viewfs.rename.strategy</name>
<value>SAME_FILESYSTEM_ACROSS_MOUNTPOINT</value>
</property>
<property>
<name>hadoop.proxyuser.hive.hosts</name>
<value>*</value>
</property>
<property>
<name>hadoop.spnego.allowed.ips</name>
<value>.*</value>
</property>
<property>
<name>hadoop.security.instrumentation.requires.admin</name>
<value>true</value>
</property>
<property>
<name>fs.obs.impl</name>
<value>org.apache.hadoop.fs.obs.OBSFileSystem</value>
</property>
<property>
<name>dfs.encrypt.data.transfer</name>
<value>false</value>
</property>
<property>
<name>ipc.client.ping</name>
<value>true</value>
</property>
<property>
<name>fs.nas.impl</name>
<value>com.huawei.nasfilesystem.ShareNASFileSystem</value>
</property>
<property>
<name>fs.obs.access.key</name>
<value></value>
</property>
<property>
<name>fs.obs.endpoint</name>
<value></value>
</property>
<property>
<name>fs.obs.server-side-encryption-key</name>
<value></value>
</property>
<property>
<name>cas.conf.serverName.off</name>
<value>https://10-160-60-12:21301</value>
</property>
<property>
<name>hadoop.http.server.MaxRequests</name>
<value>2000</value>
</property>
<property>
<name>hadoop.ssl.require.client.cert</name>
<value>false</value>
</property>
<property>
<name>hbase.mob.file.compaction.batch.size</name>
<value>100</value>
</property>
<property>
<name>hadoop.security.openssl.engin.id</name>
<value>kae</value>
</property>
<property>
<name>cas.conf.casServerUrlPrefix.on</name>
<value>https://************:20027/cas/</value>
</property>
<property>
<name>hbase.mob.delfile.max.count</name>
<value>3</value>
</property>
<property>
<name>hadoop.security.key.provider.path</name>
<value></value>
</property>
<property>
<name>hadoop.security.authorization</name>
<value>true</value>
</property>
<property>
<name>hadoop.proxyuser.loader.hosts</name>
<value>*</value>
</property>
<property>
<name>ipc.client.connect.timeout</name>
<value>20000</value>
</property>
<property>
<name>fs.obs.write.buffer.size</name>
<value>8192</value>
</property>
<property>
<name>cas.conf.casServerUrlPrefix.off</name>
<value>https://************:20009/cas/</value>
</property>
<property>
<name>hadoop.security.authentication</name>
<value>kerberos</value>
</property>
<property>
<name>fs.obs.buffer.dir</name>
<value>${BIGDATA_HOME}/tmp/HBase/obs</value>
</property>
<property>
<name>mapreduce.job.hdfs-servers</name>
<value>hdfs://hacluster,hdfs://hacluster</value>
</property>
<property>
<name>hadoop.proxyuser.hue.hosts</name>
<value>*</value>
</property>
<property>
<name>fs.obs.readahead.range</name>
<value>2097152</value>
</property>
<property>
<name>hadoop.security.token.service.use_ip</name>
<value>true</value>
</property>
<property>
<name>hadoop.proxyuser.thrift.hosts</name>
<value>*</value>
</property>
<property>
<name>hadoop.security.crypto.implementation.class</name>
<value>com.huawei.hadoop.datasight.security.FMHadoopCryptAdapter</value>
</property>
<property>
<name>hadoop.proxyuser.hue.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.proxyuser.hive.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.proxyuser.thrift.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.http.authentication.center</name>
<value>https://************:20009/cas/</value>
</property>
<property>
<name>fs.defaultFS</name>
<value>hdfs://hacluster</value>
</property>
<property>
<name>fs.obs.server-side-encryption-type</name>
<value>NONE</value>
</property>
<property>
<name>hadoop.groupname</name>
<value>hadoop</value>
</property>
<property>
<name>fs.nas.mount.dir</name>
<value>/mnt/nfsdata0</value>
</property>
<property>
<name>hadoop.proxyuser._default_proxy_user_.groups</name>
<value>*</value>
</property>
<property>
<name>hadoop.proxyuser.pollux.groups</name>
<value>*</value>
</property>
<property>
<name>fs.AbstractFileSystem.nas.impl</name>
<value>com.huawei.nasfilesystem.WushanFs</value>
</property>
<property>
<name>hbase.admin.acl</name>
<value>hbase</value>
</property>
<property>
<name>cas.proxy.switch</name>
<value>true</value>
</property>
<property>
<name>dfs.encrypt.data.transfer.algorithm</name>
<value>3des</value>
</property>
<property>
<name>hadoop.http.kerberos.internal.spnego.keytab</name>
<value>/opt/huawei/Bigdata/FusionInsight_HD_6.5.1.6/install/FusionInsight-HBase-1.3.1/keytabs/HBase/hbase.keytab</value>
</property>
<property>
<name>hadoop.ssl.hostname.verifier</name>
<value>DEFAULT</value>
</property>
<property>
<name>hadoop.security.crypto.codec.classes.aes.ctr.nopadding</name>
<value>org.apache.hadoop.crypto.OpensslAesCtrCryptoCodec,org.apache.hadoop.crypto.JceAesCtrCryptoCodec</value>
</property>
<property>
<name>hadoop.http.authentication.logout</name>
<value>https://************:20009/cas/logout</value>
</property>
<property>
<name>hbase.mob.cache.evict.remain.ratio</name>
<value>0.5</value>
</property>
<property>
<name>hadoop.ssl.server.conf</name>
<value>ssl-server.xml</value>
</property>
<property>
<name>fs.obs.read.buffer.size</name>
<value>8192</value>
</property>
<property>
<name>fs.AbstractFileSystem.obs.impl</name>
<value>org.apache.hadoop.fs.obs.OBS</value>
</property>
<property>
<name>hadoop.proxyuser.omm.hosts</name>
<value>*</value>
</property>
<property>
<name>cas.conf.casServerLoginUrl</name>
<value>https://************:20009/cas/login</value>
</property>
<property>
<name>hbase.mob.cache.evict.period</name>
<value>3600</value>
</property>
<property>
<name>hadoop.http.server.name</name>
<value>https://10-160-60-12:21301</value>
</property>
<property>
<name>ha.zookeeper.session-timeout.ms</name>
<value>45000</value>
</property>
<property>
<name>hadoop.rpc.protection</name>
<value>privacy</value>
</property>
</configuration>
