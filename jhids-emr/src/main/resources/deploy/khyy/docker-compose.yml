version: "3"
services:
  jhidc-is:
    image: java:8
    restart: always
    hostname: "jhidc-is"
    container_name: jhidc-is
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /jhmk/container/jhidc/jhidc-is:/usr/jhidc-is
      - /jhmk/container/jhidc/jhidc-is/logs:/data/container/jhidc/jhidc-is/logs
      - /etc/localtime:/etc/localtime
    working_dir: /usr/jhidc-is
    network_mode: "host"
    ports:
      - "9054:9054"
    extra_hosts:
      - "jhmk01:*************"
      - "jhmk02:*************"
      - "jhmk03:*************"
      - "jhmk04:*************"
      - "jhmk05:*************"
      - "jhidc-is:127.0.0.1"
    command: java -Djava.security.egd=file:/dev/./urandom -jar -Dfile.encoding=UTF-8 -Dspring.profiles.active=khyy -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -Xms1024m -Xmx2048m jhidc-is-0.0.1-SNAPSHOT.jar
