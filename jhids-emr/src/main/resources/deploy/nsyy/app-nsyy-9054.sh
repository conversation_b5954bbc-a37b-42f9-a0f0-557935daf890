#!/bin/sh

#设置Java运行环境
JAVA_HOME=/usr/java/jdk1.8.0_231-amd64
JAVA=$JAVA_HOME/bin/java
CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar

#这里可替换为你自己的执行程序，其他代码无需更改
APP_PORT=9054
APP_PATH=/data/SpringCloud/jhidc-is-service/$APP_PORT
APP_NAME=$APP_PATH/jhidc-is-0.0.1-$APP_PORT.jar
APP_ENV=nsyy

#使用说明，用来提示输入参数
usage() {
    echo "Usage: sh 执行脚本.sh [start|stop|restart|status]"
    exit 1
}
#检查程序是否在运行
is_exist(){
  pid=`ps -ef|grep $APP_NAME|grep -v grep|awk '{print $2}' `
  #如果不存在返回1，存在返回0
  if [ -z "${pid}" ]; then
   return 1
  else
    return 0
  fi
}

#启动方法
start(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "${APP_NAME} is already running. pid=${pid} ."
  else
    if [ ! -d "$APP_PATH/tmpdir" ]; then
      mkdir -p $APP_PATH/tmpdir
    fi
    nohup $JAVA -Djava.io.tmpdir=$APP_PATH/tmpdir -Djava.ext.dirs=$JAVA_HOME/jre/lib/ext -Dfile.encoding=UTF-8 -Dspring.profiles.active=$APP_ENV -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -Xms512m -Xmx512m -Xmn256m -jar $APP_NAME --server.port=$APP_PORT >> app.out 2>&1 &
    status
  fi
}

#停止方法
stop(){
  is_exist
  if [ $? -eq "0" ]; then
    kill -9 $pid
    status
  else
    echo "${APP_NAME} is not running"
  fi
}

#输出运行状态
status(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "${APP_NAME} is running. Pid is ${pid}"
  else
    echo "${APP_NAME} is not running."
  fi
}

#重启
restart(){
  stop
  start
}

#根据输入参数，选择执行对应方法，不输入则执行使用说明
case "$1" in
  "start")
    start
    ;;
  "stop")
    stop
    ;;
  "status")
    status
    ;;
  "restart")
    restart
    ;;
  *)
    usage
    ;;
esac
