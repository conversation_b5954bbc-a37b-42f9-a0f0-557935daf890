jhdl:
  project:
    name: jhidc-is

server:
  port: 9054
  servlet:
    context-path: /jhidc/is

spring:
  application:
    name: jhidc-is-service
  cloud:
    nacos:
      discovery:
        # 指定nacos server的地址
        server-addr: jhmk01:9048
  # Redis Config
  redis:
    host: jhmk03
    port: 9079
    password: Jhmk@16B03
  # DataSource Config
  datasource:
    dynamic:
      primary: JHSM #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      druid: #以下是全局默认值，可以全局更改
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        max-active: 20
        # 配置获取连接等待超时的时间
        max-wait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        time-between-eviction-runs-millis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        min-evictable-idle-time-millis: 300000
      datasource:
        #系统管理数据库
        JHSM:
          url: *********************************************************************************************************
          username: root
          password: Jhmk.1234
          driver-class-name: com.mysql.cj.jdbc.Driver
        #数据索引数据库
        JHDL:
          url: ****************************************************************************************************************************
          username: root
          password: Jhmk.1234
          driver-class-name: com.mysql.cj.jdbc.Driver
        #基础数据库
        JHBAS:
          url: **********************************************************************************************************
          username: root
          password: Jhmk.1234
          driver-class-name: com.mysql.cj.jdbc.Driver

  quartz:
    job-store-type: jdbc #数据库方式
    jdbc:
      initialize-schema: never #不初始化表结构
    properties:
      org:
        quartz:
          scheduler:
            instanceId: AUTO #默认主机名和时间戳生成实例ID,可以是任何字符串，但对于所有调度程序来说，必须是唯一的 对应qrtz_scheduler_state INSTANCE_NAME字段
            #instanceName: clusteredScheduler #quartzScheduler
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX #数据保存方式为数据库持久化
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate #我们仅为数据库制作了特定于数据库的代理
            useProperties: false #以指示JDBCJobStore将JobDataMaps中的所有值都作为字符串，因此可以作为名称 - 值对存储而不是在BLOB列中以其序列化形式存储更多复杂的对象。从长远来看，这是更安全的，因为您避免了将非String类序列化为BLOB的类版本问题。
            tablePrefix: QRTZ_  #数据库表前缀
            misfireThreshold: 60000 #在被认为“失火”之前，调度程序将“容忍”一个Triggers将其下一个启动时间通过的毫秒数。默认值（如果您在配置中未输入此属性）为60000（60秒）。
            clusterCheckinInterval: 5000 #设置此实例“检入”*与群集的其他实例的频率（以毫秒为单位）。影响检测失败实例的速度。
            isClustered: false #打开群集功能
          threadPool: #连接池
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 20
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true

etl-config:
  #ETL批量处理提交数
  batch-size: 1000
  #任务处理过程中打印任务日志时间间隔，单位秒
  task-log-time: 60

# Logger Config
logger:
  path: /data/container/jhidc/jhidc-is/logs
  level: info
  maxHistory:
    #单位为天
    info: 3
    error: 30

management:
  endpoints:
    web:
      exposure:
        include: "*"