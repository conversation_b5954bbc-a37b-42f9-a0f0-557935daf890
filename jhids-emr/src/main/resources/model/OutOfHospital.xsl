<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
  <xsl:template match="/">
    <html>
      <head>
        <link rel="stylesheet"  href="/jhids/emr/static/css/style.css" />
        <script type="text/javascript" src="/jhids/emr/static/js/style.js"></script>
      </head>
      <body>
        <!--定义中间变量用于获取年龄-->
        <xsl:param   name = "year">
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite">
            <xsl:if test="Code/@Code = 'VK3' ">
              <xsl:value-of select="Text"/>
            </xsl:if>
          </xsl:for-each>
        </xsl:param>
        <xsl:if test="$year != '' ">
          <h3 align="center">24 小 时 入 出 院 记 录</h3>
        </xsl:if>
        <xsl:if test="$year = '' ">
          <h3 align="center">出 院 记 录</h3>
        </xsl:if>
        <table class="baseInfo" id="baseInfocolor1" style="width:100%">
          <th  colspan="8" >患者一般项目</th>
          <tr>
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIString">

                <xsl:if test="Code/@Code = 'L010' ">
                  <td width="80px"><strong>姓名：</strong></td>
                  <td width="100px" colspan="2"><xsl:value-of select="."/></td>
                </xsl:if>

                <xsl:if test="Code/@Code = 'L011' ">
                  <td><strong>病案号：</strong></td>
                  <td colspan="4"><xsl:value-of select="."/></td>
                </xsl:if>

            </xsl:for-each>
          </tr>

          <tr>
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIDictionary">
              <xsl:if test="Code/@Code = 'I002' ">
                <td><strong>性别：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
              </xsl:if>
            </xsl:for-each>
            <xsl:if test="$year != '' ">
              <td><strong>年龄：</strong></td>
              <td colspan="2"><xsl:value-of select="$year"/></td>
            </xsl:if>
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIString">
              <xsl:if test="Code/@Code = 'L003' ">
                <td><strong>年龄：</strong></td>
                <td colspan="2"><xsl:value-of select="."/></td>
              </xsl:if>

              <xsl:if test="Code/@Code = 'L016' ">
                <td><strong>职业：</strong></td>
                <td ><xsl:value-of select="."/></td>
              </xsl:if>
            </xsl:for-each>
          </tr>


          <tr>
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIDateTime">
              <xsl:if test="Code/@Code = 'D001' ">
                <td><strong>入院日期：</strong></td>
                <td><xsl:value-of select="Value"/></td>
              </xsl:if>
              <xsl:if test="Code/@Code = 'D002' ">
                <td><strong>出院日期：</strong></td>
                <td colspan="4"><xsl:value-of select="Value"/></td>
              </xsl:if>
            </xsl:for-each>
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIMonoChoice">
              <xsl:if test="Code/@Code = 'O002' ">
                <td><strong>病史陈述者：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
              </xsl:if>
              <xsl:if test="Code/@Code = 'O003' ">
                <td><strong>可靠程度：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
              </xsl:if>
            </xsl:for-each>
          </tr>
        </table>
        <table class="altrowstable" id="alternatecolor1">
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section">
            <xsl:if test="Code/@DisplayName != '页眉' and Code/@DisplayName != '页脚' ">
              <xsl:if test="Code/@DisplayName != '患者一般项目' and Code/@DisplayName != '基本信息' and  Code/@Code != 'S010'">
                <tr>
                  <td width="80px"><strong><xsl:value-of select="Code/@DisplayName"/></strong></td>
                  <td><xsl:value-of select="Text"/></td>
                </tr>
              </xsl:if>
            </xsl:if>
            <xsl:if test="Code/@DisplayName != '页眉' and Code/@DisplayName != '页脚' ">
              <xsl:if test="Code/@Code = 'S010'">
                <xsl:if test="$year != '' ">
                  <td width="80px"><strong>医师签名</strong></td>
                  <td><xsl:value-of select="substring(Text,6,20)"/></td>
                </xsl:if>
                <xsl:if test="$year = '' ">
                  <td width="80px"><strong>医师签名</strong></td>
                  <td><xsl:value-of select="Text"/></td>
                </xsl:if>
              </xsl:if>
            </xsl:if>
          </xsl:for-each>
        </table>
      </body>
    </html>
  </xsl:template>
</xsl:stylesheet>
