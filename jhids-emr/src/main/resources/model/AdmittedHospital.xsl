<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
  <xsl:template match="/">
    <html>
      <head>
        <link rel="stylesheet"  href="/jhids/emr/static/css/style.css" />
        <script type="text/javascript" src="/jhids/emr/static/js/style.js"></script>
      </head>
      <body>
        <h3 align="center">入 院 记 录</h3>
        <table class="baseInfo" id="baseInfocolor">
          <th  colspan="8" >患者基本信息</th>
          <tr>
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIString">
            <xsl:if test="Code/@Code != 'L013' and Code/@Code != 'L043' and Code/@Code != 'L044' and Code/@Code != 'L045' and Code/@Code != 'L046'">

            <xsl:if test="Code/@Code = 'L001' ">
                <td width="80px"><strong>姓名：</strong></td>
                <td width="200px"><xsl:value-of select="."/></td>
            </xsl:if>

                <xsl:if test="Code/@Code = 'L012' ">
                    <td><strong>病案号：</strong></td>
                    <td><xsl:value-of select="."/></td>
                </xsl:if>
              <xsl:if test="Code/@Code = 'L047' ">
                  <td><strong>病人id：</strong></td>
                  <td colspan="3"><xsl:value-of select="."/></td>
              </xsl:if>
            </xsl:if>
          </xsl:for-each>
          </tr>
          <tr>
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIString">
              <xsl:if test="Code/@Code != 'L013' and Code/@Code != 'L043' and Code/@Code != 'L044' and Code/@Code != 'L045' and Code/@Code != 'L046'">
                <xsl:if test="Code/@Code = 'L015' ">
                  <td><strong>年龄：</strong></td>
                  <td><xsl:value-of select="."/></td>
                </xsl:if>
                <xsl:if test="Code/@Code = 'L016' ">
                  <td><strong>籍贯：</strong></td>
                  <td><xsl:value-of select="."/></td>
                </xsl:if>
                <xsl:if test="Code/@Code = 'L033' ">
                  <td><strong>职业：</strong></td>
                  <td><xsl:value-of select="."/></td>
                </xsl:if>
                <xsl:if test="Code/@Code = 'L052' ">
                  <td><strong>单位或地址：</strong></td>
                  <td><xsl:value-of select="."/></td>
                </xsl:if>
              </xsl:if>
            </xsl:for-each>
          </tr>
          <tr>
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIDictionary">
            <xsl:if test="Code/@Code = 'I007' ">
                <td><strong>科室关联：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
            </xsl:if>
            <xsl:if test="Code/@Code = 'I006' ">
                <td><strong>民族：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
            </xsl:if>
            <xsl:if test="Code/@Code = 'I004' ">
                <td><strong>性别：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
            </xsl:if>
            <xsl:if test="Code/@Code = 'I005' ">
                <td><strong>婚姻：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
            </xsl:if>
            <!--<tr>
              <td><xsl:value-of select="Code/@DisplayName"/></td>
              <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
            </tr>-->
          </xsl:for-each>
          </tr>
          <tr>
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIDateTime">
            <xsl:if test="Code/@Code = 'D001' ">
                <td><strong>入院日期：</strong></td>
                <td><xsl:value-of select="Value"/></td>
            </xsl:if>
            <xsl:if test="Code/@Code = 'D002' ">
                <td><strong>记录日期：</strong></td>
                <td><xsl:value-of select="Value"/></td>
            </xsl:if>
          </xsl:for-each>
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MIMonoChoice">
            <xsl:if test="Code/@Code = 'O002' ">
                <td><strong>病史陈述者：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
            </xsl:if>
            <xsl:if test="Code/@Code = 'O003' ">
                <td><strong>可靠程度：</strong></td>
                <td><xsl:value-of select="Value/Choice/@DisplayName"/></td>
            </xsl:if>
          </xsl:for-each>
          </tr>
        </table>
        <table class="altrowstable" id="alternatecolor">

          <xsl:for-each select="NInstanceData/ScatterData/Component/Section">
            <xsl:if test="Code/@DisplayName != '页眉' and Code/@DisplayName != '页脚' ">
              <xsl:if test="Code/@DisplayName != '辅 助 检 查'
              and Code/@DisplayName != '专 科 情 况'
              and Code/@DisplayName != '体 格 检 查'
              and Code/@DisplayName != '基本信息'
              and Code/@DisplayName != '望闻问切'
              and Code/@Code != 'S012' and Code/@Code != 'S014'">
                <tr>
                  <td><strong><xsl:value-of select="Code/@DisplayName"/></strong></td>
                  <td><xsl:value-of select="Text"/></td>
                </tr>
              </xsl:if>
            </xsl:if>
          </xsl:for-each>

          <xsl:for-each select="NInstanceData/ScatterData/Component/Section">
            <xsl:if test="Code/@DisplayName = '体 格 检 查' ">
              <tr>
                <th colspan="2" align="center"><xsl:value-of select="Code/@DisplayName"/></th>
              </tr>
            </xsl:if>
          </xsl:for-each>
          <!--定义中间变量用于拼接体格检查-->
          <xsl:param   name = "qname">
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MINumber">
              <xsl:if test="Code/@Code = 'N011' ">
                <xsl:value-of select="Value"/>
              </xsl:if>
            </xsl:for-each>
          </xsl:param>
          <xsl:param   name = "qname1">
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MINumber">
              <xsl:if test="Code/@Code = 'N012' ">
                <xsl:value-of select="Value"/>
              </xsl:if>
            </xsl:for-each>
          </xsl:param>
          <xsl:param   name = "qname2">
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MINumber">
              <xsl:if test="Code/@Code = 'N013' ">
                <xsl:value-of select="Value"/>
              </xsl:if>
            </xsl:for-each>
          </xsl:param>
          <xsl:param   name = "qname3">
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MINumber">
              <xsl:if test="Code/@Code = 'N014' ">
                <xsl:value-of select="Value"/>
              </xsl:if>
            </xsl:for-each>
          </xsl:param>
          <xsl:param   name = "qname4">
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite/SubItems/MINumber">
              <xsl:if test="Code/@Code = 'N016' ">
                <xsl:value-of select="Value"/>
              </xsl:if>
            </xsl:for-each>
          </xsl:param>

          <xsl:for-each select="NInstanceData/ScatterData/Component/Section/Composite">
              <xsl:if test="Code/@Code = 'V007' ">
                <tr>
                  <td colspan="2" align="center">
                    <pre>
T:<xsl:value-of select="$qname"/>℃ P:<xsl:value-of select="$qname1"/>次/分 R:<xsl:value-of select="$qname2"/>次/分 BP:<xsl:value-of select="$qname3"/>/<xsl:value-of select="$qname4"/>mmHg SpO2:<xsl:value-of select="Text"/>
                    </pre>
                  </td>
                </tr>
              </xsl:if>
          </xsl:for-each>
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section">
              <xsl:if test="Code/@DisplayName = '体 格 检 查' ">
                <tr>
                  <td colspan="2" width="800px"><xsl:value-of select="Text"/></td>
                </tr>
              </xsl:if>
          </xsl:for-each>
          <xsl:for-each select="NInstanceData/ScatterData/Component/Section">
               <xsl:if test="Code/@DisplayName = '专 科 情 况' ">
                 <tr>
                   <th colspan="2" align="center"><xsl:value-of select="Code/@DisplayName"/></th>
                 </tr>
                 <tr>
                   <td colspan="2" width="800px"><xsl:value-of select="Text"/></td>
                 </tr>
               </xsl:if>
               <xsl:if test="Code/@DisplayName = '辅 助 检 查' ">
                 <tr>
                   <th colspan="2" align="center"><xsl:value-of select="Code/@DisplayName"/></th>
                 </tr>
                 <tr>
                   <td colspan="2" width="800px"><xsl:value-of select="Text"/></td>
                 </tr>
               </xsl:if>
          </xsl:for-each>
            <xsl:for-each select="NInstanceData/ScatterData/Component/Section">
                <xsl:if test="Code/@DisplayName != '页眉' and Code/@DisplayName != '页脚' ">
                    <xsl:if test="Code/@Code = 'S012' or Code/@Code = 'S014'">
                        <tr>
                            <td><strong><xsl:value-of select="Code/@DisplayName"/></strong></td>
                            <td><xsl:value-of select="Text"/></td>
                        </tr>
                    </xsl:if>
                </xsl:if>
            </xsl:for-each>
        </table>
      </body>
    </html>
  </xsl:template>
</xsl:stylesheet>
