<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
  <xsl:template match="/">
    <html>
      <head>
        <link rel="stylesheet"  href="/jhids/emr/static/css/style.css" />
        <script type="text/javascript" src="/jhids/emr/static/js/style.js"></script>
      </head>
      <body>
        <h3>入 院 记 录</h3>
        <table class="altrowstable" id="alternatecolor">
          <tr>
            <th width="65px">病 历 项</th><th>描 述</th>
          </tr>
          <xsl:for-each select="NInstanceData/SubItems/NISimple">
            <tr>
              <xsl:choose>
                <xsl:when test="Code = 'I0065'">
                  <td>医师签名：</td>
                </xsl:when>
                <xsl:otherwise>
                  <td><xsl:value-of select="Code"/></td>
                </xsl:otherwise>
              </xsl:choose>
              <td><xsl:value-of select="Value"/></td>
            </tr>
          </xsl:for-each>
          <xsl:for-each select="NInstanceData/SubItems/NITextDesc">
            <tr>
              <xsl:if test="Code = 'E0013'">
                <td>主 诉：</td>
              </xsl:if>
              <xsl:if test="Code = 'E0014'">
                <td>现病史：</td>
              </xsl:if>
              <xsl:if test="Code = 'E0015'">
                <td>待 定：</td>
              </xsl:if>
              <xsl:if test="Code = 'E0082'">
                <td>待 定：</td>
              </xsl:if>
              <xsl:if test="Code = 'E0083'">
                <td>待 定：</td>
              </xsl:if>
              <xsl:choose>
                <xsl:when test="Code = 'E0015'">
                  <td><xsl:value-of select="SubItems/NISegment/SubItems/NIFree/Value"/></td>
                </xsl:when>
                <xsl:otherwise>
                  <td><xsl:value-of select="SubItems/NIFree/Value"/></td>
                </xsl:otherwise>
              </xsl:choose>
            </tr>
          </xsl:for-each>
          <xsl:for-each select="NInstanceData/SubItems/NISegment">
            <tr>
              <xsl:if test="Code = 'G0016'">
                <td>既往史：</td>
              </xsl:if>
              <xsl:if test="Code = 'G0017'">
                <td>个人史：</td>
              </xsl:if>
              <xsl:if test="Code = 'G0018'">
                <td>婚育史：</td>
              </xsl:if>
              <xsl:if test="Code = 'G0019'">
                <td>家族史：</td>
              </xsl:if>
              <xsl:if test="Code = 'G0076'">
                <td>月经史：</td>
              </xsl:if>
              <xsl:if test="Code = 'G0081'">
                <td>待 定：</td>
              </xsl:if>

              <td>
                  <xsl:call-template name="join">
                    <xsl:with-param name="NISimple1" select="SubItems/NISimple[1]/Value" />
                    <xsl:with-param name="NISimple2" select="SubItems/NISimple[2]/Value" />
                    <xsl:with-param name="NIFree1" select="SubItems/NIFree[1]/Value" />
                    <xsl:with-param name="NISimple3" select="SubItems/NISimple[3]/Value" />
                    <xsl:with-param name="NISimple4" select="SubItems/NISimple[4]/Value" />
                    <xsl:with-param name="NIFree2" select="SubItems/NIFree[2]/Value" />
                    <xsl:with-param name="NISimple5" select="SubItems/NISimple[5]/Value" />
                    <xsl:with-param name="NIFree3" select="SubItems/NIFree[3]/Value" />
                    <xsl:with-param name="NISimple6" select="SubItems/NISimple[6]/Value" />
                    <xsl:with-param name="NIFree4" select="SubItems/NIFree[4]/Value" />
                  </xsl:call-template>

              </td>
            </tr>
          </xsl:for-each>
        </table>

      </body>
    </html>
  </xsl:template>

  <xsl:template name="join">
    <xsl:param name="NISimple1" />
    <xsl:param name="NISimple2" />
    <xsl:param name="NIFree1" />
    <xsl:param name="NISimple3" />
    <xsl:param name="NISimple4" />
    <xsl:param name="NIFree2" />
    <xsl:param name="NISimple5" />
    <xsl:param name="NIFree3" />
    <xsl:param name="NISimple6" />
    <xsl:param name="NIFree4" />
      <xsl:value-of select="$NISimple1" />
      <xsl:value-of select="$NISimple2" />
      <xsl:value-of select="$NIFree1" />
      <xsl:value-of select="$NISimple3" />
      <xsl:value-of select="$NISimple4" />
      <xsl:value-of select="$NIFree2" />
      <xsl:value-of select="$NISimple5" />
      <xsl:value-of select="$NIFree3" />
      <xsl:value-of select="$NISimple6" />
      <xsl:value-of select="$NIFree4" />
  </xsl:template>

</xsl:stylesheet>
