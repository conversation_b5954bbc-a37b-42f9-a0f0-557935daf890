<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhids.emr.mapper.JhPatInfoMapper">



    <select id="queryFileContent" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT
        a.FILE_CONTENT FILECONTENT
        FROM
        V_JH_FILE_CONTENT a
        <where>
            a.FILE_SN_CODE = #{fileSnCode}
        </where>
    </select>
    <select id="queryFileContentSK" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT
        a.EMR_DG_VALUE EMR_DG_VALUE,
        a.EMR_DG_CODE EMR_DG_CODE
        FROM
        HDR_EMR_CONTENT_DG_SK a
        <where>
            a.EMR_PK = #{fileSnCode}
        </where>
    </select>

    <select id="getFileContentSKModel" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT
        a.HTML_TEXT HTML_TEXT
        FROM
        HDR_EMR_DG_HTML_SK a
        <where>
            a.EMR_CLASS_CODE = #{EMR_CLASS_CODE}
            and a.is_enabled ="Y"
        </where>
    </select>
    <select id="getModel" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT
        a.FILE_CONTENT FILECONTENT
        FROM
            V_JH_FILE_CONTENT a
        WHERE
        a.FILE_SN_CODE = (
        SELECT
        b.MR_CONTENT_4
        FROM
            V_JH_IN_MR b
        WHERE
        b.MR_CONTENT_1 = #{fileSnCode})
        union all
        SELECT
            a.FILE_CONTENT FILECONTENT
        FROM
            V_JH_FILE_CONTENT a
        WHERE
                a.FILE_SN_CODE = (
                SELECT
                    b.MR_CONTENT_4
                FROM
                    V_JH_OUT_MR b
                WHERE
                    b.MR_CONTENT_1 = #{fileSnCode})
    </select>
    <update id="updateFileContent" parameterType="java.util.Map">
        UPDATE jh_file_content b
        SET b.file_content = #{byts}
        where
            b.file_sn_code = #{fileSnCode}

    </update>
    <select id="getPatInfo" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT
        b.PATIENT_NAME PATNAME,
        b.ID_CARD_NO IDNO,
        b.HOME_PHONE HOMEPHONE,
        b.NEXT_OF_KIN OTHERNAME,
        b.NEXT_OF_KIN_PHONE OTHERPHONE,
        b.MAILING_ADDRESS BUSSPHONE
        FROM
        V_JH_IN_MR a,
        V_jh_pat_info b
        <where>
            a.PAT_ROWKEY = b.PAT_ROWKEY
        AND a.MR_CONTENT_1 = #{fileSnCode}
        </where>
    </select>

    <select id="getPatInfo2" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT
        b.PATIENT_NAME PATNAME,
        b.ID_CARD_NO IDNO,
        b.HOME_PHONE HOMEPHONE,
        b.NEXT_OF_KIN OTHERNAME,
        b.NEXT_OF_KIN_PHONE OTHERPHONE,
        b.MAILING_ADDRESS BUSSPHONE
        FROM
        V_JH_OUT_MR a,
        V_jh_pat_info b
        <where>
            a.PAT_ROWKEY = b.PAT_ROWKEY
            AND a.MR_CONTENT_1 = #{fileSnCode}
        </where>
    </select>

    <select id="getMrTopic" resultType="java.util.Map" parameterType="java.lang.String">
       SELECT
        A.MR_TOPIC,
        D.PATIENT_NAME AS PATNAME,
        D.ID_CARD_NO AS IDNO,
        D.HOME_PHONE AS HOMEPHONE,
        D.NEXT_OF_KIN OTHERNAME,
        D.NEXT_OF_KIN_PHONE AS OTHERPHONE,
        CASE WHEN C.TemplateID IN(1,232,233,234,237,303,507,704,854,859,892,944,946) THEN (SELECT B.DataValue FROM JHDL.`JHDL_15_EMRinstance.ICompositeSimple` B WHERE B.InstanceDataID = A.HIS_KEY AND B.SectionCode = 'S001' AND B.SimpleItemCode = 'L0071')
        WHEN C.TemplateID IN(38,230) THEN (SELECT B.DataValue FROM JHDL.`JHDL_15_EMRinstance.ICompositeSimple` B WHERE B.InstanceDataID = A.HIS_KEY AND B.SimpleItemCode = 'L0460') END AS BUSSPHONE
        FROM JHPDL.V_JH_IN_MR A
        INNER JOIN JHDL.`JHDL_15_EMRinstance.InstanceData` C ON A.HIS_KEY=C.ID
        INNER JOIN JHPDL.V_JH_PAT_INFO D ON A.PAT_ROWKEY=D.PAT_ROWKEY
        <where>
            A.MR_CONTENT_1 = #{fileSnCode}
            AND C.TemplateID IN (1,232,233,234,237,303,507,704,854,859,892,944,946,38,230)
            AND C.Status = 'Save'
        </where>
    </select>
</mapper>
