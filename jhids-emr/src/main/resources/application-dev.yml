jhdl:
  project:
    name: jhids-emr

server:
  port: 9099
  servlet:
    context-path: /jhids/emr
spring:
  datasource:
    LOCALMYSQL:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ******************************************************************************************************************
      username: jhdcp
      password: jhmk5354(#($
  #datasource:
  #LOCALMYSQL:
  #driver-class-name: com.mysql.cj.jdbc.Driver
  #url: ****************************************************************************************************************
  #username: root
  #password: jhmk5354(#($
  #datasource:
  #LOCALMYSQL:
  #driver-class-name: com.mysql.cj.jdbc.Driver
  #url: *****************************************************************************************************************
  # username: root
  #password: jhmk5354(#($
  #  datasource:
  #    LOCALMYSQL:
  #    driver-class-name: oracle.jdbc.driver.OracleDriver
  #    url: ****************************************
  #    username: zjk
  #    password: Jiahemk_8IK
  application:
    name: jhids-emr-service
  cloud:
    nacos:
      discovery:
        # 指定nacos server的地址
        server-addr: 183.62.250.66:58048
  mvc:
    static-path-pattern: /static/**
  resources:
    static-locations: classpath:/static,classpath:/public,classpath:/resources,classpath:/META-INF/resources

ribbon:
  ReadTimeout: 20000
  ConnectTimeout: 300000

etl-config:
  #ETL批量处理提交数
  batch-size: 1000
  #任务处理过程中打印任务日志时间间隔，单位秒
  task-log-time: 1

# Logger Config
logger:
  path: d:/logs/jhids-emr
  level: info
  maxHistory:
    #单位为天
    info: 1
    error: 1