package com.jhids.emr.contants;

public class FORMATTERS {

    public final static String EMR_MODEL_FORMATTER = "%s.xsl";
    public final static String APPLY_URL_FORMATTER = "http://jhids4s-service/jhids4s/fbc/%s";
   /* public final static String MODEL_URL_FORMATTER = "jhids-emr\\src\\main\\resources\\model\\%s";*/

    //由于在windows和linux下路径的差异，在本地测试的时候用以下路径
   public final static String MODEL_URL_FORMATTER = "src\\main\\resources\\model\\%s";
    //由于在windows和linux下路径的差异，打包到服务器上的时候用以下路径
    /*public final static String MODEL_URL_FORMATTER = "/usr/local/jhids-emr/model/%s";*/

}
