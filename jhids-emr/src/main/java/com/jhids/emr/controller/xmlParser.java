/**
 * 
 */
package com.jhids.emr.controller;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;


/**
 * <AUTHOR>
 *
 */
public class xmlParser {
	private static String fileName;
	private static Map<String, String>imgMap;

	/**
	 * @return the fileName
	 */
	public static String getFileName() {
		return fileName;
	}
	/**
	 * @param fileName the fileName to set
	 */
	
	public static void setFileName(String fileName) {
		xmlParser.fileName = fileName;
	}
	/**
	 * @return the imgMap
	 */
	public static Map<String, String> getImgMap() {
		return imgMap;
	}
	/**
	 * @param imgMap the imgMap to set
	 */
	public static void setImgMap(Map<String, String> imgMap) {
		xmlParser.imgMap = imgMap;
	}

	public static void main(String[] args) {
		//getXml();
		//stringTransform(getXml());
		
		//Doc_[2018-8-24 15^37^51~1535096271]  SAVE_DOCUMENT_5352842--1
		/*System.out.println("输入文件名：");
		Scanner scanner=new Scanner(System.in);*/
		String filePath="C:\\Users\\<USER>\\Desktop\\test\\PDL.xml";
		System.out.println(filePath);
		stringToHtml(filePath);
		/*scanner.close();*/
		
		 /* String dsrcl = "<xx>11111<xx>[qqq]<xx>2222<xx>[wwww]<xx>3333";
	        dsrcl = dsrcl.replaceAll(">\\[.*?]",">");
	        dsrcl = dsrcl.replaceAll("\\(.*?\\)|\\{.*?}|\\[.*?]|（.*?）", "");
	        System.out.println(dsrcl);*/


	}
	public static String getXml(String filePath){
		String classCssString="";
		String headString=null;
		String bodyString=null;
		String footString=null;
		SAXReader reader = new SAXReader();
		try {
			Document document = reader.read(new File(filePath));
			//Document document = reader.read(new File("C:/Users/<USER>/Desktop/新建文本文档.xml"));
			Element root = document.getRootElement();
			//System.out.println(root.getName());
			//获取文件名
			/*Attribute fname=root.element("Code").attribute("DisplayName");*/
			xmlParser.setFileName("入院记录");
			//遍历List的方法
			/*List<Element> list = root.elements() ;
			 for (Element e:list){
			// System.out.println(e.getName());
			 }*/
			//abiword下的元素
/*
			List<Element> styles = root.element("NInstanceData").element("abiword").element("styles").elements();
*/
			String aa = root.attribute("InstanceID").getText();
			Element section = root.element("abiword");
			if(root.element("abiword").element("data")!=null&&
					!root.element("abiword").element("data").elements().isEmpty()){
				List<Element> datas = root.element("abiword").element("data").elements();
				xmlParser.setImgMap(getImage(datas));
			}
			//获取图片信息的map
			for (Iterator i = section.elementIterator(); i.hasNext();) {
				Element el = (Element) i.next();
				if("section".equals(el.getName())){
					for(Iterator j = el.elementIterator(); j.hasNext();){
						Element el2 = (Element) j.next();
						if("table".equals(el2.getName())){
							/*System.out.println(el2.asXML());*/
							//table上添加固定属性
							el2.addAttribute("border", "0");
							el2.addAttribute("cellpadding", "0");
							if(null ==el2.attribute("props")){
								el2.addAttribute("props","table-column-props:8.62cm/8.74cm/; table-row-heights:0.62cm/; table-column-leftpos:0.00cm; list-tag:1;width:900px;border-collapse:collapse;table-layout:fixed");

							}else{
								String props=el2.attribute("props").getValue();
								el2.attribute("props").setValue(props+";width:900px;border-collapse:collapse;table-layout:fixed");
							}
							//table里的标签格式进行转换
							String lastTopNum=null;
							lastTopNum=tableParser(el2.elements(),lastTopNum,el2);
						}
					}
				}
				// System.out.println(el.asXML());
			}

			/* for(Element e:sections){
				 if("table".equals(e.getName())){
					 System.out.println(e.asXML());
					// tableParser(e.elements());
				 }

			 }*/
			List<Element> abiword = root.element("abiword").elements();
			// System.out.println(root.asXML());
			//根据<styles>生成class样式
			/*for (Element e:styles){
				String className= e.attribute("name").getValue();
				String css= e.attribute("props").getValue();
				classCssString+="."+className+"{\n"+css+"\n}";
			}*/
			//根据type属性对应生成head,body,footer
			for (Element e:abiword){
				// System.out.println(e.getName());
				if("section".equals(e.getName())){
					// System.out.println(e.asXML());
					List<Attribute> listAttr=e.attributes();//当前节点的所有属性的list
					for(Attribute attr:listAttr){//遍历当前节点的所有属性
						String name=attr.getName();//属性名称
						String value=attr.getValue();//属性的值
						// System.out.println("属性名称："+name+"属性值："+value);
						if("type".equals(name)&&"header".equals(value)){
							// System.out.println(e.asXML());
							headString= "<!DOCTYPE HTML PUBLIC '-//W3C//DTD HTML 4.01//EN' 'http://www.w3.org/TR/html4/strict.dtd'>\n"
									+ "<html>\n"
									+ "<head>\n"
									+ "<meta http-equiv='Content-Type' content='text/html; charset=utf-8' />\n"
									+"<title>"+xmlParser.getFileName()+"</title>\n"
									+"<style type='text/css'>\n"
									+"body{\n"
									+"padding-top : 1in;\n"
									+"padding-bottom : 1in;\n"
									+"padding-left : 1in;\n"
									+"padding-right : 1in;\n"
									+"}\n"
									+classCssString
									+"</style>\n"+e.asXML()+"\n</head>\n";
						}else if("type".equals(name)&&"footer".equals(value)){
							footString="<footer>\n"+e.asXML()+"\n</footer>\n</html>";
						}else if (e.attribute("type")==null){
							bodyString="<body>\n"+e.asXML()+"\n</body>\n";
						}
					}
				}
			}


		} catch (DocumentException e) {
			e.printStackTrace();
		}
		return headString+bodyString+footString;
	}

	public static String tableParser(List<Element> tables,String lastTopNum,Element el2){
		if(tables!=null){
			Element tr=null;
			StringBuffer nodeStr=new StringBuffer();
			int index=0;
			for (Element e:tables){
				System.out.println(e.asXML());
				List<String> strList1=new ArrayList<>();
				List<String> strList2=new ArrayList<>();
				//把线条粗细的属性提到前边不然不会生效 ----strat
				String propss=e.attribute("props").getValue();
				String[] strs=propss.split(";");
				for(int i=0;i<strs.length;i++){
					strList1.add(strs[i]);
				}
				for(Iterator<String> it = strList1.iterator();it.hasNext();){
					String s1=it.next();
					if(s1.contains("left-thickness")||s1.contains("right-thickness")||
							s1.contains("top-thickness")||s1.contains("bot-thickness")){
						strList2.add(s1);
						it.remove();
					}
				}
				strList2.addAll(strList1);
				String newProps=strList2.toString().replace("[", "").replace("]", "").replaceAll(",", ";");
				e.attribute("props").setValue(newProps);
				//把线条粗细的属性提到前边不然不会生效----end
				String props=e.attribute("props").getValue();
				String[] str1=props.split(";");
				String leftNum = null;
				String rightNum=null;
				String topNum=null;
				String botNum=null;
				for(int i=0;i<str1.length;i++){
					if(str1[i].contains("left-thickness")||str1[i].contains("right-thickness")||
							str1[i].contains("top-thickness")||str1[i].contains("bot-thickness")){
					}
					if(str1[i].contains("top-attach")){
						topNum=str1[i].split(":")[1];
						e.addAttribute("row",topNum );
					}else if(str1[i].contains("left-attach")){
						leftNum=str1[i].split(":")[1];
						e.addAttribute("column", leftNum);
					}else if(str1[i].contains("right-attach")){
						/* rowspan="" colspan="6"*/
						rightNum=str1[i].split(":")[1];
					}else if(str1[i].contains("bot-attach")){
						botNum=str1[i].split(":")[1];
						/* rowspan="" colspan="6"*/
					}
					if(rightNum!=null&&leftNum!=null){
						e.addAttribute("colspan",  (Integer.parseInt(rightNum)- Integer.parseInt(leftNum))+"");

					}
					if(topNum!=null&&botNum!=null){
						e.addAttribute("rowspan",  (Integer.parseInt(botNum)- Integer.parseInt(topNum))+"");

					}
				}
				if(topNum.equals(lastTopNum)&&tr!=null){
					nodeStr.append(e.asXML()+"\n");
					el2.remove(e);
				}else{
					if(nodeStr.length()!=0){
						tr.setText(nodeStr.toString());
						nodeStr.setLength(0);
						el2.remove(e);
					}
					tr=el2.addElement("tr");
					if(nodeStr.length()==0){
						nodeStr.append(e.asXML()+"\n");
						el2.remove(e);
					}
					//System.out.println(e.asXML());

				}
				lastTopNum=topNum;
				//最后一次必须把内容设进去
				index++;
				if(index==tables.size()){
					tr.setText(nodeStr.toString());
					
				}
			}
		}
		return lastTopNum;

	}
	public static String stringTransform(String string){
		String newString;
		String newString1=string.replaceAll("&lt;","<").replaceAll("&gt;",">").replaceAll("style=", "class=")
				.replaceAll("props=", "style=").replaceAll("<cell", "<td").replaceAll("</cell>", "</td>")
				.replaceAll("bgcolor:", "background:#").replaceAll("<c", "<span").replaceAll("</c>", "</span>")
				.replaceAll("left-color:", "border-left-color:#").replaceAll("right-color:", "border-right-color:#").replaceAll("bot-color:", "border-bottom-color:#")
				.replaceAll("top-color:", "border-top-color:#").replaceAll("left-thickness", "border-left").replaceAll("right-thickness", "border-right")
				.replaceAll("top-thickness", "border-top").replaceAll("bot-thickness", "border-bottom").replaceAll("left-style:1", "border-left-style:solid")
				.replaceAll("right-style:1", "border-right-style:solid").replaceAll("bot-style:1", "border-bottom-style:solid").replaceAll("top-style:1", "border-top-style:solid")
				.replaceAll(">\\[.*?]",">");
		String newString2=null;
		if(xmlParser.getImgMap()!=null&&!xmlParser.getImgMap().isEmpty()){
			for (Map.Entry<String, String> entry : xmlParser.getImgMap().entrySet()) { 
				if(newString1.indexOf("image dataid=\""+entry.getKey())>0){
					String sda="image src='data:image/jpg;base64,"+entry.getValue();
					newString2=newString1.replaceAll("image dataid=\""+entry.getKey(), "image src=\"data:image/jpg;base64,"+entry.getValue())
							.replaceAll("<math", "<a").replaceAll("</math>", "</a>");
				}
				
			}
			newString=newString2;
		}else {
			newString=newString1;
		}
		
		return newString;
	}
	//获取图片
	public static Map<String, String> getImage(List<Element> datas){
		Map<String,String> imgMap=new HashMap<>();
		for(Element e:datas){
			String dataId=e.attribute("name").getValue();
			String dataText=e.getText();
			imgMap.put(dataId, dataText);
		}
		return imgMap;
		//List<Element> abiword = root.element("StructuredBody").element("NInstanceData").element("abiword").elements();
	}

	public static void stringToHtml(String filePath) {
		BufferedReader br = null;
		PrintWriter out = null;
		String xmlStr=getXml(filePath);
		Object newString=stringTransform(xmlStr);
		try {
			// br = new BufferedReader(new InputStreamReader(System.in));
			//OutputFormat format = OutputFormat.createPrettyPrint();
			out = new PrintWriter(new BufferedWriter(new FileWriter("C:/Users/<USER>/Desktop/"+xmlParser.getFileName()+"_"+System.currentTimeMillis()+".html")));
			String str = null;
			/*while((str = br.readLine())!=null){
	                if (str.equals("exit")) {
	                    System.out.println("thank you");
	                    break;
	                }
	                out.println(str);
	            }*/
			out.print(newString);
			//out.println(newString);
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			out.close();
			try {
				if(br!=null) br.close();
			} catch (IOException e) {
				e.printStackTrace();
			}

			try {
				if(out!=null) out.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}



	}
	


}

