package com.jhids.emr.controller;

import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.nacos.client.identify.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.shaded.org.apache.commons.lang.exception.ExceptionUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
@Slf4j
public class xmlHomeToHtml {



	//文件名
	private static String fileName;
	//单元格大小
	private static int cellSize;

	public static String getFileName() {
		return fileName;
	}
	public static void setFileName(String fileName) {
		xmlHomeToHtml.fileName = fileName;
	}

	public static int getCellSize() {
		return cellSize;
	}

	public static String creatHtml(String xmlUrl,String htmlUrl,String watermark,Map<String,Object> patinfoMap,Map<String,Object> patientinfo) throws Exception {
		String documentContent=readString1(xmlUrl);
		FileWriter out = null;
		String htmlString=getHtml(documentContent,watermark,htmlUrl);

		if(null==patinfoMap.get("privacyflag") || !"N".equals(patinfoMap.get("privacyflag"))){
			log.info("进入了脱敏隐私化的判断！！");
			//隐私化处理
			if(null!=patientinfo){
				for(String key :patientinfo.keySet()){
					htmlString=htmlString.replaceAll(key,(String)patientinfo.get(key));
				}
			}
			if(null!=patinfoMap) {
				Set<String> keys = patinfoMap.keySet();
				for (String key : keys) {
					if(!"privacyflag".equals(key)){
						if (htmlString.contains((String) patinfoMap.get(key))) {
						if (null != patinfoMap.get(key)) {
							if ("PATNAME".equals(key)) {
								String value = (new StringBuilder((String) patinfoMap.get(key)).replace(1, ((String) patinfoMap.get(key)).length(), "**")).toString();
								log.info("PATNAME=====" + value);
								htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
							}
							if ("OTHERNAME".equals(key)) {
								if(!StringUtils.isBlank((String)patinfoMap.get(key)) && !"-".equals((String)patinfoMap.get(key))){
									String value = (new StringBuilder((String) patinfoMap.get(key)).replace(1, ((String) patinfoMap.get(key)).length(), "**")).toString();
									htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
								}
							}
							if ("IDNO".equals(key)) {
								if(null!=patinfoMap.get(key) && !"".equals(patinfoMap.get(key))){
									String value="";
									if(((String) patinfoMap.get(key)).length()>10){
										 value = (new StringBuilder((String) patinfoMap.get(key)).replace(10, ((String) patinfoMap.get(key)).length(), "********")).toString();
									}else {
										value = (new StringBuilder((String) patinfoMap.get(key)).replace(3, ((String) patinfoMap.get(key)).length(), "********")).toString();
									}
									htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
								}

							}
							if ("HOMEPHONE".equals(key)) {
								if (((String) patinfoMap.get(key)).length() > 3) {
									String value = (new StringBuilder((String) patinfoMap.get(key)).replace(3, 7, "****")).toString();
									htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
								}
							}
							if ("OTHERPHONE".equals(key)) {
								if (((String) patinfoMap.get(key)).length() > 3) {
									String value = (new StringBuilder((String) patinfoMap.get(key)).replace(3, 7, "****")).toString();
									htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
								}
							}
							if ("BUSSPHONE".equals(key)) {
								if (((String) patinfoMap.get(key)).length() > 3) {
									//*//*String value = (new StringBuilder((String) patinfoMap.get(key)).replace(3, 7, "****")).toString();*//*
									String value = (new StringBuilder((String) patinfoMap.get(key)).replace(((String) patinfoMap.get(key)).length()-4, ((String) patinfoMap.get(key)).length()-2, "****")).toString();
									htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
								}
							}
						}
					}
				}
			}
			}
		}
		try {
			out = new FileWriter( htmlUrl );  //写入文件
			out.write(htmlString);
			return htmlString;
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
		out.close();
	}
	return htmlString;
	}

	public static String readString1(String xmlUrl)

	{
		String str="";

		File file=new File(xmlUrl);

		try {

			FileInputStream in=new FileInputStream(file);

			// size  为字串的长度 ，这里一次性读完

			int size=in.available();

			byte[] buffer=new byte[size];

			in.read(buffer);

			in.close();

			str=new String(buffer);

		} catch (IOException e) {

			// TODO Auto-generated catch block

			return null;

		}

		return str;
	}


	public static String getHtml(String xmlContent,String watermark,String fileName) throws Exception{
		Map<String, String> imageMap =new HashMap<String, String>();
		String htmlContent="";
		//替换原有的转义符号防止后面转义混淆
		xmlContent=xmlContent.replace("&lt;", "@!").replace("&gt;", "@#");
		Document document = DocumentHelper.parseText(xmlContent);
		Element root = document.getRootElement();
		if(root==null){
			return "模版内容为空";
		}
		String documentName=fileName;
		if(root.element("iEditor").elements().size()==0){
			return "模版内容为空";
			//throw new Exception("模版内容为空！");
		}
		//abiword类型模版和实例
		Element abiword =null;
		//iEditor类型模版和实例
		Element iEditor =null;
		boolean abwTempFalg=false;//3版abiwor类型的模板标志（需要把隐藏的展示出来）
		Map<String, String> tempInfo=new HashMap<>();//存放3版iEditor类型模板（病案首页）数据项描述信息

		imageMap=getImage();
		iEditor = root.element("iEditor");
		Element sheet = iEditor.element("Sheets");
		htmlContent = getXmlByiEditor(sheet,documentName,tempInfo,watermark);
		return  stringTransform(htmlContent,imageMap,abwTempFalg);
	}
	/**
	 *
	 * @Title: changeFiled2Radio
	 * @Description: 3版abiword类型模板field标签变单选框(描述)
	 * @param abiword
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2020年2月15日
	 */
	public static void changeFiled2Radio(Element abiword)throws Exception{
		List<Element> fieldElements = abiword.elements("field");
		for(Element fieldElement:fieldElements){
			String type = fieldElement.attributeValue("type");
			String revision = fieldElement.attributeValue("revision")==null?"":fieldElement.attributeValue("revision");
			if("choice_field".equals(type)&&StringUtils.isNumeric(revision)){
				String params=fieldElement.attribute("param").getValue();
				if(!StringUtils.isEmpty(params)){
					Element radio = fieldElement.addElement("input");
					String[] paramArr = params.split(";");
					for(String param:paramArr){
						if(param.indexOf("Context:")>-1){
							if(param.split(":").length>1){
								radio.setText(param.split(":")[1]);
							}
						}
						if(param.indexOf("Value:1")>-1){
							radio.addAttribute("checked", "checked");
						}
						if(param.indexOf("Value:0")>-1){
							radio.addAttribute("disabled", "true");
						}
					}
					radio.addAttribute("type", "radio");
					fieldElement.setName("label");
					fieldElement.addAttribute("style","background:#AEAEAE");
				}
			}

		}
		if(abiword.elements().size()>0){
			for(int i=0;i<abiword.elements().size();i++){
				changeFiled2Radio((Element)abiword.elements().get(i));
			}
		}
	}

	public static String stringTransform(String string,Map<String, String> imageMap,boolean abwTempFalg)
			throws Exception{
		//String newString;
		String newString=string.replaceAll("&lt;","<").replaceAll("&gt;",">").replaceAll("class=", "style=")
				.replaceAll("props=", "style=").replaceAll("<cell", "<td").replaceAll("</cell>", "</td>")
				.replaceAll("bgcolor:", "background:#").replaceAll("<c", "<span").replaceAll("</c>", "</span>")
				.replaceAll("left-color:", "border-left-color:#").replaceAll("right-color:", "border-right-color:#").replaceAll("bot-color:", "border-bottom-color:#")
				.replaceAll("top-color:", "border-top-color:#").replaceAll("left-thickness", "border-left").replaceAll("right-thickness", "border-right")
				.replaceAll("top-thickness", "border-top").replaceAll("bot-thickness", "border-bottom").replaceAll("left-style:1", "border-left-style:solid")
				.replaceAll("right-style:1", "border-right-style:solid").replaceAll("bot-style:1", "border-bottom-style:solid").replaceAll("top-style:1", "border-top-style:solid")
				/*.replaceAll(">\\[.*?]",">")*/  //去掉文本内容中[]以及其中的内容
				.replaceAll("<bookmark","<a").replaceAll("##", "#")//源xml有的颜色带#有的未带前面加过一次这里去掉重复的
				.replace("@!", "&lt;").replace("@#", "&gt;");//还原本来转义过的情况
		if(abwTempFalg){//abi的模板
			newString=newString.replaceAll("display:none", "").replaceAll("\\[\\{\\]", "{")
					.replaceAll("\\[\\}\\]", "}");
		}
		return newString;
	}
	//获取图片信息
	public static Map<String, String> getImage(){
		Map<String,String> imgMap=new HashMap<>();

			String dataId="d4c75d0b-a0e1-495e-95c2-cc1c16dfa6ad";
			String dataText="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";
			imgMap.put(dataId, dataText);
		return imgMap;
		//List<Element> abiword = root.element("StructuredBody").element("NInstanceData").element("abiword").elements();
	}

	public static String tableParser(List<Element> tables,String lastTopNum,Element el2) throws DocumentException{
		if(tables!=null){
			Element tr=null;
			StringBuffer nodeStr=new StringBuffer();
			int index=0;
			for (Element e:tables){
//				System.out.println(e.asXML());
				List<String> strList1=new ArrayList<>();
				List<String> strList2=new ArrayList<>();
				//把线条粗细的属性提到前边不然不会生效 ----strat
				if(e.attribute("props")!=null){
					String propss=e.attribute("props").getValue();
					String[] strs=propss.split(";");
					for(int i=0;i<strs.length;i++){
						strList1.add(strs[i]);
					}
				}
				for(Iterator<String> it = strList1.iterator();it.hasNext();){
					String s1=it.next();
					if(s1.contains("left-thickness")||s1.contains("right-thickness")||
							s1.contains("top-thickness")||s1.contains("bot-thickness")){
						strList2.add(s1);
						it.remove();
					}
				}
				strList2.addAll(strList1);
				String newProps=strList2.toString().replace("[", "").replace("]", "").replaceAll(",", ";");
				if(e.attribute("props")!=null){
					e.attribute("props").setValue(newProps);
				}
				//把线条粗细的属性提到前边不然不会生效----end
				String leftNum = "";
				String rightNum="";
				String topNum="";
				String botNum="";
				if(e.attribute("props")!=null){
					String props=e.attribute("props").getValue();
					String[] str1=props.split(";");
					for(int i=0;i<str1.length;i++){
						if(str1[i].contains("left-thickness")||str1[i].contains("right-thickness")||
								str1[i].contains("top-thickness")||str1[i].contains("bot-thickness")){
						}
						if(str1[i].contains("top-attach")){
							topNum=str1[i].split(":")[1];
							e.addAttribute("row",topNum );
						}else if(str1[i].contains("left-attach")){
							leftNum=str1[i].split(":")[1];
							e.addAttribute("column", leftNum);
						}else if(str1[i].contains("right-attach")){
							/* rowspan="" colspan="6"*/
							rightNum=str1[i].split(":")[1];
						}else if(str1[i].contains("bot-attach")){
							botNum=str1[i].split(":")[1];
							/* rowspan="" colspan="6"*/
						}
						if(!StringUtils.isEmpty(rightNum)&&!StringUtils.isEmpty(leftNum)){
							e.addAttribute("colspan",  (Integer.parseInt(rightNum)- Integer.parseInt(leftNum))+"");
						}
						if(!StringUtils.isEmpty(topNum)&&!StringUtils.isEmpty(botNum)){
							e.addAttribute("rowspan",  (Integer.parseInt(botNum)- Integer.parseInt(topNum))+"");

						}
					}
				}

				if(!StringUtils.isEmpty(topNum)&&topNum.equals(lastTopNum)&&tr!=null){
					nodeStr.append(e.asXML()+"\n");
					el2.remove(e);
				}else{
					if(nodeStr.length()!=0){
						String text = nodeStr.toString();
						tr.setText(text);
						nodeStr.setLength(0);
						el2.remove(e);
					}
					tr=el2.addElement("tr");
					if(nodeStr.length()==0){
						nodeStr.append(e.asXML()+"\n");
						el2.remove(e);
					}
					//System.out.println(e.asXML());

				}
				lastTopNum=topNum;
				//最后一次必须把内容设进去
				index++;
				if(index==tables.size()){
					String text = nodeStr.toString();
					tr.setText(text);
				}
			}
		}
		return lastTopNum;

	}
	/**
	 *
	 * 方法名:getXmlByiEditor
	 * 方法描述: 另一种解析方式（如病案首页）
	 * @param sheet
	 * @param tempInfo
	 * @param documentName
	 * @return
	 * @Author: xcl
	 * @Create Date: 2019年11月29日
	 */
	public static String getXmlByiEditor(Element sheet,String documentName,Map<String, String> tempInfo,String watermark)throws Exception{
		String tableStr=createNewTable(sheet,tempInfo).asXML().replace("&gt;", ">").replace("&lt;", "<");//对选项框添加span后的字符做处理
		//模板中超出内容省略的样式
		String tempStyle="";
		if(tempInfo.size()>0){
			tempStyle="table{\nbackground-color:white;\n}\ntd{\noverflow:hidden;\ntext-overflow:ellipsis;\nwhite-space:nowrap;\n}";
		}
		String htmlString="<!DOCTYPE HTML PUBLIC '-//W3C//DTD HTML 4.01//EN' 'http://www.w3.org/TR/html4/strict.dtd'>\n"
				+ "<html>\n"
				+ "<head>\n"
				+ "<meta http-equiv='Content-Type' content='text/html; charset=utf-8' />\n"
				+"<title>"+documentName+"</title>\n"
				+"<style type='text/css'>\n"
				/*+ "body{\nbackground-color:#E0E0E0;\n}\n"*/
				+ "table{\nbackground-color:white;\n}\n"+tempStyle
				+"</style>\n</head>\n<body version='V3.0'>\n"
				+"<body id='water' style =\"width:900px; height:500px; padding-right:0px; padding-left:0px; padding-bottom:0px; padding-top:0px; margin-left:0px; margin-top:0px\">\n" +
				/*"<script>\n";

				if(null==watermark || "".equals(watermark)){
					htmlString=htmlString+
							"watermark({'watermark_txt':});\n" ;
				}else{
					htmlString=htmlString+
							"watermark({'watermark_txt':'"+watermark+"'});\n" ;
				}
		         htmlString=htmlString+*/
				/*"function watermark(settings) {\n" +
				"    //默认设置\n" +
				"    var defaultSettings = {\n" +
				"        watermark_txt: \"text\",\n" +
				"        watermark_x: 20, //水印起始位置x轴坐标\n" +
				"        watermark_y: 20, //水印起始位置Y轴坐标\n" +
				"        watermark_rows: 20, //水印行数\n" +
				"        watermark_cols: 20, //水印列数\n" +
				"        watermark_x_space: 100, //水印x轴间隔\n" +
				"        watermark_y_space: 50, //水印y轴间隔\n" +
				"        watermark_color: '#aaa', //水印字体颜色\n" +
				"        watermark_alpha: 0.2, //水印透明度\n" +
				"        watermark_fontsize: '15px', //水印字体大小\n" +
				"        watermark_font: '宋体', //水印字体\n" +
				"        watermark_width: 210, //水印宽度\n" +
				"        watermark_height: 80, //水印长度\n" +
				"        watermark_angle: 20 //水印倾斜度数\n" +
				"    };\n" +
				"    if (arguments.length === 1 && typeof arguments[0] === \"object\") {\n" +
				"        var src = arguments[0] || {};\n" +
				"        for (key in src) {\n" +
				"            if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) continue;\n" +
				"            else if (src[key]) defaultSettings[key] = src[key];\n" +
				"        }\n" +
				"    }\n" +
				"    var oTemp = document.createDocumentFragment();\n" +
				" //获取页面最大宽度\n" +
				"    var page_width = Math.max(document.getElementById(\"water\").scrollWidth, document.getElementById(\"water\").clientWidth);\n" +
				"    var cutWidth = page_width * 0.0150;\n" +
				"    var page_width = page_width - cutWidth;\n" +
				"    //获取页面最大高度\n" +
				"    var page_height = Math.max(document.getElementById(\"water\").scrollHeight, document.getElementById(\"water\").clientHeight) + 450;\n" +
				"    page_height = Math.max(page_height, window.innerHeight - 30);\n" +
				"    //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔\n" +
				"    if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {\n" +
				"        defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));\n" +
				"        defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));\n" +
				"    }\n" +
				"    //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔\n" +
				"    if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {\n" +
				"        defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));\n" +
				"        defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));\n" +
				"    }\n" +
				"    var x;\n" +
				"    var y;\n" +
				"    for (var i = 0; i < defaultSettings.watermark_rows; i++) {\n" +
				"        y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;\n" +
				"        for (var j = 0; j < defaultSettings.watermark_cols; j++) {\n" +
				"            x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;\n" +
				"            var mask_div = document.createElement('div');\n" +
				"            mask_div.id = 'mask_div' + i + j;\n" +
				"            mask_div.className = 'mask_div';\n" +
				"            mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));\n" +
				"            //设置水印div倾斜显示\n" +
				"            mask_div.style.webkitTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
				"            mask_div.style.MozTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
				"            mask_div.style.msTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
				"            mask_div.style.OTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
				"            mask_div.style.transform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
				"            mask_div.style.visibility = \"\";\n" +
				"            mask_div.style.position = \"absolute\";\n" +
				"            mask_div.style.left = x + 'px';\n" +
				"            mask_div.style.top = y + 'px';\n" +
				"            mask_div.style.overflow = \"hidden\";\n" +
				"            mask_div.style.zIndex = \"9999\";\n" +
				"            //让水印不遮挡页面的点击事件\n" +
				"            mask_div.style.pointerEvents = 'none';\n" +
				"            mask_div.style.opacity = defaultSettings.watermark_alpha;\n" +
				"            mask_div.style.fontSize = defaultSettings.watermark_fontsize;\n" +
				"            mask_div.style.fontFamily = defaultSettings.watermark_font;\n" +
				"            mask_div.style.color = defaultSettings.watermark_color;\n" +
				"            mask_div.style.textAlign = \"center\";\n" +
				"            mask_div.style.width = defaultSettings.watermark_width + 'px';\n" +
				"            mask_div.style.height = defaultSettings.watermark_height + 'px';\n" +
				"            mask_div.style.display = \"block\";\n" +
				"            oTemp.appendChild(mask_div);\n" +
				"        };\n" +
				"    };\n" +
				"    document.getElementById(\"water\").appendChild(oTemp);\n" +
				"}  </script>"
				+*/tableStr+"\n</body>\n</html>";
		return htmlString;
	}
	/**
	 *
	 * 方法名:createNewTable
	 * 方法描述: 具体的解析逻辑
	 * @param sheet
	 * @return
	 * @Author: xcl
	 * @Create Date: 2019年11月29日
	 */
	public static Element createNewTable(Element sheet,Map<String, String> tempInfo)throws Exception{
		List<Element> sheets =sheet.elements();
		List<String >deleteTds=new ArrayList<String>();
		Map<String, Map<String, String>>cellInfo=new HashMap<>();
		int sheetNum=0;
		for(Element se: sheets){
			int oldstartRow=0;
			int oldstartCol=1;
			if(sheets.get(sheetNum).element("PrintArea")!=null){
				oldstartRow=Integer.valueOf(sheets.get(sheetNum).element("PrintArea").attribute("StartRow").getValue());
				oldstartCol=Integer.valueOf(sheets.get(sheetNum).element("PrintArea").attribute("StartCol").getValue());
			}
			//获取单元格
			List<Element> cells= se.elements("Cell");
			int rowTotal=Integer.valueOf(se.attribute("Rows").getValue());
			//找出是否需要整体平移  add by shijiangshan 2022-08-17  用数组取出cell最小的row值
			Integer [] intarr=new Integer[cells.size()];

			for(int i =0;i<cells.size();i++){
					intarr[i]=Integer.parseInt(cells.get(i).attribute("Row").getValue());
			}

			int temp = 0;
			boolean flag = false;
			for (int i = 0; i < intarr.length - 1; i++) {
				for (int j = i + 1; j < intarr.length - 1 ; j++) {
					if (intarr[i] > intarr[j]) {
						temp = intarr[j];
						intarr[j] = intarr[i];
						intarr[i] = temp;
					}
				}
			}

			for(Element ce: cells){

				/*System.out.println(ce.getText());*/
				//找出无用需删除的td
				int row=Integer.parseInt(ce.attribute("Row").getValue());
				int col=Integer.parseInt(ce.attribute("Col").getValue());
				//看最小的row值是否大于等于绘画的起始值，如果不大于  需要给每个cell的row加上这个插值，才能保证最小的row那一行，也在绘画的范围中  协和的统一视图部署，用下面这套
				/*if(intarr[0]<oldstartRow){
					row=row+(oldstartRow-intarr[0]);
				}*/
				//除协和以外的医院用这套
				if(oldstartRow<2){
					row+=1;
				}
				if(oldstartCol<2){
					col+=1;
				}

				int rowspan = Integer.parseInt(ce.attribute("Rows").getValue());
				int colspan = Integer.parseInt(ce.attribute("Columns").getValue());
				for(int a=0;a<rowspan;a++){
					for(int b=0;b<colspan;b++){
						int deleteRow=row+a;
						int deleteCol=col+b;
						if(!(deleteRow==row&&deleteCol==col)){
							deleteTds.add(sheetNum+"-"+deleteRow+"-"+deleteCol);
						}
					}
				}
				//cell下的<Alignment>,<Font>放进属性里;<Object>放进内容里
				//对齐位置
				String textAlign="text-align:"+ce.element("Alignment").attribute("Horizontal").getValue();
				String verticalAlign="vertical-align:"+ce.element("Alignment").attribute("Vertical").getValue();
				//字体样式
				String fontFamily="font-family:"+ce.element("Font").attribute("FontName").getValue();
				int fontSizeNum=Integer.valueOf(ce.element("Font").attribute("FontSize").getValue());
				String fontSize;

				if(fontSizeNum>13){
					fontSize="font-size:"+(Integer.valueOf(ce.element("Font").attribute("FontSize").getValue())+6)+"px";
				}else{
					fontSize="font-size:"+(Integer.valueOf(ce.element("Font").attribute("FontSize").getValue())+2)+"px";
				}
				if(fontSizeNum>30){
					fontSize="font-size:"+"12px";
				}
				String fontWeight="font-weight:"+ce.element("Font").attribute("FontWeight").getValue();
				String underLine="";
				if("true".equals(ce.element("Font").attribute("UnderLine").getValue())){
					underLine= "border-bottom:1px solid";
				}
				String color="color:"+ce.element("Font").attribute("FontColor").getValue();

				String styleStr=textAlign+";"+verticalAlign+";"+fontFamily+";"+fontSize+";"+fontWeight+";"+color+";"+underLine;

				//cell内容：<object>
				//判断对选项框做处理
				String cellContent;
				if(ce.element("Object").attribute("DrawInboxRect")==null){
					cellContent=ce.element("Object").attribute("Value").getValue();
				}else {
					cellContent="<span style='border:1px solid;padding:3px 7px'>"+ce.element("Object").attribute("Value").getValue()+"</span>";
				}
				String cellType=ce.element("Object").attributeValue("Type");
				String cellCode=ce.element("Object").attributeValue("Code");
				Map<String, String>cellAttr=new HashMap<String, String>();
				cellAttr.put("rowspan", rowspan+"");
				cellAttr.put("colspan", colspan+"");
				cellAttr.put("style", styleStr);
				cellAttr.put("text", cellContent);
				cellAttr.put("type", cellType);
				cellAttr.put("code", cellCode);
				cellInfo.put(sheetNum+"-"+row+"-"+col, cellAttr);
			}
			sheetNum++;
		}
		Element body=DocumentHelper.createElement("body");
		for(int i=0;i< sheets.size();i++){
			//创建新的table元素
			Element table=DocumentHelper.createElement("table");
			//上行（上行有元素内容的行）的元素（用于后面行格式的对照）
			Element lastTr=null;
			List<Element> lines= sheets.get(i).element("Lines").elements("Line");
			int rows=Integer.valueOf(sheets.get(i).attribute("Rows").getValue());
			int cols=Integer.valueOf(sheets.get(i).attribute("Columns").getValue());
			int oldstartRow=0;
			int oldstartCol=1;
			if(sheets.get(i).element("PrintArea")!=null){
				oldstartRow=Integer.valueOf(sheets.get(i).element("PrintArea").attribute("StartRow").getValue());
				oldstartCol=Integer.valueOf(sheets.get(i).element("PrintArea").attribute("StartCol").getValue());
			}
			int startRow=0;
			int startCol=0;
			int addRow=0;
			int addCol=0;
			//处理起始行为0的情况（为必须得有一空行在开头）
			if(oldstartRow>0){
				startRow=oldstartRow-1;
			}else{
				rows+=1;
				addRow=1;
			}
			if(oldstartCol>1){
				startCol=oldstartCol-2;
			}else{
				//startCol=oldstartCol;
				cols+=1;
				addCol=1;
			}
			//获取单元格大小（缩小一个单位）
			String cellHeight=Integer.valueOf(sheets.get(i).attribute("CellHeight").getValue())-1+"px";
			String cellWidth=Integer.valueOf(sheets.get(i).attribute("CellWidth").getValue())-1+"px";

			table.addAttribute("border", "0").addAttribute("style", "border-collapse:collapse;empty-cells:show;table-layout:fixed")
					.addAttribute("align", "center").addAttribute("width", "100px");//加个默认宽度使td超长隐藏功能生效
			//生成整个表格全部格子
			for(int r=startRow;r<=rows;r++){
				Element tr = table.addElement("tr").addAttribute("rowNum", i+"-"+r);
				for(int c=startCol;c<=cols;c++){
					if(!deleteTds.contains(i+"-"+r+"-"+c)){
						Element td = tr.addElement("td").addAttribute("tdId", i+"-"+r+"-"+c).addAttribute("height", cellHeight)
								.addAttribute("width", cellWidth);
						Map<String, String> tdInfo=cellInfo.get(i+"-"+r+"-"+c);
						if(tdInfo!=null){
							String style=tdInfo.get("style");
							String rowspan=StringUtils.isEmpty(tdInfo.get("rowspan"))?"1":tdInfo.get("rowspan");
							String colspan=StringUtils.isEmpty(tdInfo.get("colspan"))?"1":tdInfo.get("colspan");
							String text=tdInfo.get("text");
							String type=tdInfo.get("type");
							String code=tdInfo.get("code");
							//text为空的项设置背景色便于识别操作单元格
							/*if(StringUtils.isNotBlank(style)&&StringUtils.isBlank(text)){
								style="background:#c0c0c0;"+style;
							}*/
							//text为具体数据值的项设置背景色便于识别操作单元格
							/*if(StringUtils.isNotBlank(type)&&"2".equals(type)){
								style="background:#c0c0c0;"+style;
							}*/
							if(tempInfo.size()>0){//模板
								if(StringUtils.isNotBlank(type)&&"2".equals(type)&&StringUtils.isNotBlank(code)
										&&(StringUtils.isBlank(text)||text.indexOf("：")==-1)){//text为空或者不含"："符号
									String displayName = tempInfo.get(code);
									if(StringUtils.isNotBlank(displayName)){
//										if(StringUtils.isNotBlank(text)&&text.indexOf("：")>-1/*&&
//												text.indexOf("<span style='border:1px solid;padding:3px 7px'>")==-1*/){
//											//存在text的情况且带：的节点
//											td.setText(text);
//										}else{
//											td.setText(displayName);
//											td.addAttribute("title", displayName);
//										}
										td.setText(displayName);
										td.addAttribute("title", displayName);
									}
								}else{
									td.setText(text);
								}
							}else{//实例
								td.setText(text);
							}
							td.addAttribute("tag", code);//添加节点位置标志
							/*if (null!=code) {
								String pattern = ".*-.*-.*-.*-.*";
								if (code.matches(pattern)) {
									String str="<image src=\"data:image/jpg;base64,\n" +
											"iVBORw0KGgoAAAANSUhEUgAAAOEAAAAnCAMAAADO3N/hAAAAAXNSR0IArs4c6QAAAARnQU1B\n" +
											"AACxjwv8YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAA\n" +
											"AwBQTFRF/v7+/f39LSQi9/f3JBkW+/v7y8zM8/P0MywqKyMgiYmJ+fn59vb2wsPENi8uLiYk\n" +
											"/Pz8TElIz8/QSEVFKSAdJx0aw8TEbW1t7u/vJRsYS0hH+Pj4k5OT3t7fW1lZaGdn8/PzTUpJ\n" +
											"4uLi7e3uc3Jzx8jIl5eY7u7uubq6t7i4qqurLycl5ubn5+joYmFhPTk4VlRUVFJSeHd3UE1N\n" +
											"4+Pk1NXV8fHxzMzNv8DAjIyNxMXFTktLenp6vb6+KB4cODIxgoKDJhwZ9fX25OTk6erqhISE\n" +
											"8fHyhYWF6uvrmJiZeXl52dnaMCknKiEfWFZWzc7OoaGiOzY1+vr6wMHBJBoXPDc21tfXnJ2d\n" +
											"XFpaMiopra6vrq+vkZGRR0RDamlp8PDw9fX1vr6/UlBP0NDR29vcsrKz7O3t0tLTlJWV2NjZ\n" +
											"i4uM3d7ea2pqZmVl09TUPjo56OjpmpqaXVtbf35/QDw77+/wNzEvycnKU1FQNC4se3t7cG9w\n" +
											"5OXlurq7dHNz2tvbhoaGQT086enp6+vrr7CwsbGytre4wsLD5ebmkpKS9PT0h4eHY2Jib29v\n" +
											"8vLyWlhY3+DgdXR0SkZGZ2ZmVVNToqOjV1VVysvLnZ2era2u3N3d2dratbW2jY2OcnFyrKyt\n" +
											"d3Z2RkNCioqL39/fZGNjqKmpbWxsxsbHlZWWOTQzpqanPzs60NHRzs/Po6Skvr/Azc3OfHx8\n" +
											"oKChwcLCgH+Ampubjo6PYF9eg4ODsrO0u7u8xsfIs7S0RUJB5+fnQj4+YWBfcXBx1tbXlpaX\n" +
											"yMnJT0xMfX19paWmioqKtLW1kJCQp6eo3NzdbGtr7Ozsp6ip0dLSfn5+X15deHh4REFAuLm5\n" +
											"sLCx19jYQz8/ZWRkgYGC4OHhmZmZiIiIycrLoaKibm5unp6fxcXGk5SUqaqqu7y84eHigYCB\n" +
											"n5+gpKWltra3WVdXm5ycXlxcq6usj4+QdnV1UU5O1dXW09PUaWhovL29IxkWIxgV////AAAA\n" +
											"AAAAAAAAAAAAAAAAAAAAAAAAIoS+TgAAC31JREFUaEPVWXd4VMUW39303ggEAgkhxRBaSIAE\n" +
											"QkhCDxCQ3qVI79JBegdpgoLSiyCgKEVUBEQQEFCKvff61Nffm9/5852Ze3f37t27Kfqpz/k+\n" +
											"srtzzpw+pww2+pOt/37ao2oS26qG/odjtwRwf5Wk+G01bNVkRZWkqQi52WFWEJ0qQvOA+9Kw\n" +
											"/XvLfdNJONHq9IJKsNkH1KoEWsUoGwdvrZ9K9ocBAcRfLu9AYjgF2Y0IFhrKrVUCM3zRGde3\n" +
											"RFpygi+4PcAJ6SJwM3zzlLHlSHT5Zao4jE4slQwn1RKDLgK1PeTXSPulD9e+1PnCEZyLkxVo\n" +
											"yOCNwcB9vuT6SRoSors1vN2Mo33W6qArKGv5FOO+5FPFULGvIg9OzIiBv9QQA3vN4r91vA7Y\n" +
											"oreJ62o3XwgpnKhuwLE24L1MabIv1uMVO9S3hudI9UdpsJUSj39P8kVqkBRn9hzfSqaeeUez\n" +
											"59Q6j2PhcPntOS/sdSOdl7NeH4WMByi89whdNWsNHy8vCilDCt7mHskpaJiZ3z5m4FAwohrK\n" +
											"FAIjfaiwQukPHPMBH3Pqcl/lE4QS9Sq7cBU1LSzbT3GJH0PUrL2fDBmg5Sr+82BHRdZaw0b+\n" +
											"Ak/6tuw4IG4Ng5dHZGPo+tEeNAJd7p01GShySAk/tya1SwsGiGd8sKonTlF0U8boQWE2zV5D\n" +
											"vFFzFJGefjQC+4n2dtOp8kcz3xpOYw0b+taQFtSjsBvXa2m01nsiFuMpmWqiX2VQE+rECgRa\n" +
											"U/qX8q+0uC9Ow9CFFjLCTb8SLKa8LrdERmH2PCW3e41TRGZ2pnSIx3m7NV/aDxySdkqCUcN6\n" +
											"z1bf6T7VkBE2l6MhzZmrm1/gYRNeIELW2QLoRWYsWoziv9esCYVP1Ui86eeb0VNpdaX4czGg\n" +
											"FyOxzd4pQTf9CuinngOi2qDvbUpsKjA29rA/HPP7vcun9u/QMFSEBUWkSV4fxDp5vcGC/UCr\n" +
											"l3S2ZN71pK4e3nrotd7m5J1QE5tEU2o1+BBW2Rkxvr21BknS9u9cM6Y9siV4qrth3HnpjChk\n" +
											"BQXRd8CmHFxo6ybHwif2FfjxGkZTx2ABf5lzv4ymz/jjeSea0nCBHroOZ97gyyRmdrfqHuyB\n" +
											"HZqAczKihuTHhlvJ3lBKXsyQ6aDHmHCmtYIdF9U+0vxRziCu1azwRmvMc1lZ7pf2/7twVJPR\n" +
											"0DOAYrF7WS3UM5HjtO+Xib10QtNhj3T2Q4573aJpWYJLs7wTopV+PFMiCwxsbCBX/WRex4iP\n" +
											"Gqioj5q/xlpuNuojjHA0iOgQnqXt3zZr5AuR9+9G4aoTfFxmFLm63XGfGFH68d7343mz7Emy\n" +
											"J04c0x33ktmsuaBAdCDaxJKlNOezEz0rpqbhHO3OiwideKimMLYaxGus54U4Bx7k7cC5wcnv\n" +
											"eUtfP4SP1uJk0EhmeF7hAw5sfzQtP8xK0RjGvZQoIWE1nIHPn39x4bZrbSdZM8/w8Y/3DOBa\n" +
											"sX+e/8i1CUZiL6JhCwwl+kmLzClxwItGuKbhrGoqqWGKDmqt+TCqnRs1TzdxPoE9y7HBJzya\n" +
											"zo9vffNZh5kKS+asOLx7qD9/VleYAr0tVJwhkdMlQDrAtdyhO6vlnNm8HdNp6Pod9KmQ90ze\n" +
											"kCKt0mlrMJbcDyygw2Iu0VhZ/PGIt4Ysj1xR63TQJPUz2Bjz6Vzkj8Tu8KPWSKQPNWk8urFH\n" +
											"XRLKFiaogVQrjehTffuSl4bR1yXK9HZZOTGK/ezQgZqeB12ofoti2c+z5/r3Qac92BPQH1zt\n" +
											"GOcTA7FC5Y0N1OJSV72LgvOyKSy9WuuXQPfZWiVVkmeWzNivpo01yB5TgLK/8oXc5SH0u5pb\n" +
											"Z2Uhg/eHhchfA9r399e9k2vWsLfSZxs9IAXkfy/YD6qbwdfXvZpmNhIIJPvwD3svnnIw4yie\n" +
											"DHv9o+keFXSxxmCxdujbpBq5fUSygYSuoeZDaKlbekPgFYsenoHFKHMgO3o5QuqaZP4CNYNv\n" +
											"hdExDJaG2BClO0+gqBp/NSYthk/jTC0l23waSSECSSOWUR3Pm6Ko96wxPjuInot85BylvskR\n" +
											"Gv8yb0a7hheJ0gPxyezZ8U5p/r0UQrZwzqU03HhNy6U4LX89rfHWu2eTGgFNGHSFbP9E0nnz\n" +
											"rN1xWjRjx2i+Ha9IxgwFHiL6XnWW7hYxqFhXf/YgfENcjGsT5UkPCrQ2MswUAzdO2x0ZmZNR\n" +
											"wFYKKTiw0SQO/1yNSFoZckYHtO8ipR/tRFseSLbE5ulno/RiIeQTiG0rypi/r245VyAitTRb\n" +
											"SnjDmx3vXITMyLYUJG8RsXRTJTbOw867ce71sMB9nBQR3FNpuZDoQYEt1E+zsXPwUqRfVl7l\n" +
											"zL3586NoElLk2c3o3F9DiLOMt7uTrKicdQl2NoZsWYYkNpRJhA1J5/QELLIUn+gJgFtcuUIS\n" +
											"rFDqxmM776/lsORKRVdj+McgCOe0OawN2sjcWnDZdlXdPgbPYKFyIyVJ0cAj9DkPY0sh5cm4\n" +
											"mUERiJLlzry+R83O+l4HLTIM3XkpGtkaykui2Q9vMubkx1gi/sEflisN4mnuBYFk68Y1yIFV\n" +
											"PGtjKUt0isj/Z6IWPE8X6sSG83fAsZ163adK7vu8H+gSwGTYHY64E0T/wdQUlvq49Ln3+Esj\n" +
											"Idx94RPgVizPLfh0FNpIzoL6epV6OeTgK1ONryek3QJlsv3TRvzm/cxm4A7wMQqaidArfWW4\n" +
											"lnIK2ADhrlC7EdLg4TE8CCijXpDH7RdVHKk/SZ70ZJrotPo7zso1gMAzeMLL6nUdiHf6UObl\n" +
											"Hpk47kLiQpnFJGTzqK3QhaghofX5e1PTkOI8FbDp5Ln+BSo30JFICNnfGFdtmVoOI2uJZvGA\n" +
											"bm3W8zd3ZW1Xv62NEuRcITXaoo7+AzIO5U5kqlXgPK+gfyPKxkQzfCfEUpngePkN4MiiV/CZ\n" +
											"E6ffPOCANFLEigjZawmeoHMUsJ8yZ9+mX4y2YBjAR56GeInsKUpK06T8tlQee6hYQLY0zJDX\n" +
											"SpNczzhtqvn2eNaycRPmLxq3N9Oyl7cvFbWCUdIYb+z0HqZ7Q+itVQ++bylEM5XxiepWny65\n" +
											"dNfqYYLsbllc5UFeKXqZhsi3sqksBXPIvkUZ4ronBgfKneFpNgq6j7MkrwxG8YotqaGq7iWW\n" +
											"6dHE8368FdsAkR/m3CbMN8vzCb9QqZHLj+NCzqrcUxdcmvRWgfZ6BWgaNtd/yXsvl+HuGzsM\n" +
											"F/Wu6uH5dlokC3nOk+dqcKNvGAnZ3ZGdzWKNRZtu2HWehwFLA5o2O2DwCyWvjviEXx3m8TuF\n" +
											"59rKc61S4jXBg4fUNNcZIMpr9TUNA7WEmu06PFqrIVyAjY8wK2IKJ56mUY3oJefYd2iCufNJ\n" +
											"rSlKjTJ0LvrI3PwQDT+1rHMe7S3nydJAIiC4z5C36zwvJnMrk4MWJg35MUjLTg9su6uB/Lqu\n" +
											"3NolmVsqgW2lO7Q244ZeEw1Pt3XPdpCj4HkjvWlS6S/ZWF1/ENjny/rHigxTOKca4xzgeeZH\n" +
											"iCLLqcoTLVZUo3oNLkykNV+Fv4evTHyPQGj3zrTCG6bnK0HkQwAPMUrFK55Ijac/w926YbWV\n" +
											"GY9DRVUy7sWsl905oPhCcO8vwc2KkSgLcVxRqO0kgYsT8LXpxCjjPGlFzUZ6QKKm4SVKYbbw\n" +
											"ar05i8zmWx22jbMEl/Vfv35+oxI0vubXvMWy+KAkstjr4YuKXzFeJG96Njn8Sg8Ge/rLknM+\n" +
											"4tR/3t3jELBqoCoh7i9ACVUBNqH5lLdFwlhXuq80IduyPiqnOMp7T3FSs6XqtbUlPGfDSrP7\n" +
											"JYjTDuCFVjLBBFSb/zo2VJWErbqaK4ZWRkE37YPy6fV3W7Z1ehwe43CzTCvliWLjmwp0KT+U\n" +
											"fzddKmDUdqoQcgSu0rJRErC7Skf+QOTVPeVkVrVlo7B77pbzrl41av+P2H+O8Pw1lvsfTFmy\n" +
											"OdWPyHwAAAAASUVORK5CYII=\n" +
											"\" xid=\"222\" style=\"height:0.41in; width:2.34in\"/>";
									td.setText(str);
								}
							}*/
							String lineStyle = addLines(lines, r-addRow, c-addCol, Integer.parseInt(rowspan), Integer.parseInt(colspan));
							td.addAttribute("style", style+lineStyle);
							td.addAttribute("rowspan", rowspan);
							td.addAttribute("colspan", colspan);
						}else{
							String lineStyle = addLines(lines, r-addRow, c-addCol, 1, 1);
							td.addAttribute("style", lineStyle);
						}

					}
				}
			}
			body.add(table);
		}
		tempInfo=null;
		return body;
	}



	/**
	 *
	 * 方法名:addLines
	 * 方法描述: 按照xml里线条生成对应样式，规则如下：
	 * 		  0对应上边线，1对应下边线，2对应左边线，3对应右边线，4，对应所有边线；
	 * 		      当DrawType为0-3时：
	 * 		      上边线：只要在里面就有效；
	 * 		      下边线：如果是合并单元格的合并单元区域与线条池描述区域完全相同则有效
	 * 		       (且优先选，没有或者为偶数则StartRow=EndRow=单元格行号，且在列范围内的也有效)，不是合并的则行号等于EndRow就有效；
	 *        左边线：所有单元格只认StartCol且相等才画；
	 *        右边线：若是合并单元格，优先选择合并单元格的合并单元列区域与线条池描述列区域完全相同的，若完全相同的为偶数条则单元格列号与EndCol相等的也有效。
	 *        	         若不是合并单元格，行号在该线条内且列号=EndCol的也有效
	 *        当DrawType为4时：分成4条线再按照0-3的规则生成。
	 * @param lines
	 * @param row
	 * @param col
	 * @param rowSapn
	 * @param colSapn
	 * @return 线条样式的String
	 * @Author: xcl
	 * @Create Date: 2019年11月29日
	 */
	public static String addLines(List<Element> lines,int row,int col,int rowSapn,int colSapn){
		List<String> lineList = new ArrayList<String>();
		String lineType;
		String lineStyle=null;

		for(int j=0;j<lines.size();j++){
			Element le=lines.get(j);
			String startRow=le.attribute("StartRow").getValue();
			String endRow=le.attribute("EndRow").getValue();
			String starcol=le.attribute("StartCol").getValue();
			String endCol=le.attribute("EndCol").getValue();
			String drawType="";
			if(le.attribute("DrawType")==null){
				drawType="1";
			}else{
				drawType=le.attribute("DrawType").getValue();
			}
			if("0".equals(drawType)){

				if(row<=Integer.valueOf(endRow)&&Integer.valueOf(startRow)<=row&&col>=Integer.valueOf(starcol)&&col<=Integer.valueOf(endCol)){
					lineList.add(drawType);
				}
			}else if("1".equals(drawType)){

				if(colSapn>1){
					if((Integer.valueOf(startRow)==row&&col==Integer.valueOf(starcol)&&(col+colSapn-1)==Integer.valueOf(endCol)
					)/*||(Integer.valueOf(startRow)==row&&col==Integer.valueOf(starcol)&&(col+colSapn-1)<Integer.valueOf(endCol)
							&&(row+rowSapn-1)<=Integer.valueOf(endRow))*/){
						//System.out.println(row+"---"+col+":"+startRow+","+endRow+","+starcol+","+endCol+"==="+drawType);
						lineList.add(drawType);
					}
				}else {
					if(row==Integer.valueOf(endRow)&&col>=Integer.valueOf(starcol)&&col<=Integer.valueOf(endCol)){
						//System.out.println(row+"---"+col+":"+startRow+","+endRow+","+starcol+","+endCol+"========"+drawType);
						lineList.add(drawType);
					}
				}
			}else if ("2".equals(drawType)) {

				if(row<=Integer.valueOf(endRow)&&Integer.valueOf(startRow)<=row&&col==Integer.valueOf(starcol)){
					lineList.add(drawType);
				}
			}else if ("3".equals(drawType)) {
				if(rowSapn>1){
					if(row+rowSapn-1==Integer.valueOf(endRow)&&Integer.valueOf(startRow)==row&&
							col==Integer.valueOf(starcol)&&col+colSapn-1==Integer.valueOf(endCol)){
						lineList.add(drawType);
					}
				}else{
					if(row<=Integer.valueOf(endRow)&&Integer.valueOf(startRow)<=row&&(col==Integer.valueOf(endCol)/*||colSapn+col-1==Integer.valueOf(endCol)*/)){
						//System.out.println(row+"---"+col+":"+startRow+","+endRow+","+starcol+","+endCol+"============="+drawType);
						lineList.add(drawType);
					}
				}
			}else if("4".equals(drawType)){
				//把DrawType=4的转成0，1，2，3，
				for(int i=0;i<4;i++){
					Element newLe=(Element) le.clone();
					newLe.attribute("DrawType").setValue(i+"");
					lines.add(newLe);
					lines.remove(le);
				}

			}



		}
		if(row==5&&col==1){
			//System.out.println();
		}
		for(Element le:lines){
			String startRow=le.attribute("StartRow").getValue();
			String endRow=le.attribute("EndRow").getValue();
			String starcol=le.attribute("StartCol").getValue();
			String endCol=le.attribute("EndCol").getValue();
			String drawType="";
			if(le.attribute("DrawType")==null){
				drawType="1";
			}else{
				drawType=le.attribute("DrawType").getValue();
			}
			//System.out.println(le.asXML());
			if(Integer.valueOf(startRow)==5&&Integer.valueOf(starcol)==1){
				//System.out.println(le.asXML());
			}
			if("1".equals(drawType)){
				int count= Collections.frequency(lineList, "1");
				if((count&1) != 1){//为偶数
					if(colSapn>1&&Integer.valueOf(startRow)==row&&Integer.valueOf(endRow)==row&&col>=Integer.valueOf(starcol)&&col<=Integer.valueOf(endCol)){
						//System.out.println(row+"---"+col+":"+startRow+","+endRow+","+starcol+","+endCol+"！~~~~~~"+drawType);
						lineList.add(drawType);
					}else{

					}
				}
			}
			if("3".equals(drawType)){
				int count= Collections.frequency(lineList, "3");
				if((count&1) != 1&&rowSapn>1){
					if((row<=Integer.valueOf(endRow)&&Integer.valueOf(startRow)<=row&&col==Integer.valueOf(endCol))){
						//System.out.println(row+"---"+col+":"+startRow+","+endRow+","+starcol+","+endCol+"！~~~1~~~"+drawType);
						lineList.add(drawType);
					}
				}
				if((count&1) != 1&&rowSapn==1&&colSapn>1){
					if((row==Integer.valueOf(endRow)&&Integer.valueOf(startRow)==row&&col+colSapn-1==Integer.valueOf(endCol))){
						lineList.add(drawType);
						//System.out.println(row+"---"+col+":"+startRow+","+endRow+","+starcol+","+endCol+"！~~~2~~~"+drawType);
					}
						/*if((row<=Integer.valueOf(endRow)&&Integer.valueOf(startRow)<=row&&col==Integer.valueOf(endCol))){
							lineList.add(drawType);
							System.out.println(row+"---"+col+":"+startRow+","+endRow+","+starcol+","+endCol+"！~~~3~~~"+drawType);
						}*/
				}
			}



		}

		if(lineList.isEmpty()){
			lineType="";
		}else {
			lineType=lineList.toString();
		}

		//System.out.println(row+"---"+col+"::::"+lineType);
		StringBuffer sb=new StringBuffer();
		if(lineType.contains("0")){
			int count= Collections.frequency(lineList, "0");
			if((count&1) == 1){//判断出现的奇偶次数（奇加偶不加）
				sb.append("border-top:1px solid;");
			}
		}
		if (lineType.contains("1")) {
			int count= Collections.frequency(lineList, "1");
			if((count&1) == 1){
				sb.append("border-bottom:1px solid;");
			}
		}
		if (lineType.contains("2")) {
			int count= Collections.frequency(lineList, "2");
			if((count&1) == 1){
				sb.append("border-left:1px solid;");

			}
		}
		if (lineType.contains("3")) {
			int count= Collections.frequency(lineList, "3");
			if((count&1) == 1){
				sb.append("border-right:1px solid;");

			}
		}
		if (lineType.contains("4")) {
			int count= Collections.frequency(lineList, "4");
			if((count&1) == 1){
				sb.append("border:1px solid;");

			}
		}
		lineStyle=sb.toString();

		return lineStyle;
	}
	/**
	 删除多余td(住院号后面的td)
	 */
	public static void deleteTd(Element table){
		List<Element>trs=table.elements("tr");
		//第6行多余的
		List<Element>tds6=trs.get(6).elements("td");
		for(int i=18;i<tds6.size();i++){
			//System.out.println(tds.get(i).asXML());
			trs.get(6).remove(tds6.get(i));
		}
		//第7行多余的
		List<Element>tds7=trs.get(7).elements("td");
		for(int i=12;i<tds7.size();i++){
			//System.out.println(tds.get(i).asXML());
			trs.get(7).remove(tds7.get(i));
		}
	}
	/**
	 * 按规则生成的表格线条有错误的部分
	 * 在这里进行删除(错误线条位置写死，仅限于住院病案首页)
	 * @param table
	 */
	public static void deleteWrongLines(Element table) {}
	/**
	 *
	 * @Title: changeMathNodeByDataId
	 * @Description: 根据数据信息改变math节点（属性及内容）
	 * @param root
	 * @param mathList
	 * @param imageMap
	 * <AUTHOR>
	 * @throws DocumentException
	 * @date 2020年3月20日
	 */
	public static void changeMathNodeByDataId (Element root,List<Element>mathList,
											   Map<String, String>imageMap) throws DocumentException {
		List<Element> children = root.elements();
		for(Element child:children){
			if("math".equals(child.getName())){
				String mathDataid = child.attribute("dataid").getValue();
				String data = imageMap.get(mathDataid);
				if(StringUtils.isEmpty(data)){
					continue;
				}
				String mathStr ="";
				try {
					// base64解码
					mathStr = new String(Base64.decodeBase64(data.getBytes("utf-8")));
				} catch (Exception e) {
					e.printStackTrace();
					log.error("math标签base64解码错误："+e.getMessage());
				}
				Document mathDoc = DocumentHelper.parseText(mathStr);
				Element mathElement = mathDoc.getRootElement();
				List<Attribute> attributes = mathElement.attributes();
				Iterator<Attribute> iterator = attributes.iterator();
				while (iterator.hasNext()) {
					Attribute attribute = iterator.next();
					String name = attribute.getName();
					String value = attribute.getValue();
					if("display".equals(name)){
//					mathElement.addAttribute("style","display:"+value);
						if(!"none".equals(value)){
							//把原math标签上的属性添加到自定义的xclmath标签，再替换原来的math
							Element xclmath = DocumentHelper.createElement("xclmath");//自定义个标签（用于嵌套math，因为math的单击事件无效）
							Attribute childStyle = child.attribute("props");
							xclmath.addAttribute("props", childStyle.getValue());
//						mathElement.setAttributes(child.attributes());
							int index = children.indexOf(child);
							Element image = child.element("image");
							String imageProps = image.attributeValue("props");
//						image.attributeValue("props","display:none;"+imageProps);
							image.setAttributeValue("props", "display:none;"+imageProps);
							mathElement.addElement("image",mathElement.getNamespaceURI()).setAttributes(image.attributes());
							xclmath.add(mathElement);
//						children.set(index, mathElement);//替换原来的math标签
							children.set(index, xclmath);//替换原来的math标签
							mathList.add(child);//为了统计math的个数
						}else{//display为none的才添加到style样式里
							mathElement.addAttribute("style","display:"+value);
						}
						iterator.remove();//删掉math的display属性
					}
				}
			}else{
				changeMathNodeByDataId(child, mathList, imageMap);
			}

		}

	}

	/**
	 *
	 * @Title: findAllTargetElements
	 * @Description: 查找所有的目标节点
	 * @param root
	 * @param targetName
	 * @param targetElements
	 * <AUTHOR>
	 * @date 2020年3月20日
	 */
	public static void findAllTargetElements (Element root,String targetName,List<Element> targetElements) {
		List<Element> elements = root.elements(targetName);
		if(elements.size()>0){
			targetElements.addAll(elements);
		}else{
			List<Element> children = root.elements();
			for(Element child:children){
				List<Element> childElements = root.elements(targetName);
				if(childElements.size()>0){
					targetElements.addAll(childElements);
				}else{
					findAllTargetElements(child, targetName, targetElements);
				}
			}
		}

	}
	//根据节点属性获取任意子节点（属性值要唯一,有相同的话默认返回第一个）
	public static Element parse(Element node , String type , String val) {
		//System.out.println(node.asXML());
		for (Iterator iter = node.elementIterator(); iter.hasNext();) {
			Element element = (Element) iter.next();
			// System.out.println(element.asXML());
			Attribute name = element.attribute(type);
			if(val==null){//当不传属性值时只判断有没有该属性
				if(name != null){
					return element;
				}else {
					parse(element , type , val);
				}

			}else {
				if (name != null) {
					String value = name.getValue();
					if (value != null && val.equals(value)){
						return element;
					}
					else{
						parse(element , type , val);
					}
				}

			}
		}
		return null;
	}

	public static char toHex(int ch) {
		if (ch < 10) {
			return (char) ('0' + ch);
		}
		else{
			return (char) ('a' + ch - 10);
		}
	}



}
