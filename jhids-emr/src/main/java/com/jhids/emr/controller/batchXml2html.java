/**
 *
 */
package com.jhids.emr.controller;

import java.io.*;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.PostConstruct;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
* <p>标题:emr_switch</p>
* <p>业务描述:</p>
* <AUTHOR>
* @date 2019年9月12日
 */
@Component
public class batchXml2html {

	@Autowired
	private   Environment env;


	private static final Log LOG = LogFactory.getLog(batchXml2html.class);




/*
	public static void main(String[] args) throws Exception{
		String watermark="北京协和医院张三111";
		int len=0;
		int len1=0;
		String path = "C:\\Users\\<USER>\\Desktop\\test\\样式文件.xml";
		String path1 = "C:\\Users\\<USER>\\Desktop\\test\\内容.xml";
		File file=new File(path);
		File file1=new File(path1);
		FileInputStream decryptInputStream = new FileInputStream(file);
		FileInputStream decryptInputStream1 = new FileInputStream(file1);
		InputStreamReader isr=new InputStreamReader(decryptInputStream);
		InputStreamReader isr1=new InputStreamReader(decryptInputStream1);
		BufferedReader in= new BufferedReader(isr);
		BufferedReader in1= new BufferedReader(isr1);
		String line=null;
		String line1=null;
		StringBuffer str=new StringBuffer("");
		StringBuffer str1=new StringBuffer("");
		while((line=in.readLine())!=null){
			if(len!=0){
				str.append("\r\n"+line);
			} else {
				str.append(line);
			}
			len++;
		}
		while((line1=in1.readLine())!=null){
			if(len1!=0){
				str1.append("\r\n"+line1);
			} else {
				str1.append(line1);
			}
			len1++;
		}
		in.close();
		in1.close();
		decryptInputStream.close();
		decryptInputStream1.close();
		String privacy="N";
		xml2Html(str.toString(),str1.toString(),watermark,privacy);
	}*/




	/*
	 * xml->html转换逻辑
	 */
	public static String xml2Html(String xml,String nxml,String watermark,String privacy,String htmlUrl){
		try {
			String html="";
			int PageNum=1;
			Map<String,String> vMap = new HashMap<String, String>();
			Map<String,String> sMap = new HashMap<String, String>();
			Map<String,String> rlsMap = new HashMap<String, String>();
			Map<String,String> TMap = new HashMap<String, String>();
			Map<String,String> TVMap = new HashMap<String,String>();
			Map<String,String> RowMap=new HashMap<String, String>();
			Map<String,String> ColumnMap=new HashMap<String, String>();
			String EMRXML=StringUtils.substringBefore(xml, "<Request>");
			EMRXML=EMRXML.replace("<![CDATA[", "");
			EMRXML=EMRXML.replace("]]>", "");
			EMRXML=EMRXML.replace("&#x0;","");

			Document document = DocumentHelper.parseText(EMRXML);
			Document ndocument = DocumentHelper.parseText(nxml);
			int RowMax=0,ColumMax=0;
			List<Node> MaxRowColumn=document.selectNodes("/Spread/Data/Sheets/Sheet/DataArea");
			Iterator MaxRowColumnSheet=MaxRowColumn.iterator();
			while(MaxRowColumnSheet.hasNext())
			{
				Element e = (Element) MaxRowColumnSheet.next();
				RowMax=Integer.parseInt(e.attributeValue("rows"));
				ColumMax=Integer.parseInt(e.attributeValue("columns"));
			}
			String[][] SheetArr=new String[RowMax][ColumMax];
			///取单独的TR和TD的大小，以及表格的大小
			List<Node> RowItem=document.selectNodes("/Spread/Presentation/Sheets/Sheet/AxisModels/Row/Items/Item");
			Iterator RowItemit=RowItem.iterator();
			while(RowItemit.hasNext())
			{
				Element e = (Element) RowItemit.next();
				String Rowindex=e.attributeValue("index");
				Element Size=e.element("Size");
				String RowHeight=null;
				if (Size!=null)
				{
					RowHeight=String.valueOf(Integer.parseInt(Size.getText()));
				}
				if (RowHeight!=null)
				{
					RowMap.put(Rowindex, RowHeight);
				}
			}
			for(int rw=0;rw<RowMax;rw++)
			{
				for(int cl=0;cl<ColumMax;cl++)
				{
					SheetArr[rw][cl]="<td></td>";
				}
			}
			List<Node> ColumnItem=document.selectNodes("/Spread/Presentation/Sheets/Sheet/AxisModels/Column/Items/Item");
			Iterator ColumnItemit=ColumnItem.iterator();
			while(ColumnItemit.hasNext())
			{
				Element e = (Element) ColumnItemit.next();
				String Columnindex=e.attributeValue("index");
				Element Size=e.element("Size");
				String ColumnHeight=null;
				if (Size!=null)
				{
					ColumnHeight=String.valueOf(Integer.parseInt(Size.getText())-2);
				}
				if (ColumnHeight!=null)
				{
					ColumnMap.put(Columnindex, ColumnHeight);
				}
			}

			//取样式信息
			List<Node> StyleList=document.selectNodes("/Spread/EPRStyleCollection/EPRStyle");
			Iterator Styleit=StyleList.iterator();
			while(Styleit.hasNext())
			{
				Element e = (Element) Styleit.next();
				String StyleId=e.attributeValue("ID");
				sMap.put(StyleId,".S"+StyleId+"{");
				String temp="";
				Element Border=e.element("Border");
				if (Border!=null) {
					Element Sides = Border.element("Sides");
					Element Bottom = Sides.element("Bottom");
					Element Left = Sides.element("Left");
					Element Right = Sides.element("Right");
					Element Top = Sides.element("Top");
					Element BottomStyle = Bottom.element("Style");
					Element LeftStyle = Left.element("Style");
					Element RightStyle = Right.element("Style");
					Element TopStyle = Top.element("Style");
					if(BottomStyle.getText().equals("ThinLine")){
						String borderbottom="border-bottom:1px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(BottomStyle.getText().equals("MediumLine")){
						String borderbottom="border-bottom:2px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(BottomStyle.getText().equals("DashDot")){
						String borderbottom="border-bottom:1px dashed;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					if(LeftStyle.getText().equals("ThinLine")){
						String borderbottom="border-left:1px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(LeftStyle.getText().equals("MediumLine")){
						String borderbottom="border-left:2px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(LeftStyle.getText().equals("DashDot")){
						String borderbottom="border-left:1px dashed;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					if(RightStyle.getText().equals("ThinLine")){
						String borderbottom="border-right:1px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(RightStyle.getText().equals("MediumLine")){
						String borderbottom="border-right:2px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(RightStyle.getText().equals("DashDot")){
						String borderbottom="border-right:1px dashed;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					if(TopStyle.getText().equals("ThinLine")){
						String borderbottom="border-top:1px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(TopStyle.getText().equals("MediumLine")){
						String borderbottom="border-top:2px solid;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
					else if(TopStyle.getText().equals("DashDot")){
						String borderbottom="border-top:1px dashed;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+borderbottom);
					}
				}
				Element VerticalAlignment = e.element("VerticalAlignment");
				if(VerticalAlignment!=null) {
					if(VerticalAlignment.getText().contentEquals("bottom")) {
						String verticalalign = "vertical-align:bottom;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+verticalalign);
					}
					else{
						String verticalalign = "vertical-align:Center;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+verticalalign);
					}
				}
				Element HorizontalAlignment=e.element("HorizontalAlignment");
				if(HorizontalAlignment!=null) {
					String textalign="";
					if (HorizontalAlignment.getText().equals("Center"))
					{
						textalign="text-align:center;";
					}
					else if (HorizontalAlignment.getText().equals("Right"))
					{
						textalign="text-align:Right;";
					}
					temp=sMap.get(StyleId);
					sMap.put(StyleId,temp+textalign);
				}
				Element Font=e.element("Font");
				if(Font!=null) {
					Element Name = Font.element("Name");
					Element Size = Font.element("Size");
					Element Bold=Font.element("Bold");
					if (Name!=null) {
						if (Name.getText().equals("楷体_GB2312")) {
							String family = "font-family:KaiTi_GB2312;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+family);
						}
						if (Name.getText().equals("黑体")) {
							String family = "font-family:SimHei;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+family);
						}
						if (Name.getText().equals("宋体")) {
							String family = "font-family:SimSun;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+family);
						}
					}
					if(Size!=null) {
						String FontSize="font-size:"+Size.getText()+"px;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+FontSize);
					}
					if(Bold!=null) {
						if(Bold.getText().equals("True")){
							String weight="font-weight:bold;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+weight);
						}
					}
				}
				else
				{
					String FontSize="font-size:15px;font-family:SimSun;";
					temp=sMap.get(StyleId);
					sMap.put(StyleId,temp+FontSize);
				}
				temp=sMap.get(StyleId);
				sMap.put(StyleId,temp+"}");
			}
			//取行列对应的样式值
			List<Node> CellStyle=document.selectNodes("/Spread/Presentation/Sheets/Sheet/StyleModels/DataArea/CellStyles/CellStyle");
			Iterator CellStyles=CellStyle.iterator();
			while(CellStyles.hasNext()) {
				Element e = (Element) CellStyles.next();
				Element EPRStyleID=e.element("EPRStyleID");
				if (EPRStyleID==null)
				{
					continue;
				}
				else
				{
					String Row=e.attributeValue("Row");
					String Column=e.attributeValue("Column");
					rlsMap.put(Row+" "+Column,EPRStyleID.getText());
				}
			}
			//老版本电子病例有把样式写在一起的情况
			if((rlsMap.isEmpty())&&(sMap.isEmpty()))
			{
				List<Node> CellStyleOld=document.selectNodes("/Spread/Presentation/Sheets/Sheet/StyleModels/DataArea/CellStyles/CellStyle");
				Iterator CellStyleOldIt=CellStyleOld.iterator();
				int Id=0;
				while(CellStyleOldIt.hasNext()){
					Element e = (Element) CellStyleOldIt.next();
					String Row=e.attributeValue("Row");
					String Column=e.attributeValue("Column");
					Id=Id+1;
					String StyleId=String.valueOf(Id);
					rlsMap.put(Row+" "+Column,StyleId);
					sMap.put(StyleId,".S"+StyleId+"{");
					String temp="";
					Element Border=e.element("Border");
					if (Border!=null) {
						Element Sides = Border.element("Sides");
						Element Bottom = Sides.element("Bottom");
						Element Left = Sides.element("Left");
						Element Right = Sides.element("Right");
						Element Top = Sides.element("Top");
						Element BottomStyle = Bottom.element("Style");
						Element LeftStyle = Left.element("Style");
						Element RightStyle = Right.element("Style");
						Element TopStyle = Top.element("Style");
						if(BottomStyle.getText().equals("ThinLine")){
							String borderbottom="border-bottom:1px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(BottomStyle.getText().equals("MediumLine")){
							String borderbottom="border-bottom:2px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(BottomStyle.getText().equals("DashDot")){
							String borderbottom="border-bottom:1px dashed;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						if(LeftStyle.getText().equals("ThinLine")){
							String borderbottom="border-left:1px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(LeftStyle.getText().equals("MediumLine")){
							String borderbottom="border-left:2px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(LeftStyle.getText().equals("DashDot")){
							String borderbottom="border-left:1px dashed;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						if(RightStyle.getText().equals("ThinLine")){
							String borderbottom="border-right:1px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(RightStyle.getText().equals("MediumLine")){
							String borderbottom="border-right:2px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(RightStyle.getText().equals("DashDot")){
							String borderbottom="border-right:1px dashed;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						if(TopStyle.getText().equals("ThinLine")){
							String borderbottom="border-top:1px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(TopStyle.getText().equals("MediumLine")){
							String borderbottom="border-top:2px solid;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
						else if(TopStyle.getText().equals("DashDot")){
							String borderbottom="border-top:1px dashed;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+borderbottom);
						}
					}
					Element VerticalAlignment = e.element("VerticalAlignment");
					if(VerticalAlignment!=null) {
						if(VerticalAlignment.getText().equals("bottom")) {
							String verticalalign = "vertical-align:bottom;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+verticalalign);
						}
						else{
							String verticalalign = "vertical-align:Center;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+verticalalign);
						}
					}
					Element HorizontalAlignment=e.element("HorizontalAlignment");
					if(HorizontalAlignment!=null) {
						String textalign="";
						if (HorizontalAlignment.getText().equals("Center"))
						{
							textalign="text-align:center;";
						}
						else if (HorizontalAlignment.getText().equals("Right"))
						{
							textalign="text-align:Right;";
						}
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+textalign);
					}
					Element Font=e.element("Font");
					if(Font!=null) {
						Element Name = Font.element("Name");
						Element Size = Font.element("Size");
						Element Bold=Font.element("Bold");
						if (Name!=null) {
							if (Name.getText().equals("楷体_GB2312")) {
								String family = "font-family:KaiTi_GB2312;";
								temp=sMap.get(StyleId);
								sMap.put(StyleId,temp+family);
							}
							if (Name.getText().equals("黑体")) {
								String family = "font-family:SimHei;";
								temp=sMap.get(StyleId);
								sMap.put(StyleId,temp+family);
							}
							if (Name.getText().equals("宋体")) {
								String family = "font-family:SimSun;";
								temp=sMap.get(StyleId);
								sMap.put(StyleId,temp+family);
							}
						}
						if(Size!=null) {
							String FontSize="font-size:"+Size.getText()+"px;";
							temp=sMap.get(StyleId);
							sMap.put(StyleId,temp+FontSize);
						}
						if(Bold!=null) {
							if(Bold.getText().equals("True")){
								String weight="font-weight:bold;";
								temp=sMap.get(StyleId);
								sMap.put(StyleId,temp+weight);
							}
						}
					}
					else {
						String FontSize="font-size:15px;font-family:SimSun;";
						temp=sMap.get(StyleId);
						sMap.put(StyleId,temp+FontSize);
					}
					temp=sMap.get(StyleId);
					sMap.put(StyleId,temp+"}");
				}

				CellStyleOld=null;
			}
			// 取数据具体内容存vMap
			List<Node> slist= ndocument.selectNodes("/NInstanceData/SubItems/NISimple");
			List<Node> list= document.selectNodes("/Spread/Data/Sheets/Sheet/DataArea/Cells/Cell");
			List<Node> glist= ndocument.selectNodes("/NInstanceData/SubItems/NISegment");
			List<Node> elist= ndocument.selectNodes("/NInstanceData/SubItems/NITextDesc");
			List<Node> llist= ndocument.selectNodes("/NInstanceData/SubItems/NILink");

			HashMap<String,Object> hashMap1=new HashMap<>();
			HashMap<String,Object> hashMap2=new HashMap<>();
			HashMap<String,Object> hashMap3=new HashMap<>();
			HashMap<String,Object> hashMap4=new HashMap<>();
			HashMap<String,Object> hashMap5=new HashMap<>();
			HashMap<String,Object> hashMap6=new HashMap<>(); // 用于存储NILink数据
			HashMap<String,Object> duigou=new HashMap<>();
			Iterator git = glist.iterator();
			Iterator eit = elist.iterator();
			while(eit.hasNext()) {
				String strvalue ="";
				Element e1 = (Element) eit.next();
				Element code = e1.element("Code");
				String strcode=code.getText();
				List<Node> elist1=e1.selectNodes("SubItems/NIFree");
				Iterator eit1 = elist1.iterator();
				while(eit1.hasNext()) {
					Element e2 = (Element) eit1.next();
					Element value = e2.element("Value");
					strvalue=strvalue+value.getText();
					hashMap3.put(strcode,strvalue);
				}
				//SubItems/NISegment/SubItems下的NIFree
				List<Node> eglist= e1.selectNodes("SubItems/NISegment/SubItems");
				Iterator egit = eglist.iterator();
				while(egit.hasNext()) {
					Element eee = (Element) egit.next();
					for(Iterator it=eee.elementIterator();it.hasNext();){
						Element element = (Element) it.next();
						Element value = element.element("Value");
						strvalue=strvalue+value.getText();
						hashMap3.put(strcode,strvalue);
						// do something
					}
				}

			/*	for (int i=0;i<eglist.size();i++){
					Element e2 =(Element)eglist.get(i);
					Element value = e2.ele
					ment("Value");
					strvalue=strvalue+value.getText();
					hashMap5.put(strcode,strvalue);
				}*/

				//SubItems/NISegment/SubItems下的NISimple
			}
			while(git.hasNext()) {
				String strvalue ="";
				Element e1 = (Element) git.next();
				Element code = e1.element("Code");
				String strcode=code.getText();
				List<Node> glist1=e1.selectNodes("SubItems/NISimple");
				Iterator git1 = glist1.iterator();
				while(git1.hasNext()) {
					Element e2 = (Element) git1.next();
					Element value = e2.element("Value");
					strvalue=value.getText();
					hashMap2.put(strcode,strvalue);
				}
				List<Node> glist2=e1.selectNodes("SubItems/NIFree");
				Iterator git2 = glist2.iterator();
				while(git2.hasNext()) {
					Element e2 = (Element) git2.next();
					Element value = e2.element("Value");
					strvalue=value.getText();
					hashMap4.put(strcode,strvalue);
				}
			}

			// 解析NILink节点
			Iterator lit = llist.iterator();
			while(lit.hasNext()) {
				String strvalue ="";
				Element e1 = (Element) lit.next();
				Element code = e1.element("Code");
				String strcode=code.getText();
				Element value = e1.element("Value");
				if(value != null) {
					strvalue = value.getText();
					hashMap6.put(strcode, strvalue);
				}
			}

			Iterator it = list.iterator();
			Iterator nit1 = slist.iterator();
			while(nit1.hasNext()) {
				Element e1 = (Element) nit1.next();
				Element code = e1.element("Code");
				String strcode=code.getText();
				Element value = e1.element("Value");
				String strvalue=value.getText();
					hashMap1.put(strcode,strvalue);
			}
			Iterator nit = slist.iterator();
			while(nit.hasNext()) {
				Element e1 = (Element) nit.next();
				while(it.hasNext()) {
					Element e = (Element) it.next();
					Element Data = e.element("Data");

					Element Tag = e.element("Tag");
					if (Tag != null) {
						if ((!Tag.getText().equals("")) && (!Tag.getText().equals("#TABLECELL:IC"))) {
							String row = e.attributeValue("row");
							String column = e.attributeValue("column");
							String Input2 = StringUtils.substringAfter(StringUtils.substringAfter(StringUtils.substringBefore(Tag.getText(), "|"), "#"), "#");
							TMap.put(e.attributeValue("row")+" "+e.attributeValue("column"),Tag.getText());
						}
					}
					if (Data != null) {
						String Stringtemp="";
						if (Data.getText().indexOf("\\\'")!=-1) {
							String DataTemp=Data.getText();
							DataTemp="{"+StringUtils.substringAfter(DataTemp,"}}");
							InputStream in = new ByteArrayInputStream(DataTemp.getBytes());
							int ch=in.read();
							List<Character> bufferrtf = new ArrayList<Character>();
							while (ch > 0) {
								if (ch == '\\') {
									ch = in.read();
									if (ch == '\'') {
										char ch1 = (char) in.read();
										char ch2 = (char) in.read();
										if (ch1 == '9' && (ch2 == '3' || ch2 == '4')) {

										}
										ch = Character.digit(ch1, 16) * 16 + Character.digit(ch2, 16);
										bufferrtf.add((char) ch);
									} else if ((ch == '.') || (ch == '{') || (ch == '}')||(ch=='/')) {
										bufferrtf.add((char) ch);
									}
									ch=in.read();
								}
								///添加数字序号
								else if(ch=='0'||ch=='1'||ch=='2'||ch=='3'||ch=='4'||ch=='5'||ch=='6'||ch=='7'||ch=='8'||ch=='9')
								{
									int ch2=in.read();
									if (ch2 == '.') {
										bufferrtf.add((char) ch);
										bufferrtf.add((char) ch2);
									}
									ch=ch2;
								}
								else if((ch == '.')||(ch=='/')||(ch==':'))
								{
									bufferrtf.add((char) ch);
									ch=in.read();
								}
								else
								{
									ch=in.read();
								}
							}
							byte[] byteArr = new byte[bufferrtf.size()];
							for (int i = 0; i < bufferrtf.size(); i++) {
								ch = bufferrtf.get(i);
								if (ch < 0x80) {
									byteArr[i] = (byte) ch;
								}
							/*else if (ch=='0'||ch=='1'||ch=='2'||ch=='3'||ch=='4'||ch=='5'||ch=='6'||ch=='7'||ch=='8'||ch=='9'||ch == '.')
		                            {
		                            	byteArr[i] = (byte) ch;
		                            }*/
								else if (ch < '\u00FF') {
									char a = toHex(ch / 16);
									char b = toHex(ch % 16);
									String t = "" + a + b;
									int hb = Integer.parseInt(t, 16);
									byteArr[i] = (byte) hb;
								}
							}
							Stringtemp = new String(byteArr, "gbk");
						}
						else
						{
							Stringtemp=Data.getText();
						}
						Stringtemp=Stringtemp.replaceAll("(\n|\r\n|\n\r|\r)", "<br>");
						//加这里
						Iterator it2 = slist.iterator();

					/*	while(it2.hasNext()) {
							Element ee = (Element) it2.next();
							Element text = ee.element("Text");
							String strtext=text.getText();
							Element ValueCode = ee.element("ValueCode");
							String strValueCode=ValueCode.getText();
							Element Code = ee.element("Code");
							String strCode=Code.getText();
							if(null !=strValueCode && !"".equals(strValueCode) && null!=strtext && !"".equals(strtext)){
								if(null!=strCode && (strCode.equals("O0005") || strCode.equals("O0229")
										|| strCode.equals("O0242")
										|| strCode.equals("O0399")
										||strCode.equals("O0416")
										||strCode.equals("O0402")
										||strCode.equals("O0067")
										|| strCode.equals("O0157")
										|| strCode.equals("O0174")
										|| strCode.equals("O0173")
										|| strCode.equals("O0083")
										|| strCode.equals("O0354")
										|| strCode.equals("O0357")|| strCode.equals("O0218"))){
									duigou.put(strtext,strValueCode);
								}

							}

						}*/
						if(null!=duigou){
							Set<String> keys = duigou.keySet();
							for(String key :keys){
								if(Stringtemp.indexOf(key) !=-1 || "婚姻".equals(Stringtemp)) {
									System.out.print(Stringtemp);
									if("婚姻".equals(Stringtemp)){
										Stringtemp=Stringtemp+"&nbsp;"+(String)duigou.get("婚姻：");
									}else{
										Stringtemp=Stringtemp+"&nbsp;"+(String)duigou.get(key);
									}


								}
							}
						}
						if (Tag != null) {
							if ((!Tag.getText().equals("")) && (!Tag.getText().equals("#TABLECELL:IC"))) {
								Set<String> keys = hashMap1.keySet();
								for(String key :keys){
									if(Tag.getText().contains(key)) {

										//如果隐私化参数是Y则执行隐私化，如果传的N则放开所有隐私化，参数默认传Y
										if("Y".equals(privacy) || null==privacy){
										if (Stringtemp.contains("{") && Stringtemp.contains("姓名") && !Stringtemp.contains("联系人姓名")) {
											if (null != hashMap1.get(key)) {
												StringBuilder sb = new StringBuilder((String) hashMap1.get(key));
												//排除姓名过短，导致加密报错的情况
												if (sb.length() > 1) {
													sb = sb.replace(1, sb.length(), "**");
												}
												hashMap1.put(key, sb.toString());
											}
										}
										if (Stringtemp.contains("{") && Stringtemp.contains("电话")) {
											if (null != hashMap1.get(key)) {
												StringBuilder sb = new StringBuilder((String) hashMap1.get(key));
												//排除电话为空时 只有一个”-“的影响
												if (sb.length() > 3) {
													sb = sb.replace(3, 7, "****");
												}
												hashMap1.put(key, sb.toString());
											}
										}
										if (Stringtemp.contains("{") && Stringtemp.contains("身份证号")) {
											if (null != hashMap1.get(key)) {
												StringBuilder sb = new StringBuilder((String) hashMap1.get(key));
												//排除身份证号为空时 加密报错的情况
												if (sb.length() > 10) {
													sb = sb.replace(10, sb.length(), "********");
												}
												hashMap1.put(key, sb.toString());
											}
										}
									}
										if(null!=Stringtemp){
											Stringtemp=Stringtemp+(String)hashMap1.get(key);
										}else{
											Stringtemp=(String)hashMap1.get(key);
										}

									}

								}

							}
							if ((!Tag.getText().equals("")) && (!Tag.getText().equals("#TABLECELL:IC"))) {
								Set<String> keys = hashMap2.keySet();
								for(String key :keys){
									if(Tag.getText().contains(key)){
										if(null!=Stringtemp){
											Stringtemp=Stringtemp+(String)hashMap2.get(key);
										}else{
											Stringtemp=(String)hashMap2.get(key);
										}
									}

								}

							}
							if ((!Tag.getText().equals("")) && (!Tag.getText().equals("#TABLECELL:IC"))) {
								Set<String> keys = hashMap3.keySet();
								for(String key :keys){
									if(Tag.getText().contains(key)){
										if(null!=Stringtemp){
											Stringtemp=Stringtemp+(String)hashMap3.get(key);
										}else{
											Stringtemp=(String)hashMap3.get(key);
										}
									}

								}

							}
							if ((!Tag.getText().equals("")) && (!Tag.getText().equals("#TABLECELL:IC"))) {
								Set<String> keys = hashMap4.keySet();
								for(String key :keys){
									if(Tag.getText().contains(key)){
										if(null!=Stringtemp){
											Stringtemp=Stringtemp+(String)hashMap4.get(key);
										}else{
											Stringtemp=(String)hashMap4.get(key);
										}
									}

								}

							}
							if ((!Tag.getText().equals("")) && (!Tag.getText().equals("#TABLECELL:IC"))) {
								Set<String> keys = hashMap5.keySet();
								for(String key :keys){
									if(Tag.getText().contains(key)){
										if(null!=Stringtemp){
											Stringtemp=Stringtemp+(String)hashMap5.get(key);
										}else{
											Stringtemp=(String)hashMap5.get(key);
										}
									}

								}

							}
						}
						vMap.put(e.attributeValue("row") + " " + e.attributeValue("column"), Stringtemp);
					}
				}
			}

			//画表格程序
			List<Node> SheetModel=document.selectNodes("/Spread/Presentation/Sheets/Sheet/SpanModels/DataArea/CellRange");
			Iterator sheets=SheetModel.iterator();
			while(sheets.hasNext())
			{
				Element sheet=(Element)sheets.next();
				int Row=Integer.parseInt(sheet.attributeValue("Row"));
				int Column=Integer.parseInt(sheet.attributeValue("Column"));
				int RowCount=Integer.parseInt(sheet.attributeValue("RowCount"));
				int ColumnCount=Integer.parseInt(sheet.attributeValue("ColumnCount"));
				int RowTemp=Row+RowCount;
				int ColumnTemp=Column+ColumnCount;
				if (RowTemp>RowMax)
				{
					RowCount=RowMax-Row+1;
				}
				else
				{
					RowCount=RowCount;
				}
				if (ColumnTemp>ColumMax)
				{
					ColumnCount=ColumMax-Row+1;
				}
				else
				{
					ColumnCount=ColumnCount;
				}
				String Content=vMap.get(Row+" "+Column);
//				System.out.println("content---"+Content);
				if (Content==null)
				{
					Content="";
				}
				//填入实例值
				/*
		                if (TMap.containsKey(Row+" "+Column)) {
		                    if (TMap.get(Row+" "+Column)!=null) {
		                        Content = TMap.get(Row + " " + Column);
		                    }
		                    else{
		                        Content="";
		                    }
		                }
				 */
				String Style=rlsMap.get(Row+" "+Column);
				String Tag=TMap.get(Row+" "+Column);
				vMap.remove(Row+" "+Column);
//				System.out.println("vMap.remove--"+vMap.get(Row+" "+Column));
				rlsMap.remove(Row+" "+Column);
//				vMap.remove(Row+" "+Column);
//				System.out.println("vMap.remove--"+vMap.get(Row+" "+Column));
				if ((RowCount>1)&&(ColumnCount>1))
				{

					if ((Content!="")&&(Tag!=null)) {
						String[] ContentArray = Content.split("}");
						///拼整体的内容串
						String[] TagArray = Tag.split("\\$");
						String TempContent="";
						while(Content.indexOf("{")!=-1)
						{
							String TagPrint="";
							TempContent="";
							String TagContent=StringUtils.substringAfter(StringUtils.substringBefore(Content,"}"),"{");
							for (int  j= 1; j< TagArray.length; j++){
								if (TagArray[j].indexOf(TagContent)!=-1)
								{
									TagPrint="$"+TagArray[j];
									if (TVMap.get(TagPrint)==null)
									{
										TempContent=TempContent+"";
										break;
									}
									else
									{
										TempContent=TempContent+TVMap.get(TagPrint);
										break;
									}
								}
								else
								{
									String SpanceTag=TagArray[j].replace(" ", "");
									if (SpanceTag.indexOf(TagContent)!=-1)
									{
										TagPrint="$"+TagArray[j];
										if (TVMap.get(TagPrint)==null)
										{
											TempContent=TempContent+"";
										}
										else
										{
											TempContent=TempContent+TVMap.get(TagPrint);
										}
									}
								}
							}
							StringBuffer contentAll=new StringBuffer();
							String ContentBefore=StringUtils.substringBefore(Content,"{");
							String ContentAftter=StringUtils.substringAfter(Content,"}");
							contentAll.append(ContentBefore);
							contentAll.append(TempContent);
							contentAll.append(ContentAftter);
							Content=contentAll.toString();
							ContentBefore=null;
							ContentAftter=null;
							contentAll=null;
//							Content=StringUtils.substringBefore(Content,"{")+TempContent+StringUtils.substringAfter(Content,"}");
						}
						Content=Content.replace(" ", "&emsp;");
//						System.out.println("Content111---"+Content);
						String ContentReslut = "";
						if (ContentArray.length >= 2) {
							for (int i = 0; i < ContentArray.length; i++) {
								if (ContentArray[i].indexOf("{") != -1) {
									String ContentTemp = StringUtils.substringBefore(ContentArray[i], "{").trim() + "{" + StringUtils.substringAfter(ContentArray[i], "{").replace(" ", "") + "} ";
									ContentArray[i] = ContentTemp;
									if (ContentReslut.isEmpty()) {
										ContentReslut = ContentTemp;
									} else {
										ContentReslut = ContentReslut + ContentTemp;
									}
								}
								else
								{
									if (ContentReslut.isEmpty()) {
										ContentReslut = ContentArray[i];
									} else {
										ContentReslut = ContentReslut + ContentArray[i];
									}
								}
							}
							String[] ContentReslutArray = ContentReslut.split("\\s+");
							//String[] TagArray = Tag.split("\\$");
							if (ContentReslutArray.length >= 2) {
								//String Contentmontage="";
								for (int i = 0; i < ContentReslutArray.length; i++) {
									int ColumnCountTemp = ColumnCount / ContentReslutArray.length;
									int ColumnLast=ColumnCount % ContentReslutArray.length;
									int ColumnNew = Column + ColumnCountTemp * (i);
									String TagContent=StringUtils.substringAfter(StringUtils.substringBefore(ContentReslutArray[i],"}"),"{");
									String TagPrint="";
									String ContentPrint=ContentReslutArray[i];
									for (int  j= 1; j< TagArray.length; j++){
										if (TagArray[j].indexOf(TagContent)!=-1)
										{
											TagPrint="$"+TagArray[j];
											if (TVMap.get(TagPrint)==null)
											{
												TempContent="";
												break;
											}
											else
											{
												TempContent=TVMap.get(TagPrint);
												break;
											}
										}
										else
										{
											String SpanceTag=TagArray[j].replace(" ", "");
											if (SpanceTag.indexOf(TagContent)!=-1)
											{
												TagPrint="$"+TagArray[j];
												if (TVMap.get(TagPrint)==null)
												{
													TempContent="";
												}
												else
												{
													TempContent=TVMap.get(TagPrint);
												}
											}
										}
									}
									if (ContentReslutArray[i].indexOf("{")!= -1)
									{
										String BeforeStr=StringUtils.substringBefore(ContentReslutArray[i],"{");
										String AfterStr=StringUtils.substringAfter(ContentReslutArray[i], "}");
										ContentPrint=BeforeStr+TempContent+AfterStr;
									}
									else
									{

									}
									if ((Style == null)&&(i==ContentReslutArray.length-1)) {
										ColumnLast=ColumnLast+ColumnCountTemp;
										SheetArr[Row][ColumnNew] = "<td style=\"font-size:15px;font-family:SimSun;\" rowspan=" + RowCount + "  colspan=" + ColumnLast + " Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\" HisData=\""+ContentPrint+"\">" + ContentPrint + "</td>"+"<td style=\"font-size:15px;font-family:SimSun;\" rowspan=" + RowCount + " colspan=" + ColumnCount + ">"+Content+"</td>";
									}
									else if (Style == null)
									{
										SheetArr[Row][ColumnNew] = "<td style=\"font-size:15px;font-family:SimSun;\" rowspan=" + RowCount + "  colspan=" + ColumnCountTemp + " Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\" HisData=\""+ContentPrint+"\">" + ContentPrint + "</td>";
									}
									else if ((Style!=null)&&(i==0)) {
										String StyleContent = sMap.get(Style);
										if (StyleContent.indexOf("border-right") != -1) {
											StyleContent=StringUtils.substringBefore(StringUtils.substringAfter(StyleContent,"{"),"}");
											String[] StyleContentArray = StyleContent.split(";");
											String StyleTemp = "";
											for (int k = 0; k < StyleContentArray.length; k++) {
												if (StyleContentArray[k].indexOf("border-right") != -1) {
													StyleTemp = StyleTemp;
												} else {
													if (StyleTemp.isEmpty()) {
														StyleTemp = StyleContentArray[k];
													}
													else{
														StyleTemp = StyleTemp + ";" + StyleContentArray[k];
													}
												}
											}
											SheetArr[Row][ColumnNew] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCountTemp + " style=\"" + StyleTemp + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										} else {
											SheetArr[Row][ColumnNew] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCountTemp + " class=\"S" + Style + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										}
									}
									else if ((Style!=null)&&(i==ContentReslutArray.length-1)) {
										String StyleContent = sMap.get(Style);
										if (StyleContent.indexOf("border-left") != -1) {
											StyleContent=StringUtils.substringBefore(StringUtils.substringAfter(StyleContent,"{"),"}");
											String[] StyleContentArray = StyleContent.split(";");
											String StyleTemp = "";
											for (int k = 0; k < StyleContentArray.length; k++) {
												if (StyleContentArray[k].indexOf("border-left") != -1) {
													StyleTemp = StyleTemp;
												} else {
													if (StyleTemp.isEmpty()) {
														StyleTemp = StyleContentArray[k];
													}
													else {
														StyleTemp = StyleTemp + ";" + StyleContentArray[k];
													}
												}
											}
											ColumnLast=ColumnLast+ColumnCountTemp;
											SheetArr[Row][ColumnNew] = "<td rowspan=" + RowCount + "  colspan=" + ColumnLast + " style=\"" + StyleTemp + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\" HisData=\""+TempContent+"\">" + ContentPrint + "</td>"+"<td rowspan=" + RowCount + " colspan=" + ColumnCount + " class=\"S" + Style + "\">"+Content+"</td>";
										} else {
											ColumnLast=ColumnLast+ColumnCountTemp;
											SheetArr[Row][ColumnNew] = "<td rowspan=" + RowCount + "  colspan=" + ColumnLast + " class=\"S" + Style + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\" HisData=\""+TempContent+"\">" + ContentPrint + "</td>"+"<td rowspan=" + RowCount + " colspan=" + ColumnCount + " class=\"S" + Style + "\">"+Content+"</td>";
										}
									}
									else{
										String StyleContent = sMap.get(Style);
										if ((StyleContent.indexOf("border-right") != -1)||(StyleContent.indexOf("border-left") != -1)) {
											StyleContent=StringUtils.substringBefore(StringUtils.substringAfter(StyleContent,"{"),"}");
											String[] StyleContentArray = StyleContent.split(";");
											String StyleTemp = "";
											for (int k = 0; k < StyleContentArray.length; k++) {
												if ((StyleContentArray[k].indexOf("border-left") != -1)||(StyleContentArray[k].indexOf("border-right")!= -1)) {
													StyleTemp = StyleTemp;
												} else {
													if (StyleTemp.isEmpty()) {
														StyleTemp = StyleContentArray[k];
													}
													else {
														StyleTemp = StyleTemp + ";" + StyleContentArray[k];
													}
												}
											}
											SheetArr[Row][ColumnNew] = "<td rowspan=" + RowCount + " rowspan=" + RowCount + "  colspan=" + ColumnCountTemp + " style=\"" + StyleTemp + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\" HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										} else {
											SheetArr[Row][ColumnNew] = "<td rowspan=" + RowCount + " rowspan=" + RowCount + "  colspan=" + ColumnCountTemp + " class=\"S" + Style + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										}

									}
								}

							}
							else {
								if (ContentReslut.indexOf("{")!= -1)
								{
									///可能存在BUG根据生成文件进行调整
									for (int  j= 1; j< TagArray.length; j++){
										String TagContent='$'+TagArray[j];
										TempContent=TempContent+TVMap.get(TagContent);
									}
									String BeforeStr=StringUtils.substringBefore(Content,"{");
									String AfterStr=StringUtils.substringAfter(Content, "}");
									Content=BeforeStr+TempContent+AfterStr;
//									System.out.println("Content333---"+Content);
									if (Style == null) {
										SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+TempContent+"\">" + Content + "</td>";
									} else {
										SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+TempContent+"\">" + Content + "</td>";
									}
								}
								else
								{
									if (Style == null) {
										SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+Content+"\">" + Content + "</td>";
									} else {
										SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+Content+"\">" + Content + "</td>";
									}
								}
							}
						}
						else
						{
							if (Content.indexOf("{")!= -1)
							{
								//String[] TagArray = Tag.split("\\$");
								for (int  j= 1; j< TagArray.length; j++){
									String TagContent='$'+TagArray[j];
									TempContent=TVMap.get(TagContent);
									String BeforeStr=StringUtils.substringBefore(Content,"{");
									String AfterStr=StringUtils.substringAfter(Content, "}");
									Content=BeforeStr+TempContent+AfterStr;
//									System.out.println("Content444--"+Content);
									if (Style == null) {
										SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\" HisData=\""+TempContent+"\">" + Content + "</td>";
									} else {
										SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\" HisData=\""+TempContent+"\">" + Content + "</td>";
									}
								}
							}
							else
							{
								if (Style == null) {
									SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\" HisData=\""+Content+"\">" + Content + "</td>";
								} else {
									SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\" HisData=\""+Content+"\">" + Content + "</td>";
								}
							}
						}
					}
					else if (Content!="")
					{
						//Content=Content.replaceAll("(\n)", "<br/>");
						if (Style == null) {
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+Content+"\">" + Content + "</td>";
						} else {
							String SMYS="<td rowspan=" + RowCount + "  colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\" HisData=\""+Content+"\">" + Content + "</td>";
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\" HisData=\""+Content+"\">" + Content + "</td>";
						}
					}
					else
					{
						if (Style == null) {
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"></td>";
						} else {
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"></td>";
						}
					}
				}
				else if((RowCount>1)&&(ColumnCount==1))
				{
					if (Tag!=null)
					{
						String TempContent="";
						if (Content.indexOf("{")!= -1)
						{
							String[] TagArray = Tag.split("\\$");
							for (int  j= 1; j< TagArray.length; j++){
								String TagContent='$'+TagArray[j];
								TempContent=TVMap.get(TagContent);
								String BeforeStr=StringUtils.substringBefore(Content,"{");
								String AfterStr=StringUtils.substringAfter(Content, "}");
								Content=BeforeStr+TempContent+AfterStr;
//								System.out.println("Content444"+Content);
							}
							if (Style==null) {
								SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" rowspan=" + RowCount + " Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+TempContent+"\">" + Content + "</td>";
							}
							else{
								SheetArr[Row][Column] = "<td rowspan=" + RowCount + " class=\"S"+Style+ "\"  Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+TempContent+"\">" + Content + "</td>";
							}
						}
					}
					else
					{
						if (Style==null) {
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" rowspan=" + RowCount + " Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
						else{
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + " class=\"S"+Style+ "\"  Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
					}
				}
				else if((RowCount==1)&&(ColumnCount>1))
				{
					if ((Content!="")&&(Tag!=null)) {
						String[] ContentArray = Content.split("}");
						///拼整体的内容串
						String[] TagArray = Tag.split("\\$");
						String TempContent="";
						while(Content.indexOf("{")!=-1)
						{
							/*num++;
							if(num==1){
								LOG.info("Content----"+Content);
								LOG.info("tempId----"+tempId);

							}
							if("{术前记录.术前诊断}".equals(Content)||"{术前记录.拟施手术}".equals(Content)){
								LOG.info("Content----"+Content);
								LOG.info("tempId----"+tempId);

							}
							LOG.info("进入循环----"+num+"次");*/
							String TagPrint="";
							TempContent="";
							String TagContent=StringUtils.substringAfter(StringUtils.substringBefore(Content,"}"),"{");
							for (int  j= 1; j< TagArray.length; j++){
								String rtfErrorStr="";
								String TagContentErrorStr="";
								if(TagArray[j].indexOf("(")>-1){
									 rtfErrorStr=TagArray[j].substring(TagArray[j].indexOf("("),TagArray[j].indexOf(")")+1);
									 TagContentErrorStr=TagArray[j].substring(TagArray[j].indexOf("(")+1,TagArray[j].indexOf(")"));

								}
								if (TagArray[j].replace(rtfErrorStr,"").indexOf(TagContent.replace(TagContentErrorStr,""))!=-1)
								{
									TagPrint="$"+TagArray[j];
									if (TVMap.get(TagPrint)==null)
									{
										TempContent=TempContent+"";
										break;
									}
									else
									{
										TempContent=TempContent+TVMap.get(TagPrint);
										break;
									}
								}
								else
								{
									String SpanceTag=TagArray[j].replace(" ", "");
									if (SpanceTag.indexOf(TagContent)!=-1)
									{
										TagPrint="$"+TagArray[j];
										if (TVMap.get(TagPrint)==null)
										{
											TempContent=TempContent+"";
										}
										else
										{
											TempContent=TempContent+TVMap.get(TagPrint);
										}
									}
								}
							}
							StringBuffer contentAll=new StringBuffer();
							String ContentBefore=StringUtils.substringBefore(Content,"{");
							String ContentAftter=StringUtils.substringAfter(Content,"}");
							contentAll.append(ContentBefore);
							contentAll.append(TempContent);
							contentAll.append(ContentAftter);
							Content=contentAll.toString();
							ContentBefore=null;
							ContentAftter=null;
							contentAll=null;
//							Content=StringUtils.substringBefore(Content,"{")+TempContent+StringUtils.substringAfter(Content,"}");
//							System.out.println("Content555"+Content);
						}
						Content=Content.replace(" ", "&emsp;");
						String ContentReslut = "";
						if (ContentArray.length >= 2) {
							for (int i = 0; i < ContentArray.length; i++) {
								if (ContentArray[i].indexOf("{") != -1) {
									String ContentTemp = StringUtils.substringBefore(ContentArray[i], "{").trim() + "{" + StringUtils.substringAfter(ContentArray[i], "{").replace(" ", "") + "} ";
									ContentArray[i] = ContentTemp;
									if (ContentReslut.isEmpty()) {
										ContentReslut = ContentTemp;
									} else {
										ContentReslut = ContentReslut + ContentTemp;
									}
								}
								else
								{
									if (ContentReslut.isEmpty()) {
										ContentReslut = ContentArray[i];
									} else {
										ContentReslut = ContentReslut + ContentArray[i];
									}
								}
							}
							String[] ContentReslutArray = ContentReslut.split("\\s+");
							//String[] TagArray = Tag.split("\\$");
							if (ContentReslutArray.length >= 2) {
								//String Contentmontage="";
								for (int i = 0; i < ContentReslutArray.length; i++) {
									int ColumnCountTemp = ColumnCount / ContentReslutArray.length;
									int ColumnLast=ColumnCount % ContentReslutArray.length;
									int ColumnNew = Column + ColumnCountTemp * (i);
									String TagContent=StringUtils.substringAfter(StringUtils.substringBefore(ContentReslutArray[i],"}"),"{");
									String TagPrint="";
									String ContentPrint=ContentReslutArray[i];
									for (int  j= 1; j< TagArray.length; j++){
										if (TagArray[j].indexOf(TagContent)!=-1)
										{
											TagPrint="$"+TagArray[j];
											if (TVMap.get(TagPrint)==null)
											{
												TempContent="";
												break;
											}
											else
											{
												TempContent=TVMap.get(TagPrint);
												break;
											}
										}
										else
										{
											String SpanceTag=TagArray[j].replace(" ", "");
											if (SpanceTag.indexOf(TagContent)!=-1)
											{
												TagPrint="$"+TagArray[j];
												if (TVMap.get(TagPrint)==null)
												{
													TempContent="";
												}
												else
												{
													TempContent=TVMap.get(TagPrint);
												}
											}
										}
									}
									if (ContentReslutArray[i].indexOf("{")!= -1)
									{
										String BeforeStr=StringUtils.substringBefore(ContentReslutArray[i],"{");
										String AfterStr=StringUtils.substringAfter(ContentReslutArray[i], "}");
										ContentPrint=BeforeStr+TempContent+AfterStr;
										/*if (Contentmontage.equals(""))
											{
												Contentmontage=ContentPrint;
											}
											else
											{
												Contentmontage=Contentmontage+"&nbsp;"+ContentPrint;
											}*/
									}
									else
									{
										/*if (Contentmontage.equals(""))
											{
												Contentmontage=ContentReslutArray[i];
											}
											else
											{
												Contentmontage=Contentmontage+"&nbsp;"+ContentReslutArray[i];
											}*/
										//Contentmontage=Contentmontage+ContentReslutArray[i];
									}
									if ((Style == null)&&(i==ContentReslutArray.length-1)) {
										ColumnLast=ColumnLast+ColumnCountTemp;
										SheetArr[Row][ColumnNew] = "<td style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnLast + " Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>"+"<td style=\"font-size:10.5px;font-family:SimSun;\"  colspan=" + ColumnCount + ">"+Content+"</td>";
									}
									else if (Style == null)
									{
										SheetArr[Row][ColumnNew] = "<td style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCountTemp + " Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
									}
									else if ((Style!=null)&&(i==0)) {
										String StyleContent = sMap.get(Style);
										if (StyleContent.indexOf("border-right") != -1) {
											StyleContent=StringUtils.substringBefore(StringUtils.substringAfter(StyleContent,"{"),"}");
											String[] StyleContentArray = StyleContent.split(";");
											String StyleTemp = "";
											for (int k = 0; k < StyleContentArray.length; k++) {
												if (StyleContentArray[k].indexOf("border-right") != -1) {
													StyleTemp = StyleTemp;
												} else {
													if (StyleTemp.isEmpty()) {
														StyleTemp = StyleContentArray[k];
													}
													else{
														StyleTemp = StyleTemp + ";" + StyleContentArray[k];
													}
												}
											}
											SheetArr[Row][ColumnNew] = "<td colspan=" + ColumnCountTemp + " style=\"" + StyleTemp + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										} else {
											SheetArr[Row][ColumnNew] = "<td colspan=" + ColumnCountTemp + " class=\"S" + Style + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										}
									}
									else if ((Style!=null)&&(i==ContentReslutArray.length-1)) {
										String StyleContent = sMap.get(Style);
										if (StyleContent.indexOf("border-left") != -1) {
											StyleContent=StringUtils.substringBefore(StringUtils.substringAfter(StyleContent,"{"),"}");
											String[] StyleContentArray = StyleContent.split(";");
											String StyleTemp = "";
											for (int k = 0; k < StyleContentArray.length; k++) {
												if (StyleContentArray[k].indexOf("border-left") != -1) {
													StyleTemp = StyleTemp;
												} else {
													if (StyleTemp.isEmpty()) {
														StyleTemp = StyleContentArray[k];
													}
													else {
														StyleTemp = StyleTemp + ";" + StyleContentArray[k];
													}
												}
											}
											ColumnLast=ColumnLast+ColumnCountTemp;
											SheetArr[Row][ColumnNew] = "<td colspan=" + ColumnLast + " style=\"" + StyleTemp + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>"+"<td colspan=" + ColumnCount + " class=\"S" + Style + "\">"+Content+"</td>";
										} else {
											ColumnLast=ColumnLast+ColumnCountTemp;
											SheetArr[Row][ColumnNew] = "<td colspan=" + ColumnLast + " class=\"S" + Style + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>"+"<td colspan=" + ColumnCount + " class=\"S" + Style + "\">"+Content+"</td>";
										}
									}
									else{
										String StyleContent = sMap.get(Style);
										if ((StyleContent.indexOf("border-right") != -1)||(StyleContent.indexOf("border-left") != -1)) {
											StyleContent=StringUtils.substringBefore(StringUtils.substringAfter(StyleContent,"{"),"}");
											String[] StyleContentArray = StyleContent.split(";");
											String StyleTemp = "";
											for (int k = 0; k < StyleContentArray.length; k++) {
												if ((StyleContentArray[k].indexOf("border-left") != -1)||(StyleContentArray[k].indexOf("border-right")!= -1)) {
													StyleTemp = StyleTemp;
												} else {
													if (StyleTemp.isEmpty()) {
														StyleTemp = StyleContentArray[k];
													}
													else {
														StyleTemp = StyleTemp + ";" + StyleContentArray[k];
													}
												}
											}
											SheetArr[Row][ColumnNew] = "<td colspan=" + ColumnCountTemp + " style=\"" + StyleTemp + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										} else {
											SheetArr[Row][ColumnNew] = "<td colspan=" + ColumnCountTemp + " class=\"S" + Style + "\"  Location=" + Row + "-" + ColumnNew + " Tag=\"" + TagPrint + "\" hidden=\"ture\"  HisData=\""+TempContent+"\">" + ContentPrint + "</td>";
										}

									}
								}

							}
							else {
								if (ContentReslut.indexOf("{")!= -1)
								{
									String HISDATAContent="";
									for (int  j= 1; j< TagArray.length; j++){
										String TagContent='$'+TagArray[j];
										TempContent=TVMap.get(TagContent);
										HISDATAContent=HISDATAContent+TempContent;
										String BeforeStr=StringUtils.substringBefore(Content,"{");
										String AfterStr=StringUtils.substringAfter(Content, "}");
										Content=BeforeStr+TempContent+AfterStr;
									}
									Content=Content.replace("null","");
									if (Style == null) {
										SheetArr[Row][Column] = "<td style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+HISDATAContent+"\">" + Content + "</td>";
									} else {
										SheetArr[Row][Column] = "<td colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+HISDATAContent+"\">" + Content + "</td>";
									}
								}
								else
								{
									Content=Content.replace("null","");
									if (Style == null) {
										SheetArr[Row][Column] = "<td style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+Content+"\">" + Content + "</td>";
									} else {
										SheetArr[Row][Column] = "<td colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+Content+"\">" + Content + "</td>";
									}
								}
							}
						}
						else
						{
							if (Content.indexOf("{")!= -1)
							{
								//String[] TagArray = Tag.split("\\$");
								for (int  j= 1; j< TagArray.length; j++){
									String TagContent='$'+TagArray[j];
									TempContent=TVMap.get(TagContent);
									String BeforeStr=StringUtils.substringBefore(Content,"{");
									String AfterStr=StringUtils.substringAfter(Content, "}");
									Content=BeforeStr+TempContent+AfterStr;
								}
							}
							Content=Content.replace("null","");
							if (Style == null) {
								SheetArr[Row][Column] = "<td style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+TempContent+"\">" + Content + "</td>";
							} else {
								SheetArr[Row][Column] = "<td colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+TempContent+"\">" + Content + "</td>";
							}
						}
					}
					else if (Content!="")
					{
						Content=Content.replace("null","");
						if (Style == null) {
							SheetArr[Row][Column] = "<td style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+Content+"\">" + Content + "</td>";
						} else {
							SheetArr[Row][Column] = "<td colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
					}
					else
					{
						Content=Content.replace("null","");
						if (Style == null) {
							SheetArr[Row][Column] = "<td style=\"font-size:15px;font-family:SimSun;\" colspan=" + ColumnCount + " Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"></td>";
						} else {
							SheetArr[Row][Column] = "<td colspan=" + ColumnCount + " class=\"S" + Style + "\"  Location=" + Row + "-" + Column + " Tag=\"" + Tag + "\"></td>";
						}
					}
				}
				else if((RowCount==1)&&(ColumnCount==1)&&(Content!=""))
				{
					if (Content.indexOf("{")!= -1)
					{
						String HISDATA="";
						String[] TagArray = Tag.split("\\$");
						for (int  j= 1; j< TagArray.length; j++){
							String TagContent='$'+TagArray[j];
							String TempContent=TVMap.get(TagContent);
							HISDATA=HISDATA+TempContent;
							String BeforeStr=StringUtils.substringBefore(Content,"{");
							String AfterStr=StringUtils.substringAfter(Content, "}");
							Content=BeforeStr+TempContent+AfterStr;
						}
						Content=Content.replace("null","");
						if (Style==null) {
							SheetArr[Row][Column] = "<td style=\"font-size:15px;font-family:SimSun;\" Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+HISDATA+"\">" + Content + "</td>";
						}
						else {
							SheetArr[Row][Column] = "<td class=\"S"+Style+"\"  Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+HISDATA+"\">" + Content + "</td>";
						}
					}
					else
					{
						Content=Content.replace("null","");
						if (Style==null) {
							SheetArr[Row][Column] = "<td style=\"font-size:15px;font-family:SimSun;\" Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
						else {
							SheetArr[Row][Column] = "<td class=\"S"+Style+"\"  Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
					}
				}
				else
				{
					if (Tag!=null)
					{
						String TempContent="";
						if (Content.indexOf("{")!= -1)
						{
							String[] TagArray = Tag.split("\\$");
							for (int  j= 1; j< TagArray.length; j++){
								String TagContent='$'+TagArray[j];
								TempContent=TVMap.get(TagContent);
								String BeforeStr=StringUtils.substringBefore(Content,"{");
								String AfterStr=StringUtils.substringAfter(Content, "}");
								Content=BeforeStr+TempContent+AfterStr;
							}
							if (Style==null) {
								Content=Content.replace("null","");
								SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" rowspan=" + RowCount + " Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+TempContent+"\">" + Content + "</td>";
							}
							else{
								Content=Content.replace("null","");
								SheetArr[Row][Column] = "<td rowspan=" + RowCount + " class=\"S"+Style+ "\"  Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+TempContent+"\">" + Content + "</td>";
							}
						}
					}
					else
					{
						Content=Content.replace("null","");
						if (Style==null) {
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + "  style=\"font-size:15px;font-family:SimSun;\" rowspan=" + RowCount + " Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
						else{
							SheetArr[Row][Column] = "<td rowspan=" + RowCount + " class=\"S"+Style+ "\"  Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
					}
					/*
						if (Style==null) {
							SheetArr[Row][Column] = "<td style=\"font-size:10.5px;font-family:SimSun;\" Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
						else {
							SheetArr[Row][Column] = "<td class=\"S"+Style+"\"  Location="+Row+"-"+Column+" Tag=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
						}
					 */
				}
				for(int rwi=Row;rwi<Row+RowCount;rwi++)
				{
					if (rwi==Row) {
						int ColumnCountTemp=Column + ColumnCount;
						if (ColumnCountTemp>ColumMax)
						{
							ColumnCountTemp=ColumMax;
						}
						for (int Coli = Column+1; Coli < ColumnCountTemp; Coli++) {
							if (SheetArr[rwi][Coli].equals("<td></td>"))
							{
								SheetArr[rwi][Coli] = "";
							}
						}
					}
					else
					{
						int ColumnCountTemp=Column + ColumnCount;
						if (ColumnCountTemp>ColumMax)
						{
							ColumnCountTemp=ColumMax;
						}
						for (int Coli = Column; Coli < ColumnCountTemp; Coli++) {
							if (SheetArr[rwi][Coli].equals("<td></td>")) {
								SheetArr[rwi][Coli] = "";
							}
						}
					}
				}
				Content=null;
			}

			//填充没有合并单元格的值
			if (!vMap.isEmpty())
			{
				Iterator<Map.Entry<String, String>> vMapit = vMap.entrySet().iterator();
				while (vMapit.hasNext()) {
					Map.Entry<String, String> entry = vMapit.next();
					String RowColumn = entry.getKey();
					String Style=rlsMap.get(RowColumn);
					rlsMap.remove(RowColumn);
					String[] Arry = RowColumn.split(" ");
					String Content=entry.getValue();
					/* if (TMap.containsKey(entry.getKey())){
		                        if (TMap.get(entry.getKey())!=null) {
		                            Content = TMap.get(entry.getKey());
		                        }
		                        else{
		                            Content="";
		                        }
		                    }*/
					int row = Integer.parseInt(Arry[0]);
					int column = Integer.parseInt(Arry[1]);
					String Tag=TMap.get(row+" "+column);
					/*if(Tag==null){
						System.out.println("TMapKey--"+row+" "+column);
						System.out.println("TMap---"+TMap);
						System.out.println("vMap---"+vMap);
					}*/
					if(/*Tag!=null&&*/SheetArr[row][column].equals("<td></td>")) {
						if (Content.indexOf("{")!= -1)
						{
							String HISDATA="";
							if(Tag!=null){
								String[] TagArray = Tag.split("\\$");
								for (int  j= 1; j< TagArray.length; j++){
									String TagContent='$'+TagArray[j];
									if (TagContent.contains("打印页码"))
									{
										String TempContent=String.valueOf(PageNum);
										HISDATA=HISDATA+TempContent;
										String BeforeStr=StringUtils.substringBefore(Content,"{");
										String AfterStr=StringUtils.substringAfter(Content, "}");
										Content=BeforeStr+TempContent+AfterStr;
										PageNum=PageNum+1;
									}
									else
									{
										String TempContent=TVMap.get(TagContent);
										HISDATA=HISDATA+TempContent;
										String BeforeStr=StringUtils.substringBefore(Content,"{");
										String AfterStr=StringUtils.substringAfter(Content, "}");
										Content=BeforeStr+TempContent+AfterStr;
									}
								}
							}
							if (Style==null) {
								Content=Content.replace("null","");
								SheetArr[row][column] = "<td style=\"font-size:15px;font-family:SimSun;\" Location="+row+"-"+column+" value=\""+Tag+"\"  HisData=\""+HISDATA+"\">" + Content + "</td>";
							}
							else {
								Content=Content.replace("null","");
								SheetArr[row][column] = "<td " + " class=\"S" + Style +"\" Location="+row+"-"+column+" value=\""+Tag+"\"  HisData=\""+HISDATA+"\">" + Content + "</td>";
							}
						}
						else
						{
							Content=Content.replace("null","");
							if (Style==null) {
								SheetArr[row][column] = "<td style=\"font-size:15px;font-family:SimSun;\" Location="+row+"-"+column+" value=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
							}
							else {
								SheetArr[row][column] = "<td " + " class=\"S" + Style +"\" Location="+row+"-"+column+" value=\""+Tag+"\"  HisData=\""+Content+"\">" + Content + "</td>";
							}
						}
					}
					Content=null;
				}
			}
			//填充没有合拼单元的样式
			if (!rlsMap.isEmpty())
			{
				Iterator<Map.Entry<String, String>> rlsMapit = rlsMap.entrySet().iterator();
				while (rlsMapit.hasNext()) {
					Map.Entry<String, String> entry = rlsMapit.next();
					String RowColumn = entry.getKey();
					String Style=rlsMap.get(RowColumn);
					String[] Arry = RowColumn.split(" ");
					int row = Integer.parseInt(Arry[0]);
					int column = Integer.parseInt(Arry[1]);
					if(SheetArr[row][column].equals("<td></td>")) {
						SheetArr[row][column] = "<td "+ " class=\"S"+Style+ "\"></td>";
					}
				}

			}

			StringBuffer Outbuffer=new StringBuffer();
			Outbuffer.append("  <!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\r\n" +
					"<html xmlns=\"http://www.w3.org/1999/xhtml\">\r\n" +
					"<head>\r\n" +
					"<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n" +
					"<title>test</title>\r\n" +
					"<script type=\"text/css\" >\r\n" +
					"*{ font-size:30px;}"+
					"\r\n" +
					"window.onload =initClick;\r\n" +
					"	function initClick(){\r\n" +
					"	var tds =document.getElementsByTagName('td');\r\n" +
					"	var trs =document.getElementsByTagName('tr');\r\n" +
					"        for (var i = 0; i < tds.length; i++)\r\n" +
					"            tds[i].onclick =doclick;\r\n" +
					"        for (var i = 0; i < trs.length; i++)\r\n" +
					"            trs[i].ondblclick =dodblclick;\r\n" +
					"	}\r\n" +
					"\r\n" +
					"function doclick (obj) {\r\n" +
					"var tds=document.getElementsByTagName('td')\r\n" +
					"	 if(obj.target.style.backgroundColor){\r\n" +
					"	 if(obj.target.style.backgroundColor=='red'){\r\n" +
					"	 obj.target.style.backgroundColor=\"#BEBEBE\";\r\n" +
					"	 }else{\r\n" +
					"	 obj.target.style.backgroundColor=\"\";\r\n" +
					"	 }\r\n" +
					"	 return\r\n" +
					"}\r\n" +
					"	 if(obj.target.innerHTML){\r\n" +
					"	 obj.target.style.backgroundColor=\"#BEBEBE\";	  \r\n" +
					"}\r\n" +
					"}\r\n" +
					"\r\n" +
					"function dodblclick (obj)  {\r\n" +
					"	 if(obj.target.style.backgroundColor){\r\n" +
					"	 if(obj.target.style.backgroundColor=='red'){\r\n" +
					"	 obj.target.style.backgroundColor=\"#BEBEBE\";\r\n" +
					"	 }else{\r\n" +
					"	 for(var  i =0;i< obj.currentTarget.children.length;i++){\r\n" +
					"	 obj.currentTarget.children[i].style.backgroundColor=\"\";\r\n" +
					"	 	}\r\n" +
					"	 }\r\n" +
					"	 return\r\n" +
					"}\r\n" +
					"	 if(obj.currentTarget.innerHTML){\r\n" +
					"	 	for(var  i =0;i< obj.currentTarget.children.length;i++){\r\n" +
					"	 	if(obj.currentTarget.children[i].innerHTML){\r\n" +
					"		 obj.currentTarget.children[i].style.backgroundColor=\"#BEBEBE\";	  \r\n" +
					"		}\r\n" +
					"	}\r\n" +
					"}\r\n" +
					"}\r\n" +
					"\r\n" +
					"</script>\r\n" +
					"<style type=\"text/css\">\r\n" +
					"table{\r\n" +
					" border-collapse:collapse;\r\n" +
					" table-layout:fixed;\r\n" +
					"\r\n" +
					"}\r\n" +
					"\r\n" +
					"table  tr tr{\r\n" +
					"		\r\n" +
					"		font-weight:300; \r\n" +
					"		font-size:80px\r\n" +
					"\r\n" +
					"}");
			///输出样式语句
			if (!sMap.isEmpty())
			{
				Iterator<Map.Entry<String, String>> sMapit = sMap.entrySet().iterator();
				while (sMapit.hasNext()) {
					Map.Entry<String, String> entry = sMapit.next();
					//System.out.println(entry.getValue());
					Outbuffer.append(entry.getValue());
				}
			}

			///解析对勾
			/*List<Node> PSShapeDocument=document.selectNodes("/Spread/Drawing/Sheets/Sheet/DrawingContainer/ContainedObjects/PSShape");
			Iterator PSShapeIt=PSShapeDocument.iterator();
			int CountPSShape=0;
			while(PSShapeIt.hasNext())
			{
				CountPSShape=CountPSShape+1;
				Element PSShape=(Element)PSShapeIt.next();
				Element TopElement=PSShape.element("Top");
				Element LeftElement=PSShape.element("Left");
				Element TagElement=PSShape.element("Tag");
				Element	HeightElement=PSShape.element("Height");
				Element	WidthElement=PSShape.element("Width");
				if (TagElement==null)
				{
					continue;
				}
				String	StipulateHeight=HeightElement.getText();
				String	StipulateWidth=WidthElement.getText();
				String 	Tag=TagElement.getText();
				int  	Top=Integer.parseInt(TopElement.getText())+Integer.parseInt(StipulateHeight)/2;
				int		Left=Integer.parseInt(LeftElement.getText())+Integer.parseInt(StipulateWidth)/2;
				int X=0,Y=0,Height=0,Width=0;
				for(int i=0;i<=RowMax-1;i++)
				{
					if (i==0)
					{
						if (RowMap.get(String.valueOf(i))==null)
						{
							Height=20;
						}
						else
						{
							Height=Integer.parseInt(RowMap.get(String.valueOf(i)));
						}
					}
					else
					{
						if (RowMap.get(String.valueOf(i))==null)
						{
							Height=Height+20;
						}
						else
						{
							Height=Height+Integer.parseInt(RowMap.get(String.valueOf(i)));
						}
					}
					if (Height>Top)
					{
						X=i;
						break;
					}
				}
				for(int j=0;j<=ColumMax-1;j++)
				{
					if (j==0)
					{
						Width=Integer.parseInt(ColumnMap.get(String.valueOf(j)))+2;
					}
					else
					{
						Width=Width+Integer.parseInt(ColumnMap.get(String.valueOf(j)))+2;
					}
					if (Width>Left)
					{
						Y=j;
						break;
					}
					if (j==ColumMax-1)
					{
						Y=j;
						break;
					}
				}
				if (SheetArr[X][Y].contains("Tag=\"null\""))
				{
					String TempSheet=SheetArr[X][Y];
					String TempTag="Tag=\""+Tag+"\"";
					TempSheet=TempSheet.replace("Tag=\"null\"", TempTag);
					SheetArr[X][Y]=TempSheet;
				}
				if (!SheetArr[X][Y].contains("Tag"))
				{
			*//*		String TempSheet=SheetArr[X][Y];
					String TempTag="Tag=\""+Tag+"\"";
					String BeforeString=StringUtils.substringBefore(TempSheet, ">");
					String AfterString=StringUtils.substringAfter(TempSheet, ">");
					TempSheet=BeforeString+" "+ TempTag +">"+AfterString;
					//TempSheet=TempSheet.replace("Tag=null", TempTag);
					SheetArr[X][Y]=TempSheet;*//*
				}
				List<Node> ItemList=document.selectNodes("/Spread/Drawing/Sheets/Sheet/DrawingContainer/ContainedObjects/PSShape["+CountPSShape+"]/Shape/PathPoints/Item");
				//List<Node> ItemList=PSShape.getDocument().selectNodes("/Shape/PathPoints/Item");
				Iterator ItemIt=ItemList.iterator();
				int Count=0;
				while(ItemIt.hasNext())
				{
					Element Item=(Element)ItemIt.next();
					String Coordinate=Item.getText();
					String x=StringUtils.substringAfter(StringUtils.substringBefore(Coordinate,","),"X=");
					String y=StringUtils.substringBefore(StringUtils.substringAfter(Coordinate,"Y="),"}");
					if (Count==0)
					{
						Outbuffer.append("context.moveTo("+x+","+y+");\r\n");
					}
					else
					{
						Outbuffer.append("context.lineTo("+x+","+y+");\r\n");
					}
					Count=Count+1;
				}
				Outbuffer.append("   context.stroke();   \r\n" +
						"   context.restore();  \r\n" +
						"   context.fill();");
			}*/

			///根据具体行列大小决定表格大小
			int  Height=1500,Width=900;
			for(int i=0;i<RowMax;i++)
			{
				if(i==0)
				{
					if (RowMap.get(String.valueOf(i))==null)
					{
						Height=20;
					}
					else
					{
						Height=Integer.parseInt(RowMap.get(String.valueOf(i)));
					}
				}
				else
				{
					if (RowMap.get(String.valueOf(i))==null)
					{
						Height=Height+20;
					}
					else
					{
						Height=Height+Integer.parseInt(RowMap.get(String.valueOf(i)));
					}
				}
			}
			for(int j=0;j<ColumMax;j++)
			{
				if(j==0)
				{
					if (ColumnMap.get(String.valueOf(j))==null)
					{
						Width=30;
					}
					else
					{
						Width=Integer.parseInt(ColumnMap.get(String.valueOf(j)))+2;
					}
				}
				else
				{
					if (ColumnMap.get(String.valueOf(j))==null)
					{
						Width=Width+30;
					}
					else
					{
						Width=Width+Integer.parseInt(ColumnMap.get(String.valueOf(j)))+2;
					}
				}
			}
			//添加水印
			Outbuffer.append("</style>");
			Outbuffer.append("</head>\r\n" +
					"<body id='water' style=\"width:"+Width+"px; height:"+Height+"px; padding-right:0px; padding-left:0px; padding-bottom:0px; padding-top:0px; margin-left:0px; margin-top:0px\">\r\n");
			/*if(null==watermark || "".equals(watermark)){
				Outbuffer.append("<script>\r\n" +
						"watermark({'watermark_txt':});");
			}else{
				Outbuffer.append("<script>\r\n" +
						"watermark({'watermark_txt':'"+watermark+"'});");
			}
			Outbuffer.append(
					"function watermark(settings) {\n" +
					"    //默认设置\n" +
					"    var defaultSettings = {\n" +
					"        watermark_txt: \"text\",\n" +
					"        watermark_x: 20, //水印起始位置x轴坐标\n" +
					"        watermark_y: 20, //水印起始位置Y轴坐标\n" +
					"        watermark_rows: 20, //水印行数\n" +
					"        watermark_cols: 20, //水印列数\n" +
					"        watermark_x_space: 100, //水印x轴间隔\n" +
					"        watermark_y_space: 50, //水印y轴间隔\n" +
					"        watermark_color: '#aaa', //水印字体颜色\n" +
					"        watermark_alpha: 0.2, //水印透明度\n" +
					"        watermark_fontsize: '15px', //水印字体大小\n" +
					"        watermark_font: '宋体', //水印字体\n" +
					"        watermark_width: 210, //水印宽度\n" +
					"        watermark_height: 80, //水印长度\n" +
					"        watermark_angle: 20 //水印倾斜度数\n" +
					"    };\n" +
					"    if (arguments.length === 1 && typeof arguments[0] === \"object\") {\n" +
					"        var src = arguments[0] || {};\n" +
					"        for (key in src) {\n" +
					"            if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) continue;\n" +
					"            else if (src[key]) defaultSettings[key] = src[key];\n" +
					"        }\n" +
					"    }\n" +
					"    var oTemp = document.createDocumentFragment();\n" +
					"    //获取页面最大宽度\n" +
					"    var page_width = Math.max(document.getElementById(\"water\").scrollWidth, document.getElementById(\"water\").clientWidth);\n" +
					"    var cutWidth = page_width * 0.0150;\n" +
					"    var page_width = page_width - cutWidth;\n" +
					"    //获取页面最大高度\n" +
					"    var page_height = Math.max(document.getElementById(\"water\").scrollHeight, document.getElementById(\"water\").clientHeight) + 450;\n" +
					"    page_height = Math.max(page_height, window.innerHeight - 30);\n" +
					"    //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔\n" +
					"    if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {\n" +
					"        defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));\n" +
					"        defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));\n" +
					"    }\n" +
					"    //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔\n" +
					"    if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {\n" +
					"        defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));\n" +
					"        defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));\n" +
					"    }\n" +
					"    var x;\n" +
					"    var y;\n" +
					"    for (var i = 0; i < defaultSettings.watermark_rows; i++) {\n" +
					"        y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;\n" +
					"        for (var j = 0; j < defaultSettings.watermark_cols; j++) {\n" +
					"            x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;\n" +
					"            var mask_div = document.createElement('div');\n" +
					"            mask_div.id = 'mask_div' + i + j;\n" +
					"            mask_div.className = 'mask_div';\n" +
					"            mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));\n" +
					"            //设置水印div倾斜显示\n" +
					"            mask_div.style.webkitTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
					"            mask_div.style.MozTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
					"            mask_div.style.msTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
					"            mask_div.style.OTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
					"            mask_div.style.transform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
					"            mask_div.style.visibility = \"\";\n" +
					"            mask_div.style.position = \"absolute\";\n" +
					"            mask_div.style.left = x + 'px';\n" +
					"            mask_div.style.top = y + 'px';\n" +
					"            mask_div.style.overflow = \"hidden\";\n" +
					"            mask_div.style.zIndex = \"9999\";\n" +
					"            //让水印不遮挡页面的点击事件\n" +
					"            mask_div.style.pointerEvents = 'none';\n" +
					"            mask_div.style.opacity = defaultSettings.watermark_alpha;\n" +
					"            mask_div.style.fontSize = defaultSettings.watermark_fontsize;\n" +
					"            mask_div.style.fontFamily = defaultSettings.watermark_font;\n" +
					"            mask_div.style.color = defaultSettings.watermark_color;\n" +
					"            mask_div.style.textAlign = \"center\";\n" +
					"            mask_div.style.width = defaultSettings.watermark_width + 'px';\n" +
					"            mask_div.style.height = defaultSettings.watermark_height + 'px';\n" +
					"            mask_div.style.display = \"block\";\n" +
					"            oTemp.appendChild(mask_div);\n" +
					"        };\n" +
					"    };\n" +
					"    document.getElementById(\"water\").appendChild(oTemp);\n" +
					"}"+
					"  </script>");*/
			Outbuffer.append("</head>\r\n" +
					"<body style=\"width:900px; height:500px; padding-right:0px; padding-left:0px; padding-bottom:0px; padding-top:0px; margin-left:0px; margin-top:0px\">\r\n");

			if(xml.contains("Rh血型")){
				Outbuffer.append("<canvas id=\"canvas\" hidden=\"ture\"></canvas>"+
						"<table  style=\"border-collapse:collapse;table-layout:;\" Height=\""+Height+"\" width=\""+1000+"\" align=\"center\">");
			}else{
				Outbuffer.append("<canvas id=\"canvas\" hidden=\"ture\"></canvas>"+
						"<table  style=\"border-collapse:collapse;table-layout:;\" Height=\""+Height+"\" width=\""+Width+"\" align=\"center\">");
			}
			Outbuffer.append("<colgroup>");
			for(int i=0;i<ColumMax;i++)
			{
				int width=60;
				if (ColumnMap.get(String.valueOf(i))!=null)
				{
					width=Integer.parseInt(ColumnMap.get(String.valueOf(i)));
				}
				Outbuffer.append("<col width=\""+width+"\" />");
			}
			Outbuffer.append("</colgroup>");
			///输出结果
			for(int i=0;i<RowMax;i++)
			{
				String Tempheight=RowMap.get(String.valueOf(i));
				if (Tempheight==null)
				{
					Tempheight="20";
				}
				//System.out.print("<tr>");
				if (TMap.containsKey(i+" 0"))
				{
					String tempTag=TMap.get(i+" 0");
					if (tempTag.contains("HIDEROWTEXT"))
					{
						String TempValue=TVMap.get(tempTag);
						/*if(TempValue==null){
							System.out.println("TVMap："+TVMap);
							System.out.println("tempTag："+tempTag);
						}*/
						String CHARTEXT=StringUtils.substringBefore(StringUtils.substringAfter(tempTag, "CHARTEXT:"), "#");
						if (TempValue!=null&&TempValue.contains(CHARTEXT))
						{
							String TempContent=SheetArr[i][0];
							//TempContent=StringUtils.substringBefore(TempContent,">")+" hidden=\"ture\"> "+StringUtils.substringAfter(TempContent,">");
							TempContent=StringUtils.substringBefore(TempContent,">")+"></td>";
							SheetArr[i][0]=TempContent;
							Outbuffer.append("<tr height=\""+Tempheight+"\">");
						}
						else
						{
							Outbuffer.append("<tr height=\""+Tempheight+"\" hidden=\"ture\">");
						}
					}
					else
					{
						Outbuffer.append("<tr height=\""+Tempheight+"\">");
					}
				}
				else
				{
					Outbuffer.append("<tr height=\""+Tempheight+"\">");
				}
				//Outbuffer.append("<tr>");
				for(int j=0;j<ColumMax;j++)
				{
					//System.out.print(SheetArr[i][j]);
					Outbuffer.append(SheetArr[i][j]);
				}
				//System.out.println("</tr>");
				Outbuffer.append("</tr>");
			}
			Outbuffer.append("</table>\r\n" +
					"</body>\r\n" +
					"</html>");
			String resultstr=Outbuffer.toString();
			resultstr=resultstr.replaceAll("&KeyEnter;","<br>");
			resultstr=resultstr.replaceAll("font-size:10px;","font-size:15px;");
			resultstr=resultstr.replaceAll("font-size:10.5px;","font-size:15px;");
			resultstr=resultstr.replaceAll("font-size:11px;","font-size:15px;");
			resultstr=resultstr.replaceAll("font-size:12px;","font-size:15px;");
			FileWriter out = null;
			try {
				out = new FileWriter( htmlUrl );  //写入文件
				out.write(resultstr);
				html=resultstr;
				return html;
			} catch (IOException e) {
				e.printStackTrace();
			}finally {
				out.close();
			}

//			htmlTemplate InstanceTemplateInfo=tempMappingMapper.gethtmlTemplate(PrintTemplateVersionID);
/*			htmlTemplate InstanceTemplateInfo=tempMappingMapper.gethtmlTemplate(SystemId+"_"+standardID+"_"+PrintTemplateVersion);
			String ReturnJson="";
			String categoryId="";
			if(InstanceTemplateInfo!=null&&!StringUtils.isEmpty(InstanceTemplateInfo.getTemplateId())){
				String standardXmlId=tempMappingMapper.getstandardXmlId(InstanceTemplateInfo.getTemplateId());
				if(!StringUtils.isEmpty(standardXmlId)){
					TStandardTemplate TStandardTemplate=tempMappingMapper.getcategoryId(standardXmlId);
					categoryId=TStandardTemplate.getCategoryId();
				}
			}
			SimpleDateFormat formatter= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date date = new Date(System.currentTimeMillis());
			String format = formatter.format(date);
			htmlInstance2SourceDb htmlInstance2SourceDb=new htmlInstance2SourceDb();
			htmlInstance2SourceDb.setHosd_organid("00001");
			htmlInstance2SourceDb.setHosd_patientid(patientId);
			htmlInstance2SourceDb.setHosd_visitnumber(Integer.parseInt(diagNo));
			htmlInstance2SourceDb.setHosd_subsysdocumentid(SystemId+"_"+tempId);
			htmlInstance2SourceDb.setHosd_serialnumber(tempId);
			htmlInstance2SourceDb.setHosd_templateid(SystemId+"_"+standardID+"_"+PrintTemplateVersion);
			htmlInstance2SourceDb.setHosd_classificationcode(categoryId);
			htmlInstance2SourceDb.setHosd_updateusercode(CreateUserID);
			htmlInstance2SourceDb.setHosd_updatedate(format.split(" ")[0]);
			htmlInstance2SourceDb.setHosd_updatetime(format.split(" ")[1]);
			htmlInstance2SourceDb.setHosd_updatedatetime(format);
			if(!StringUtils.isEmpty(CreateDateTime)){
				htmlInstance2SourceDb.setUpdatedate(CreateDateTime.split(" ")[0]);
				htmlInstance2SourceDb.setUpdatetime(CreateDateTime.split(" ")[1]);
			}*/
//			htmlInstance2SourceDb.setHosd_updatedatetime(format);
/*			htmlInstance2SourceDb.setHosd_templateversionnumber(PrintTemplateVersion);
			htmlInstance2SourceDb.setHosd_manufacturerid(1);
			htmlInstance2SourceDb.setHosd_format("html");
			htmlInstance2SourceDb.setHosd_status("1");
			htmlInstance2SourceDb.setHosd_transformflag("N");
			htmlInstance2SourceDb.setDocumentcontent(Return);*/

	/*		ResponseInfo ResponseInfo=new ResponseInfo();
			ResponseInfo.setErrorCode("0");
			//				ResponseInfo.setErrorMsg(ReturnJson);
			ResponseInfo.setErrorMsg(JSONObject.toJSONString(htmlInstance2SourceDb));*/
			vMap =null;
			sMap =null;
			rlsMap =null;
			TMap =null;
			TVMap =null;
			RowMap=null;
			ColumnMap=null;
			/*TagContentList=null;*/
			StyleList=null;
			CellStyle=null;
			list=null;
			SheetModel=null;
			/*PSShapeDocument=null;*/
		} catch (Exception e) {
			e.printStackTrace();
//			System.out.print(tempId);
			Log log = LogFactory.getLog(batchXml2html.class);
		/*	log.error("EMR实例生成错误提示"+tempId,e);
			ResponseInfo ResponseInfo=new ResponseInfo();
			ResponseInfo.setErrorCode("-1");
			ResponseInfo.setErrorMsg(tempId+":"+e.getMessage());
			return ResponseInfo;*/
			//return Input+e.getMessage();
		}
		//System.out.println("Hello World!");
		return null;
	}

	public static char toHex(int ch) {
		if (ch < 10) {
			return (char) ('0' + ch);
		}
		else{
			return (char) ('a' + ch - 10);
		}
	}
}
