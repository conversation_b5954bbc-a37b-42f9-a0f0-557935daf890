package com.jhids.emr.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;


@Controller
@Slf4j
@RequestMapping("display")
public class EmrController extends BaseController {

    /*@RequestMapping("/dh")
    public void emrDemo(String fileSnCode,String watermark,String privacy, HttpServletResponse httpResponse) {
        display(fileSnCode,watermark,privacy, httpResponse);
    }*/

   /* @RequestMapping("/dh")
    public String emrDemo(@RequestBody Map<String,Object> params, HttpServletResponse httpResponse) {
        String fileSnCode=params.get("fileSnCode").toString();
        String watermark=params.get("watermark").toString();
        String privacy=params.get("privacy").toString();
        String result="";
        Map<String,Object> patientinfo=new HashMap<>();
        for(String key:params.keySet()){
            if(!"fileSnCode".equals(key)  && !"watermark".equals(key) && !"privacy".equals(key)){
                patientinfo.put(key,params.get(key));
            }
        }
        display(fileSnCode,watermark,privacy, patientinfo,httpResponse);
        return result;
    }*/
    @ResponseBody
    @RequestMapping("/dhnew")
    public String emrDemo(@RequestBody(required=false) Map<String,Object> params ) {
        log.info("进入了电子病历方法！！！@！！！！");
        for(String str:params.keySet()){
            log.info("打印入参！！！@！！！！"+str+"/"+params.get(str));
        }
        String fileSnCode=params.get("fileSnCode").toString();
        String watermark=params.get("watermark").toString();
        String privacy=params.get("privacy").toString();
        String oid=null;
        if(null!=params.get("OID")){
             oid=params.get("OID").toString();
        }

        String EMR_CLASS_CODE=null;
        String result="";
        Map<String,Object> patientinfo=new HashMap<>();
        for(String key:params.keySet()){
            if(!"fileSnCode".equals(key)  && !"watermark".equals(key) && !"privacy".equals(key)){
                if(!"-".equals(key)){
                    patientinfo.put(key,params.get(key));
                }
            }
        }
        if("SZSNSQYLJTZB".equals(oid)){
            EMR_CLASS_CODE=params.get("EMR_CLASS_CODE").toString();
            result= displaySK(fileSnCode,watermark,privacy, patientinfo,EMR_CLASS_CODE);
        }else{
            result= display(fileSnCode,watermark,privacy, patientinfo);
        }

        System.out.println(result);
        return result;
    }

}
