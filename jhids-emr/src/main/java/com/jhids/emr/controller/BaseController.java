package com.jhids.emr.controller;


import com.jhids.emr.service.EmrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Controller
public class BaseController {

    @Autowired
    private EmrService emrService;

    public String display(String fileSnCode, String watermark, String privacy, Map<String,Object> patientinfo) {
        log.info("请求获取的电子病历文件识别码:{}", fileSnCode);
        log.info("隐私化标识是:{}", privacy);
        StopWatch cost = StopWatch.createStarted();
        String html="";
        try {
            html=emrService.request(fileSnCode,watermark,privacy,patientinfo);
        } catch (Exception e) {
            log.error("电子病历展示异常:{}", fileSnCode, e);
        } finally {
            log.info("{}电子病历展示耗时:{}毫秒", fileSnCode, cost.getTime(TimeUnit.MILLISECONDS));
        }
        return html;
    }
    public String displaySK(String fileSnCode, String watermark, String privacy, Map<String,Object> patientinfo,String EMR_CLASS_CODE) {
        log.info("请求获取的电子病历文件识别码:{}", fileSnCode);
        log.info("隐私化标识是:{}", privacy);
        StopWatch cost = StopWatch.createStarted();
        String html="";
        try {
            html=emrService.requestSK(fileSnCode,watermark,privacy,patientinfo,EMR_CLASS_CODE);
        } catch (Exception e) {
            log.error("电子病历展示异常:{}", fileSnCode, e);
        } finally {
            log.info("{}电子病历展示耗时:{}毫秒", fileSnCode, cost.getTime(TimeUnit.MILLISECONDS));
        }
        return html;
    }
}
