package com.jhids.emr.utils;

import com.jhdl.common.api.DataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/*
 * RestTemplate工具类
 */

@Slf4j
@Component
public class RestTemplateUtil {

    @Autowired
    private RestTemplate restTemplate;

    public ResponseEntity<DataResponse> postJsonObject(String url, Object jsonObject) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<Object>(jsonObject, headers);
        ResponseEntity<DataResponse> result = restTemplate.postForEntity(url, entity, DataResponse.class);
        return result;
    }

    public ResponseEntity<DataResponse> postJsonObject(String url) {

        ResponseEntity<DataResponse> result = restTemplate.postForEntity(url, null, DataResponse.class);
        return result;
    }
}
