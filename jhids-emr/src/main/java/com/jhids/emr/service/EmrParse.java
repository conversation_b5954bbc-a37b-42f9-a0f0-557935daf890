package com.jhids.emr.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Assert;
import com.alibaba.nacos.client.utils.StringUtils;
import com.jhdl.common.api.DataResponse;
import com.jhids.emr.controller.batchXml2html;
import com.jhids.emr.controller.xmlHomeToHtml;
import com.jhids.emr.dto.JhFileContentDTO;
import com.jhids.emr.mapper.JhPatInfoMapper;
import com.sun.javafx.binding.StringFormatter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.ibatis.annotations.Param;
import org.dom4j.*;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.w3c.dom.NodeList;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import javax.sql.rowset.serial.SerialBlob;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import java.io.*;
import java.net.ConnectException;
import java.net.SocketException;
import java.nio.charset.StandardCharsets;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class EmrParse implements Emr {

    private final static Map<String, Object> models = new HashMap<>();
    //本地的路径和linux路径不一样，部署的时候需要改！
    //本机
    @Value("${file-path.templates}")
    private  String HTML_URL_FORMATTER;
//            = "D:\\development\\jhids-emr\\jhids-emr\\src\\main\\resources\\templates\\%s.html";
    @Value("${file-path.xml}")
    private String XML_URL_FORMATTER;
//    = "D:\\development\\jhids-emr\\jhids-emr\\src\\main\\resources\\xml\\%s.xml";
    //服务器  /usr/local/jhids-emr/jhids-emr/templates
    /*private final static String HTML_URL_FORMATTER = "/usr/local/jhids-emr/jhids-emr/templates/%s.html";
    //服务器  /usr/local/jhids-emr/jhids-emr/xml
    private final static String XML_URL_FORMATTER = "/usr/local/jhids-emr/jhids-emr/xml/%s.xml";*/
    @Autowired
    private JhFileContentInfoService jhFileContentInfoService;
    private static String fileName;
    private static Map<String, String> imgMap;
    @Autowired
    DataSource dataSource;
    /**
     * @return the fileName
     */
    public static String getFileName() {
        return fileName;
    }
    /**
     * @param fileName the fileName to set
     */

    public static void setFileName(String fileName) {
        EmrParse.fileName = fileName;
    }
    /**
     * @return the imgMap
     */
    public static Map<String, String> getImgMap() {
        return imgMap;
    }
    /**
     * @param imgMap the imgMap to set
     */
    public static void setImgMap(Map<String, String> imgMap) {
        EmrParse.imgMap = imgMap;
    }



    public JhFileContentDTO apply1(String fileSnCode) {
        JhFileContentDTO jh = new JhFileContentDTO();
        try {
            //根据sncode查询对应的病历内容
      /*  Map<String, Object> reponseMap = jhFileContentInfoService.getFileContent(fileSnCode);*/
       //如果是病案首页或者入院记录
        /*    Map<String, Object> mrTopic=jhFileContentInfoService.getMrTopic(fileSnCode);
            if(null!=mrTopic){
                    log.info("进入了MRTOPIC，获取到的地址是:{}", mrTopic.get("BUSSPHONE"));
                    jh.setPatinfoMap(mrTopic);

            }else{
                //做隐私化的一些对比参数，通过这些参数判断应该隐私哪个数据
                Map<String, Object> patinfoMap=jhFileContentInfoService.getPatInfo(fileSnCode);
   *//*         Map<String, Object> patinfoMap=new HashMap<>();
            patinfoMap.put("PATNAME","许沐瑶");
            patinfoMap.put("BUSSPHONE","南海城中心8栋4O4启");*//*
                if(null!=patinfoMap){
                    log.info("关联V_JH_IN_MR获取到的患者姓名是:{}", patinfoMap.get("PATNAME"));
                }
                Map<String, Object> patinfoMap2=jhFileContentInfoService.getPatInfo2(fileSnCode);
                if(null!=patinfoMap2){
                    log.info("关联V_JH_OUT_MR获取到的患者姓名是:{}", patinfoMap2.get("PATNAME"));
                }
              *//*  Map<String, Object> patinfoMap2=null;*//*

                if(null!= patinfoMap){
                    jh.setPatinfoMap(patinfoMap);
                }
            }*/


            /*
            *测试代码用于本地测试大文本是否能够正常展示
            Map<String, Object> patinfoMap=new HashMap<>();
            patinfoMap.put("PATNAME","许沐瑶");
            patinfoMap.put("BUSSPHONE","南海城中心8栋4O4启");
            jh.setPatinfoMap(patinfoMap);*/

            Connection connection= dataSource.getConnection();
            if(connection.toString().contains("oracle")){
                //如果数据库是tidb用byte接收
             /*   byte[] byte_data = (byte[])reponseMap.get("FILECONTENT");
                String result = new String(byte_data);*/
String result="<NInstanceData><Code>A0000</Code><Value /><ValueCode /><BindKBCode /><SubItems><NITextDesc><Code>E0039</Code><Value /><ValueCode /><SubItems><NISegment><Code>G0000</Code><Value /><ValueCode /><KBNodeID>96430</KBNodeID><SegmentNumber>1</SegmentNumber><SubItems><NISimple><Code>S0001</Code><Value>1、右输尿管上段结石&amp;nbsp;2、右肾积水&amp;nbsp;3、尿路感染</Value><ValueCode /><FormatRangeCollection /></NISimple></SubItems><FormatRangeCollection /></NISegment></SubItems><FormatRangeCollection /></NITextDesc><NITextDesc><Code>E0038</Code><Value /><ValueCode /><SubItems><NIFree><Code /><Value>1、右输尿管上段结石&amp;nbsp;2、右肾积水&amp;nbsp;3、尿路感染4、左肾结石&amp;nbsp;5、前列腺钙化</Value><ValueCode /><FormatRangeCollection /></NIFree></SubItems><FormatRangeCollection /><RevisionHistory><Revision><RevPerson>李世潘</RevPerson><RevTime>2020-7-14 8:43:10</RevTime><RevIP>fe80::214c:6aaf:7bc5:4679%11</RevIP><SubRevision><RevType>O</RevType><Content /></SubRevision><SubRevision><RevType>R</RevType><Content>1、右输尿管上段结石 2、右肾积水 3、尿路感染4、左肾结石 5、前列腺钙化</Content></SubRevision><SubRevision><RevType>R</RevType><Content /></SubRevision></Revision></RevisionHistory></NITextDesc><NISimple><Code>S0037</Code><Value>14-04</Value><ValueCode /><FormatRangeCollection /></NISimple><NISimple><Code>S0036</Code><Value>0003655334</Value><ValueCode /><FormatRangeCollection /></NISimple><NISimple><Code>S0001</Code><Value>159572</Value><ValueCode /><FormatRangeCollection /></NISimple><NISimple><Code>I0035</Code><Value>西丽泌尿外科</Value><ValueCode>671</ValueCode><FormatRangeCollection /></NISimple><NISimple><Code>S0002</Code><Value>高重阳</Value><ValueCode /><FormatRangeCollection /></NISimple><NISimple><Code>S0004</Code><Value>34岁</Value><ValueCode /><FormatRangeCollection /></NISimple><NISimple><Code>O0006</Code><Value>男</Value><ValueCode>1</ValueCode><FormatRangeCollection /></NISimple><NISimple><Code>D0007</Code><Value>2020年7月11日</Value><ValueCode>2020-7-11 00:00:00</ValueCode><FormatRangeCollection /></NISimple><NISimple><Code>D0008</Code><Value>2020年7月14日</Value><ValueCode>2020-7-14 00:00:00</ValueCode><FormatRangeCollection /></NISimple><NITextDesc><Code>E0009</Code><Value /><ValueCode /><SubItems><NISegment><Code>G0000</Code><Value /><ValueCode /><KBNodeID>69324</KBNodeID><SegmentNumber>1</SegmentNumber><SubItems><NIFree><Code /><Value>患者因“</Value><ValueCode /><FormatRangeCollection /></NIFree><NISimple><Code>S0001</Code><Value>反复右腰腹部疼痛3月，加重2小时。</Value><ValueCode /><FormatRangeCollection /></NISimple><NIFree><Code /><Value>”入院。查体：</Value><ValueCode /><FormatRangeCollection /></NIFree><NISimple><Code>S0002</Code><Value>双肾区无隆起，双肾未及，右肋脊角压痛，右肾区叩痛，右上输尿管点压痛。左肾区无叩痛，左输尿管行程无压痛。膀胱区无隆起，无压痛。阴茎、双侧睾丸、附睾发育正常，尿道外口无异常分泌物。</Value><ValueCode /><FormatRangeCollection /></NISimple><NIFree><Code /><Value>辅助检查：</Value><ValueCode /><FormatRangeCollection /></NIFree><NISimple><Code>S0003</Code><Value>2020-3-30日泌尿:双肾输尿管膀胱彩超[西丽],诊断所见:14：14分，急诊超声检查：双肾形态轮廓大小正常，双肾实质回声均匀，右肾集合系统分离扩张，暗区前后径16mm；左肾未见明显异常回声。CDFI:双肾内未见明显异常血流信号。右侧输尿管上段可见一个强回声团，大小约8×5mm，后方伴声影，近段输尿管扩张，内径8mm。对侧输尿管未显示扩张。膀胱不充盈。诊断意见:右输尿管上段结石并输尿管扩张。右肾轻度积水。左肾未见明显异常。</Value><ValueCode /><FormatRangeCollection /></NISimple></SubItems><FormatRangeCollection /></NISegment></SubItems><FormatRangeCollection /></NITextDesc><NITextDesc><Code>E0012</Code><Value /><ValueCode /><SubItems><NISegment><Code>G0000</Code><Value /><ValueCode /><KBNodeID>69339</KBNodeID><SegmentNumber>1</SegmentNumber><SubItems><NIFree><Code /><Value>入院后继续完善相关检查，尿常规白细胞+，尿路CT平扫+增强+重建[西丽],诊断意见:1、右侧输尿管上段结石，右肾轻度积水。2、左肾小结石。3、前列腺少量钙化。诊断明确。于</Value><ValueCode /><FormatRangeCollection /></NIFree><NISimple><Code>D0001</Code><Value>2020年7月13日</Value><ValueCode>2020-7-13 00:00:00</ValueCode><FormatRangeCollection /></NISimple><NIFree><Code /><Value>行</Value><ValueCode /><FormatRangeCollection /></NIFree><NISimple><Code>O0002</Code><Value>右</Value><ValueCode>1</ValueCode><FormatRangeCollection /></NISimple><NIFree><Code /><Value>输尿管上段结石体外冲击波碎石术，术后予</Value><ValueCode /><FormatRangeCollection /></NIFree><NISimple><Code>O0003</Code><Value>左氧氟沙星</Value><ValueCode>1</ValueCode><FormatRangeCollection /></NISimple><NIFree><Code /><Value>抗炎、排石、对症等治疗，间断有碎石自尿中排出。</Value><ValueCode /><FormatRangeCollection /></NIFree></SubItems><FormatRangeCollection /></NISegment></SubItems><FormatRangeCollection /></NITextDesc><NITextDesc><Code>E0014</Code><Value /><ValueCode /><SubItems><NISegment><Code>G0000</Code><Value /><ValueCode /><KBNodeID>69340</KBNodeID><SegmentNumber>1</SegmentNumber><SubItems><NISimple><Code>O0001</Code><Value>右</Value><ValueCode>1</ValueCode><FormatRangeCollection /></NISimple><NIFree><Code /><Value>腰腹部胀痛好转，轻度尿痛，无尿频、尿急，无肉眼血尿，生命体征平稳，双肾区无叩击痛。患者好转出院。</Value><ValueCode /><FormatRangeCollection /></NIFree></SubItems><FormatRangeCollection /></NISegment></SubItems><FormatRangeCollection /></NITextDesc><NITextDesc><Code>E0015</Code><Value /><ValueCode /><SubItems><NISegment><Code>G0000</Code><Value /><ValueCode /><KBNodeID>69341</KBNodeID><SegmentNumber>1</SegmentNumber><SubItems><NIFree><Code /><Value>1、带药：复方金钱草颗粒3g*40袋，冲服，每天3次，每次2袋，疗程一周。2、注意休息，全休壹周。多饮水，少食动物内脏、豆制品等，禁食菠菜。3、一周后门诊复查血尿常规、肾功能，一个月后门诊复查泌尿系B超,必要时行二次体外碎石术或微创腔镜手术。4、如有不适，门诊随诊。</Value><ValueCode /><FormatRangeCollection /></NIFree></SubItems><FormatRangeCollection /></NISegment></SubItems><FormatRangeCollection /></NITextDesc><NISimple><Code>I0016</Code><Value>罗保华</Value><ValueCode>510227</ValueCode><FormatRangeCollection /></NISimple><NISimple><Code>I0017</Code><Value>李世潘</Value><ValueCode>510351</ValueCode><FormatRangeCollection /></NISimple><NISimple><Code>S0027</Code><Value>3</Value><ValueCode /><FormatRangeCollection /></NISimple></SubItems></NInstanceData>";
                jh.setFILE_CONTENT(result);
            }else{
              /*  //如果数据库是oracle 用这个强转接收
                Blob b = (Blob)reponseMap.get("FILECONTENT");
                String blobString1 = getBlobToString(b,"UTF-8");
                jh.setFILE_CONTENT(blobString1);*/
            }
            //关闭
            connection.close();


          /*  Iterator it = jh.getPatinfoMap().keySet().iterator();
            while(it.hasNext()){
                String key = (String) it.next();
                Object object = jh.getPatinfoMap().get(key);
                log.info("PatinfoMap中的数据有:{}", object);
            }
            log.info("在jh_pat_info中获取到的患者姓名是:{}", jh.getPatinfoMap().get("PATNAME"));
            if(jh.getFILE_CONTENT().contains((String)(jh.getPatinfoMap().get("PATNAME")))){
                log.info("在blob的string中包含了需要隐私化的姓名:{}",jh.getPatinfoMap().get("PATNAME"));
            }else{
                log.info("在blob的string中没有包含需要隐私化的姓名:{}",jh.getPatinfoMap().get("PATNAME"));
            }*/
        return jh;
        } catch (Exception throwables) {
            throwables.printStackTrace();
        }
        return jh;
    }

    @Override
    public JhFileContentDTO apply(String fileSnCode) {
        JhFileContentDTO jh = new JhFileContentDTO();
        try {
            //根据sncode查询对应的病历内容
            Map<String, Object> reponseMap = jhFileContentInfoService.getFileContent(fileSnCode);

            //做隐私化的一些对比参数，通过这些参数判断应该隐私哪个数据
            Map<String, Object> patinfoMap=jhFileContentInfoService.getPatInfo(fileSnCode);
            //获得数据库连接
            Connection connection= dataSource.getConnection();
            if(connection.toString().contains("mysql")){

                if(null==reponseMap){
                    connection.close();
                    return jh;
                }
                //如果数据库是tidb用byte接收
                byte[] byte_data = (byte[])reponseMap.get("FILECONTENT");
                String result = new String(byte_data);
                jh.setFILE_CONTENT(result);
            }else{
                if(null==reponseMap){
                    connection.close();
                    return jh;
                }
                //如果数据库是oracle 用这个强转接收
                Blob b = (Blob)reponseMap.get("FILECONTENT");
                String blobString1 = getBlobToString(b,"UTF-8");


                jh.setFILE_CONTENT(blobString1);
            }

            //关闭
            connection.close();

            Map<String, Object> patinfoMap2=jhFileContentInfoService.getPatInfo2(fileSnCode);
            Map<String, Object> patinfoMap1 =new HashMap<>();
            patinfoMap1.put("PATNAME1","许沐瑶");
            jh.setPatinfoMap(patinfoMap1);
            if(null!= patinfoMap){
                jh.setPatinfoMap(patinfoMap);
            }else if(null!= patinfoMap2){
                jh.setPatinfoMap(patinfoMap2);
            }
            if(jh.getFILE_CONTENT().contains("病 危 通 知 单")){
                jh.getPatinfoMap().put("bingweiflag","true");
            }else{
                jh.getPatinfoMap().put("bingweiflag","false");
            }
            return jh;
        } catch (Exception throwables) {
            throwables.printStackTrace();
        }
        return jh;
    }

    @Override
    public JhFileContentDTO applySK(String fileSnCode,String EMR_CLASS_CODE) throws Exception {
        JhFileContentDTO jh = new JhFileContentDTO();
        try {
            //根据sncode查询对应的病历内容
            List<Map<String, Object>> reponseMap = jhFileContentInfoService.getFileContentSK(fileSnCode);
            Map<String, Object> reponseMapModel = jhFileContentInfoService.getFileContentSKModel(EMR_CLASS_CODE);
            String string=(String) reponseMapModel.get("HTML_TEXT");

            for(Map<String, Object> map:reponseMap){
                String append="\\{\\{"+(String)map.get("EMR_DG_CODE")+"\\}\\}";
                System.out.println(append);
                if(null!=map.get("EMR_DG_VALUE") && !"".equals((String)map.get("EMR_DG_VALUE"))){
                    string=string.replaceAll(append,(String) map.get("EMR_DG_VALUE"));
                }else{
                    string=string.replaceAll(append,"");
                }

            }


            string=string.replaceAll("\\{\\{"+"[0-9]+"+"\\}\\}","");
            jh.setFILE_CONTENT(string);
        }catch (Exception throwables){
            throwables.printStackTrace();
        }
        return jh;
    }

    public static String getBlobToString(Blob blob,String charset) throws Exception{
        String value;
        InputStream is=blob.getBinaryStream();
        int length =(int)blob.length();
        if(length!=0){
            byte [] buffer=new byte[length];
            is.read(buffer);
            is.close();
            value=new String(buffer,charset);
        }else{
            value="";

        }
        return value;
    }
    private static void  getChildNodes(Element elem){
        System.out.println(elem.getName());
        Iterator<Node> it=    elem.nodeIterator();
        while (it.hasNext()){
            Node node = it.next();
            if (node instanceof Element){
                Element e1 = (Element)node;
                getChildNodes(e1);
            }

        }
    }
    @Override
    public Map<String,Object> parse(String source, String watermark, String fileSnCode,Map<String,Object> patinfoMap,Map<String,Object> patientinfo) {
        Map<String,Object> mapResult=new HashMap<>();
        String uniFileSnCode = generateHtmlName(fileSnCode);
        String xmlUrl = String.format(XML_URL_FORMATTER, uniFileSnCode);
        String  html="";
        //生成一个xml存放的路径，然后先把要转换的xml放到系统的某个路径下去，然后在去解析文件中的xml
        FileWriter fw = null;
        try {
            //创建字符输出流对象，负责向文件内写入
            fw = new FileWriter(xmlUrl);
            //将str里面的内容读取到fw所指定的文件中
            fw.write(source);
        } catch (IOException e) {
            e.printStackTrace();
        }finally{
            if(fw!=null){
                try {
                    fw.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }
        String htmlUrl = String.format(HTML_URL_FORMATTER, uniFileSnCode);
        //判断是用abi或者iedtor去解析
        try {
        source=source.replace("&lt;", "@!").replace("&gt;", "@#");
        Document document = DocumentHelper.parseText(source);
        Element root = document.getRootElement();
        //三版除病案首页应用abi
        Element abiword = root.element("abiword");
        //二版病例+三版病案首页
        Element iEditor = root.element ("iEditor");
        //判断是用abiword编译器去解析还是用iEditor编译器去解析
        if(null !=abiword){
            //abi编译器
              html=stringToHtml(xmlUrl,htmlUrl,watermark,patinfoMap,patientinfo);
        }else if(null !=iEditor){
            //ieditor编译器
             html=xmlHomeToHtml.creatHtml(xmlUrl,htmlUrl,watermark,patinfoMap,patientinfo);
        }else {
            //判断现在是老病历，用老病历的形式去解析
          /*  Map<String, Object> reponseMap = jhFileContentInfoService.getModel(fileSnCode);
            Blob b = (Blob)reponseMap.get("FILECONTENT");
            String model = getBlobToString(b,"UTF-8");*/
            String model="<?xml version=\"1.0\" encoding=\"utf-8\"?><Spread class=\"FarPoint.Win.Spread.FpSpread\"><!--Settings Section - This section contains the workbook properties, and the sheet objects, properties and selections.--><Settings><RootWorkbook><ActiveSheetIndex>0</ActiveSheetIndex><AllowDragDrop>False</AllowDragDrop><AllowDragFill>False</AllowDragFill><AllowCellOverflow>False</AllowCellOverflow><AllowColumnMove>False</AllowColumnMove><AllowEditOverflow>False</AllowEditOverflow><AllowUserFormulas>False</AllowUserFormulas><AllowRowMove>False</AllowRowMove><AllowSheetMove>False</AllowSheetMove><AllowUndo>True</AllowUndo><GroupBarText>Double click a column to group by that column.</GroupBarText><AllowUserZoom>False</AllowUserZoom><AutoClipboard>True</AutoClipboard><BackColor>White</BackColor><BackgroundImage type=\"Image\"></BackgroundImage><BackgroundImageLayout>Tile</BackgroundImageLayout><BorderCollapse>Collapse</BorderCollapse><RightToLeft>No</RightToLeft><TabStripInsertTab>False</TabStripInsertTab><ScrollTipPolicy>Off</ScrollTipPolicy><ScrollBarTrackPolicy>Off</ScrollBarTrackPolicy><ScrollBarShowMax>True</ScrollBarShowMax><ScrollBarMaxAlign>True</ScrollBarMaxAlign><BorderStyle>Fixed3D</BorderStyle><ButtonDrawMode>Always</ButtonDrawMode><CellNoteIndicatorVisible>True</CellNoteIndicatorVisible><ClipboardOptions>AllHeaders</ClipboardOptions><ClipboardPasteToFill>False</ClipboardPasteToFill><ColumnSplitBoxAlignment>Leading</ColumnSplitBoxAlignment><ColumnSplitBoxPolicy>Never</ColumnSplitBoxPolicy><EditModePermanent>False</EditModePermanent><EditModeReplace>False</EditModeReplace><HorizontalScrollBarHeight>-1</HorizontalScrollBarHeight><HorizontalScrollBarPolicy>Always</HorizontalScrollBarPolicy><LeftTab>0</LeftTab><MoveActiveOnFocus>False</MoveActiveOnFocus><RetainSelectionBlock>True</RetainSelectionBlock><RightToLeft>No</RightToLeft><RowSplitBoxAlignment>Leading</RowSplitBoxAlignment><RowSplitBoxPolicy>Never</RowSplitBoxPolicy><SelectionBlockOptions>Cells, Rows, Columns</SelectionBlockOptions><SheetCount>1</SheetCount><TabStrip><BackColor>Control</BackColor><Editable>False</Editable><ButtonPolicy>Always</ButtonPolicy><ActiveSheetTab /><DefaultSheetTab /><Items><SheetTab /></Items></TabStrip><TabStripPlacement>WithHorizontalScrollBar</TabStripPlacement><TabStripPolicy>AsNeeded</TabStripPolicy><TabStripRatio>0.5</TabStripRatio><TextTipAppearance class=\"FarPoint.Win.Spread.TipAppearance\"><BackColor>Info</BackColor><ForeColor>InfoText</ForeColor><Font><Name>宋体</Name><Size>9</Size><Bold>False</Bold><CharSet>134</CharSet><Italic>False</Italic><Strikeout>False</Strikeout><Underline>False</Underline><Unit>Point</Unit><Vertical>False</Vertical></Font></TextTipAppearance><TextTipDelay>500</TextTipDelay><TextTipPolicy>Off</TextTipPolicy><VerticalScrollBarWidth>-1</VerticalScrollBarWidth><VerticalScrollBarPolicy>Always</VerticalScrollBarPolicy><VisualStyles>Off</VisualStyles><FocusRenderer><IFocusRenderer class=\"FarPoint.Win.Spread.DefaultFocusIndicatorRenderer\"><Thickness>1</Thickness></IFocusRenderer></FocusRenderer><InterfaceRenderer /><SelectionRenderer><ISelectionRenderer class=\"FarPoint.Win.Spread.DefaultSelectionRenderer\" /></SelectionRenderer><CellNoteIndicatorRenderer /><SuspendAnimations>False</SuspendAnimations><HorizontalScrollBar class=\"FarPoint.Win.Spread.SpreadHScrollBar\"><Buttons class=\"FarPoint.Win.Spread.FpScrollBarButtonCollection\"><Button class=\"FarPoint.Win.Spread.FpScrollBarButtonBackwardLine\"><ChangeType>SmallChange</ChangeType><ChangeValue>-1</ChangeValue></Button><Button class=\"FarPoint.Win.Spread.FpScrollBarComboThumb\"><TrackBackward class=\"FarPoint.Win.Spread.FpScrollBarTrackBackward\"><ChangeType>LargeChange</ChangeType><ChangeValue>-1</ChangeValue></TrackBackward><Thumb class=\"FarPoint.Win.Spread.FpScrollBarThumb\"><Gripper class=\"FarPoint.Win.Spread.FpScrollBarGripper\" /></Thumb><TrackForward class=\"FarPoint.Win.Spread.FpScrollBarTrackForward\"><ChangeType>LargeChange</ChangeType><ChangeValue>1</ChangeValue></TrackForward></Button><Button class=\"FarPoint.Win.Spread.FpScrollBarButtonForwardLine\"><ChangeType>SmallChange</ChangeType><ChangeValue>1</ChangeValue></Button></Buttons><VisualStyles>Off</VisualStyles><Renderer><IScrollBarRenderer class=\"FarPoint.Win.Spread.DefaultScrollBarRenderer\" /></Renderer></HorizontalScrollBar><VerticalScrollBar class=\"FarPoint.Win.Spread.SpreadVScrollBar\"><Buttons class=\"FarPoint.Win.Spread.FpScrollBarButtonCollection\"><Button class=\"FarPoint.Win.Spread.FpScrollBarButtonBackwardLine\"><ChangeType>SmallChange</ChangeType><ChangeValue>-1</ChangeValue></Button><Button class=\"FarPoint.Win.Spread.FpScrollBarComboThumb\"><TrackBackward class=\"FarPoint.Win.Spread.FpScrollBarTrackBackward\"><ChangeType>LargeChange</ChangeType><ChangeValue>-1</ChangeValue></TrackBackward><Thumb class=\"FarPoint.Win.Spread.FpScrollBarThumb\"><Gripper class=\"FarPoint.Win.Spread.FpScrollBarGripper\" /></Thumb><TrackForward class=\"FarPoint.Win.Spread.FpScrollBarTrackForward\"><ChangeType>LargeChange</ChangeType><ChangeValue>1</ChangeValue></TrackForward></Button><Button class=\"FarPoint.Win.Spread.FpScrollBarButtonForwardLine\"><ChangeType>SmallChange</ChangeType><ChangeValue>1</ChangeValue></Button></Buttons><VisualStyles>Off</VisualStyles><Renderer><IScrollBarRenderer class=\"FarPoint.Win.Spread.DefaultScrollBarRenderer\" /></Renderer></VerticalScrollBar></RootWorkbook><Sheets><Sheet index=\"0\" class=\"FarPoint.Win.Spread.SheetView\"><SheetName>Sheet1</SheetName><PrintInfo><AbortMessage /><Printer /><JobName /><Header whitespace=\"\" /><Footer /><ZoomFactor>1</ZoomFactor><FirstPageNumber>1</FirstPageNumber><Orientation>Portrait</Orientation><PrintType>All</PrintType><PageOrder>Auto</PageOrder><BestFitCols>False</BestFitCols><BestFitRows>False</BestFitRows><PageStart>-1</PageStart><PageEnd>1</PageEnd><ColStart>-1</ColStart><ColEnd>-1</ColEnd><RowStart>-1</RowStart><RowEnd>-1</RowEnd><Preview>True</Preview><ShowBorder>False</ShowBorder><ShowGrid>False</ShowGrid><ShowColor>False</ShowColor><ShowShadows>False</ShowShadows><ShowColumnHeader>Hide</ShowColumnHeader><ShowRowHeader>Hide</ShowRowHeader><ShowPrintDialog>False</ShowPrintDialog><UseMax>False</UseMax><UseSmartPrint>False</UseSmartPrint><Margin><Top>59</Top><Left>39</Left><Right>39</Right><Bottom>59</Bottom><Header>0</Header><Footer>0</Footer></Margin><Opacity>255</Opacity><PrintNotes>None</PrintNotes><PaperSize><Height>1169</Height><Kind>A4</Kind><PaperName>A4</PaperName><Width>827</Width></PaperSize><PaperSize class=\"System.Drawing.Printing.PaperSize\" assembly=\"System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a\" encoded=\"true\">AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj0yLjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAACFTeXN0ZW0uRHJhd2luZy5QcmludGluZy5QYXBlclNpemUFAAAABGtpbmQEbmFtZQV3aWR0aAZoZWlnaHQbY3JlYXRlZEJ5RGVmYXVsdENvbnN0cnVjdG9yBAEAAAAhU3lzdGVtLkRyYXdpbmcuUHJpbnRpbmcuUGFwZXJLaW5kAgAAAAgIAQIAAAAF/f///yFTeXN0ZW0uRHJhd2luZy5QcmludGluZy5QYXBlcktpbmQBAAAAB3ZhbHVlX18ACAIAAAAJAAAABgQAAAACQTQ7AwAAkQQAAAAL</PaperSize><PrintShapes>True</PrintShapes><Centering>None</Centering><RepeatColStart>-1</RepeatColStart><RepeatColEnd>-1</RepeatColEnd><RepeatRowStart>1</RepeatRowStart><RepeatRowEnd>4</RepeatRowEnd><SmartPrintPagesTall>1</SmartPrintPagesTall><SmartPrintPagesWide>1</SmartPrintPagesWide><HeaderHeight>-1</HeaderHeight><FooterHeight>-1</FooterHeight><PdfFileName /><PrintToPdf>False</PrintToPdf><PdfWriteMode>0</PdfWriteMode><PdfWriteTo>0</PdfWriteTo></PrintInfo><Categories><Appearance /><Behavior /><Layout><ColumnCount>24</ColumnCount><RowCount>20</RowCount></Layout></Categories><SelectionModel class=\"FarPoint.Win.Spread.Model.DefaultSheetSelectionModel\" /></Sheet></Sheets></Settings><!--Styles Section - This section contains the workbook and sheet named style collections.--><Styles><NamedStyles class=\"FarPoint.Win.Spread.NamedStyleCollection\" /></Styles><!--Presentation Section - This section contains the column and row properties, cell spans, and the sheet, column, row, and cell styles.--><Presentation><SpreadSkin class=\"FarPoint.Win.Spread.SpreadSkin\"><Name>Classic</Name><FocusIndicatorRenderer class=\"FarPoint.Win.Spread.DefaultFocusIndicatorRenderer\"><Thickness>1</Thickness></FocusIndicatorRenderer><ScrollBarRenderer class=\"FarPoint.Win.Spread.DefaultScrollBarRenderer\" /><SelectionRenderer class=\"FarPoint.Win.Spread.DefaultSelectionRenderer\" /><DefaultStyle class=\"FarPoint.Win.Spread.NamedStyle\" Name=\"DataAreaDefault\"><BackColor>Window</BackColor><CellType class=\"FarPoint.Win.Spread.CellType.GeneralCellType\"><AcceptsArrowKeys>AllArrows</AcceptsArrowKeys><TextOrientation>TextHorizontal</TextOrientation><WordWrap>False</WordWrap><DropDownButton>False</DropDownButton><ButtonAlign>Right</ButtonAlign><ReadOnly>False</ReadOnly><NullDisplay /><AutoCompleteMode>None</AutoCompleteMode><AutoCompleteSource>None</AutoCompleteSource><Static>False</Static><TextRotationAngle>0</TextRotationAngle><AutoFillAutoCompleteCustomSource>True</AutoFillAutoCompleteCustomSource><StringTrim>None</StringTrim><IsDateFormat>False</IsDateFormat><NegativeRed>False</NegativeRed></CellType><ForeColor>WindowText</ForeColor></DefaultStyle><columnHeaderDefaultStyle class=\"FarPoint.Win.Spread.NamedStyle\" Name=\"HeaderDefault\"><BackColor>Control</BackColor><ForeColor>ControlText</ForeColor><HorizontalAlignment>Center</HorizontalAlignment><Renderer class=\"FarPoint.Win.Spread.CellType.ColumnHeaderRenderer\"><AccessibleDescription /><AccessibleName /><AccessibleRole>Default</AccessibleRole><BackColor>Control</BackColor><BackgroundImage type=\"Image\"></BackgroundImage><BackgroundImageLayout>Tile</BackgroundImageLayout><Font><Name>宋体</Name><Size>9</Size><Bold>False</Bold><CharSet>134</CharSet><Italic>False</Italic><Strikeout>False</Strikeout><Underline>False</Underline><Unit>Point</Unit><Vertical>False</Vertical></Font><ForeColor>ControlText</ForeColor><RightToLeft>No</RightToLeft><Text whitespace=\"\" /><AllowDrop type=\"System.Boolean\">False</AllowDrop><Enabled type=\"System.Boolean\">True</Enabled><ImeMode>NoControl</ImeMode><TabIndex type=\"System.Int32\">0</TabIndex><TabStop type=\"System.Boolean\">True</TabStop><Visible type=\"System.Boolean\">True</Visible><CausesValidation type=\"System.Boolean\">True</CausesValidation><Anchor>Top, Left</Anchor><Dock>None</Dock><Location><Item>0</Item><Item>0</Item></Location><Margin><Item>3</Item><Item>3</Item><Item>3</Item><Item>3</Item></Margin><MaximumSize><Item>0</Item><Item>0</Item></MaximumSize><MinimumSize><Item>0</Item><Item>0</Item></MinimumSize><Padding><Item>0</Item><Item>0</Item><Item>0</Item><Item>0</Item></Padding><Size><Item>0</Item><Item>0</Item></Size><BackColor2>ButtonFace</BackColor2><BackgroundStyle>Default</BackgroundStyle><DarkColor>ControlDark</DarkColor><DialogResult>None</DialogResult><GradientMode>Horizontal</GradientMode><HorizontalAlignment>Center</HorizontalAlignment><HotkeyPrefix>None</HotkeyPrefix><LightColor>ControlLightLight</LightColor><Picture type=\"Image\"></Picture><PictureDown type=\"Image\"></PictureDown><ShadowSize type=\"System.Int32\">0</ShadowSize><TextAlign>TextLeftPictRight</TextAlign><TextDown /><TextIndent type=\"System.Int32\">0</TextIndent><TextOrientation>TextHorizontal</TextOrientation><TextRotationAngle type=\"System.Double\">0</TextRotationAngle><TwoState type=\"System.Boolean\">False</TwoState><UseVisualStyleBackColor type=\"System.Boolean\">True</UseVisualStyleBackColor><Value type=\"System.Int32\">0</Value><VerticalAlignment>Center</VerticalAlignment><VisualStyles>Auto</VisualStyles><WordWrap type=\"System.Boolean\">True</WordWrap></Renderer><VerticalAlignment>Center</VerticalAlignment></columnHeaderDefaultStyle><rowHeaderDefaultStyle class=\"FarPoint.Win.Spread.NamedStyle\" Name=\"RowHeaderDefault\"><BackColor>Control</BackColor><ForeColor>ControlText</ForeColor><HorizontalAlignment>Center</HorizontalAlignment><Renderer class=\"FarPoint.Win.Spread.CellType.RowHeaderRenderer\"><AccessibleDescription /><AccessibleName /><AccessibleRole>Default</AccessibleRole><BackColor>Control</BackColor><BackgroundImage type=\"Image\"></BackgroundImage><BackgroundImageLayout>Tile</BackgroundImageLayout><Font><Name>宋体</Name><Size>9</Size><Bold>False</Bold><CharSet>134</CharSet><Italic>False</Italic><Strikeout>False</Strikeout><Underline>False</Underline><Unit>Point</Unit><Vertical>False</Vertical></Font><ForeColor>ControlText</ForeColor><RightToLeft>No</RightToLeft><Text whitespace=\"\" /><AllowDrop type=\"System.Boolean\">False</AllowDrop><Enabled type=\"System.Boolean\">True</Enabled><ImeMode>NoControl</ImeMode><TabIndex type=\"System.Int32\">0</TabIndex><TabStop type=\"System.Boolean\">True</TabStop><Visible type=\"System.Boolean\">True</Visible><CausesValidation type=\"System.Boolean\">True</CausesValidation><Anchor>Top, Left</Anchor><Dock>None</Dock><Location><Item>0</Item><Item>0</Item></Location><Margin><Item>3</Item><Item>3</Item><Item>3</Item><Item>3</Item></Margin><MaximumSize><Item>0</Item><Item>0</Item></MaximumSize><MinimumSize><Item>0</Item><Item>0</Item></MinimumSize><Padding><Item>0</Item><Item>0</Item><Item>0</Item><Item>0</Item></Padding><Size><Item>0</Item><Item>0</Item></Size><BackColor2>ButtonFace</BackColor2><BackgroundStyle>Default</BackgroundStyle><DarkColor>ControlDark</DarkColor><DialogResult>None</DialogResult><GradientMode>Horizontal</GradientMode><HorizontalAlignment>Center</HorizontalAlignment><HotkeyPrefix>None</HotkeyPrefix><LightColor>ControlLightLight</LightColor><Picture type=\"Image\"></Picture><PictureDown type=\"Image\"></PictureDown><ShadowSize type=\"System.Int32\">0</ShadowSize><TextAlign>TextLeftPictRight</TextAlign><TextDown /><TextIndent type=\"System.Int32\">0</TextIndent><TextOrientation>TextHorizontal</TextOrientation><TextRotationAngle type=\"System.Double\">0</TextRotationAngle><TwoState type=\"System.Boolean\">False</TwoState><UseVisualStyleBackColor type=\"System.Boolean\">True</UseVisualStyleBackColor><Value type=\"System.Int32\">0</Value><VerticalAlignment>Center</VerticalAlignment><VisualStyles>Auto</VisualStyles><WordWrap type=\"System.Boolean\">True</WordWrap></Renderer><VerticalAlignment>Center</VerticalAlignment></rowHeaderDefaultStyle><cornerDefaultStyle class=\"FarPoint.Win.Spread.NamedStyle\" Name=\"CornerDefault\"><BackColor>Control</BackColor><ForeColor>ControlText</ForeColor><HorizontalAlignment>Center</HorizontalAlignment><Renderer class=\"FarPoint.Win.Spread.CellType.CornerRenderer\" /><VerticalAlignment>Center</VerticalAlignment></cornerDefaultStyle></SpreadSkin><Font /><Sheets><Sheet index=\"0\"><ViewportInfo><RowPaneCount>1</RowPaneCount><ColumnPaneCount>1</ColumnPaneCount><ActiveRowPane>0</ActiveRowPane><ActiveColumnPane>0</ActiveColumnPane><RowPanes><RowPane row=\"0\"><TopRow>0</TopRow><PreferredHeight>-1</PreferredHeight></RowPane></RowPanes><ColumnPanes><ColumnPane column=\"0\"><LeftColumn>0</LeftColumn><PreferredWidth>-1</PreferredWidth></ColumnPane></ColumnPanes></ViewportInfo><AxisModels><Row class=\"FarPoint.Win.Spread.Model.DefaultSheetAxisModel\" orientation=\"Vertical\" count=\"20\"><Items><Item index=\"-1\" /><Item index=\"2\"><Size>26</Size></Item><Item index=\"3\"><Size>26</Size></Item><Item index=\"5\"><Size>23</Size></Item><Item index=\"6\"><Size>23</Size></Item><Item index=\"7\"><Size>23</Size></Item><Item index=\"8\"><Size>23</Size></Item><Item index=\"9\"><Size>23</Size></Item><Item index=\"10\"><Size>23</Size></Item><Item index=\"11\"><Size>23</Size></Item><Item index=\"12\"><Size>23</Size></Item><Item index=\"13\"><Size>23</Size></Item><Item index=\"14\"><Size>23</Size></Item><Item index=\"15\"><Size>23</Size></Item><Item index=\"16\"><Size>23</Size></Item><Item index=\"17\"><Size>23</Size></Item><Item index=\"18\"><Size>23</Size></Item><Item index=\"19\"><Size>23</Size></Item></Items></Row><Column class=\"FarPoint.Win.Spread.Model.DefaultSheetAxisModel\" orientation=\"Horizontal\" count=\"24\"><Items><Item index=\"-1\" /><Item index=\"0\"><Size>30</Size></Item><Item index=\"1\"><Size>30</Size></Item><Item index=\"2\"><Size>30</Size></Item><Item index=\"3\"><Size>30</Size></Item><Item index=\"4\"><Size>30</Size></Item><Item index=\"5\"><Size>30</Size></Item><Item index=\"6\"><Size>30</Size></Item><Item index=\"7\"><Size>30</Size></Item><Item index=\"8\"><Size>30</Size></Item><Item index=\"9\"><Size>30</Size></Item><Item index=\"10\"><Size>30</Size></Item><Item index=\"11\"><Size>30</Size></Item><Item index=\"12\"><Size>30</Size></Item><Item index=\"13\"><Size>30</Size></Item><Item index=\"14\"><Size>30</Size></Item><Item index=\"15\"><Size>30</Size></Item><Item index=\"16\"><Size>30</Size></Item><Item index=\"17\"><Size>30</Size></Item><Item index=\"18\"><Size>30</Size></Item><Item index=\"19\"><Size>30</Size></Item><Item index=\"20\"><Size>30</Size></Item><Item index=\"21\"><Size>30</Size></Item><Item index=\"22\"><Size>30</Size></Item><Item index=\"23\"><Size>30</Size></Item></Items></Column><RowHeaderColumn class=\"FarPoint.Win.Spread.Model.DefaultSheetAxisModel\" defaultSize=\"35\" orientation=\"Horizontal\" count=\"1\"><Items><Item index=\"-1\"><Size>35</Size></Item></Items></RowHeaderColumn><ColumnHeaderRow class=\"FarPoint.Win.Spread.Model.DefaultSheetAxisModel\" orientation=\"Vertical\" count=\"1\"><Items><Item index=\"-1\" /></Items></ColumnHeaderRow></AxisModels><SpanModels><DataArea class=\"FarPoint.Win.Spread.Model.DefaultSheetSpanModel\"><CellRange Row=\"2\" Column=\"17\" RowCount=\"1\" ColumnCount=\"7\" /><CellRange Row=\"0\" Column=\"0\" RowCount=\"2\" ColumnCount=\"24\" /><CellRange Row=\"6\" Column=\"0\" RowCount=\"1\" ColumnCount=\"9\" /><CellRange Row=\"7\" Column=\"0\" RowCount=\"1\" ColumnCount=\"24\" /><CellRange Row=\"8\" Column=\"0\" RowCount=\"1\" ColumnCount=\"5\" /><CellRange Row=\"10\" Column=\"0\" RowCount=\"1\" ColumnCount=\"5\" /><CellRange Row=\"11\" Column=\"0\" RowCount=\"1\" ColumnCount=\"24\" /><CellRange Row=\"12\" Column=\"0\" RowCount=\"1\" ColumnCount=\"5\" /><CellRange Row=\"13\" Column=\"0\" RowCount=\"1\" ColumnCount=\"24\" /><CellRange Row=\"14\" Column=\"0\" RowCount=\"1\" ColumnCount=\"8\" /><CellRange Row=\"16\" Column=\"0\" RowCount=\"1\" ColumnCount=\"5\" /><CellRange Row=\"17\" Column=\"0\" RowCount=\"1\" ColumnCount=\"24\" /><CellRange Row=\"18\" Column=\"15\" RowCount=\"1\" ColumnCount=\"9\" /><CellRange Row=\"19\" Column=\"15\" RowCount=\"1\" ColumnCount=\"9\" /><CellRange Row=\"2\" Column=\"0\" RowCount=\"1\" ColumnCount=\"6\" /><CellRange Row=\"3\" Column=\"0\" RowCount=\"1\" ColumnCount=\"5\" /><CellRange Row=\"3\" Column=\"5\" RowCount=\"1\" ColumnCount=\"4\" /><CellRange Row=\"3\" Column=\"9\" RowCount=\"1\" ColumnCount=\"3\" /><CellRange Row=\"3\" Column=\"12\" RowCount=\"1\" ColumnCount=\"7\" /><CellRange Row=\"3\" Column=\"19\" RowCount=\"1\" ColumnCount=\"5\" /><CellRange Row=\"15\" Column=\"0\" RowCount=\"1\" ColumnCount=\"24\" /><CellRange Row=\"9\" Column=\"0\" RowCount=\"1\" ColumnCount=\"24\" /><CellRange Row=\"5\" Column=\"0\" RowCount=\"1\" ColumnCount=\"9\" /><CellRange Row=\"5\" Column=\"9\" RowCount=\"1\" ColumnCount=\"10\" /><CellRange Row=\"5\" Column=\"19\" RowCount=\"1\" ColumnCount=\"5\" /><CellRange Row=\"2\" Column=\"7\" RowCount=\"1\" ColumnCount=\"10\" /><CellRange Row=\"4\" Column=\"0\" RowCount=\"1\" ColumnCount=\"24\" /></DataArea></SpanModels><StyleModels><RowHeader class=\"FarPoint.Win.Spread.Model.DefaultSheetStyleModel\" Rows=\"20\" Columns=\"1\"><AltRowCount>2</AltRowCount><DefaultStyle class=\"FarPoint.Win.Spread.StyleInfo\" Parent=\"RowHeaderDefault\" /><ConditionalFormatCollections /></RowHeader><ColumnHeader class=\"FarPoint.Win.Spread.Model.DefaultSheetStyleModel\" Rows=\"1\" Columns=\"24\"><AltRowCount>2</AltRowCount><DefaultStyle class=\"FarPoint.Win.Spread.StyleInfo\" Parent=\"HeaderDefault\" /><ConditionalFormatCollections /></ColumnHeader><DataArea class=\"FarPoint.Win.Spread.Model.DefaultSheetStyleModel\" Rows=\"20\" Columns=\"24\"><AltRowCount>2</AltRowCount><DefaultStyle class=\"FarPoint.Win.Spread.StyleInfo\" Parent=\"DataAreaDefault\"><CellType class=\"FarPoint.Win.Spread.CellType.TextCellType\"><AcceptsArrowKeys>AllArrows</AcceptsArrowKeys><TextOrientation>TextHorizontal</TextOrientation><WordWrap>False</WordWrap><DropDownButton>False</DropDownButton><ButtonAlign>Right</ButtonAlign><ReadOnly>False</ReadOnly><NullDisplay /><AutoCompleteMode>None</AutoCompleteMode><AutoCompleteSource>None</AutoCompleteSource><Static>False</Static><TextRotationAngle>0</TextRotationAngle><AutoFillAutoCompleteCustomSource>True</AutoFillAutoCompleteCustomSource><StringTrim>None</StringTrim><CharacterCasing>Normal</CharacterCasing><MaxLength>255</MaxLength><Multiline>False</Multiline><ScrollBars>None</ScrollBars><CharacterSet>Ascii</CharacterSet><HotkeyPrefix>None</HotkeyPrefix></CellType><Font><Name>宋体</Name><Size>10.5</Size><Bold>False</Bold><CharSet>134</CharSet><Italic>False</Italic><Strikeout>False</Strikeout><Underline>False</Underline><Unit>Point</Unit><Vertical>False</Vertical></Font><Locked>False</Locked><VerticalAlignment>Center</VerticalAlignment></DefaultStyle><CellStyles><CellStyle Row=\"0\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>1</EPRStyleID></CellStyle><CellStyle Row=\"2\" Column=\"7\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>2</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"1\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"2\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"3\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"4\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"5\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"6\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"7\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"8\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"9\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"10\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"11\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"12\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"13\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"14\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"15\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"16\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"17\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"18\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"19\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"20\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"21\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"3\" Column=\"22\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>3</EPRStyleID></CellStyle><CellStyle Row=\"4\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>4</EPRStyleID></CellStyle><CellStyle Row=\"6\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>5</EPRStyleID></CellStyle><CellStyle Row=\"7\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>6</EPRStyleID></CellStyle><CellStyle Row=\"8\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>5</EPRStyleID></CellStyle><CellStyle Row=\"9\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>6</EPRStyleID></CellStyle><CellStyle Row=\"10\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>5</EPRStyleID></CellStyle><CellStyle Row=\"11\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>6</EPRStyleID></CellStyle><CellStyle Row=\"12\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>5</EPRStyleID></CellStyle><CellStyle Row=\"13\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>6</EPRStyleID></CellStyle><CellStyle Row=\"14\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>5</EPRStyleID></CellStyle><CellStyle Row=\"15\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>6</EPRStyleID></CellStyle><CellStyle Row=\"16\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>5</EPRStyleID></CellStyle><CellStyle Row=\"17\" Column=\"0\" class=\"FarPoint.Win.Spread.StyleInfo\"><EPRStyleID>6</EPRStyleID></CellStyle></CellStyles><ConditionalFormatCollections /></DataArea><SheetCorner class=\"FarPoint.Win.Spread.Model.DefaultSheetStyleModel\" Rows=\"1\" Columns=\"1\"><AltRowCount>2</AltRowCount><DefaultStyle class=\"FarPoint.Win.Spread.StyleInfo\" Parent=\"CornerDefault\" /><ConditionalFormatCollections /></SheetCorner></StyleModels><Notes class=\"FarPoint.Win.Spread.DrawingSpace.SpreadNotesContainer\"><Top>0</Top><Left>0</Left><Height>827</Height><Width>1417</Width><Name>spreadNotesContainer</Name><Text /><ForeColor>ControlText</ForeColor><BackColor>Transparent</BackColor><AlignHorz>Center</AlignHorz><AlignVert>Center</AlignVert><AlphaBlendBackColor>255</AlphaBlendBackColor><Font /><PictureTransparencyColor>Empty</PictureTransparencyColor><PictureTransparencyTolerance>0</PictureTransparencyTolerance><ShapeOutlineColor>Black</ShapeOutlineColor><ShapeOutlineThickness>1</ShapeOutlineThickness><ShapeOutlineStyle>Solid</ShapeOutlineStyle><RotationAngle>0</RotationAngle><PictureRotationAngle>0</PictureRotationAngle><TextRotationAngle>0</TextRotationAngle><Anchor>0</Anchor><Locked>False</Locked><CanRotate>True</CanRotate><CanPrint>True</CanPrint><SizeProportional>False</SizeProportional><FlipHorizontal>False</FlipHorizontal><FlipVertical>False</FlipVertical><CanMove>None</CanMove><CanSize>None</CanSize><Gradient class=\"FarPoint.Win.Spread.DrawingSpace.PSGradient\"><GradientStyle>None</GradientStyle><GradientSections><GradientSection class=\"FarPoint.Win.Spread.DrawingSpace.GradientSection\"><Amount>0</Amount><Color>#ffffffff</Color></GradientSection><GradientSection class=\"FarPoint.Win.Spread.DrawingSpace.GradientSection\"><Amount>0</Amount><Color>#ffffffff</Color></GradientSection></GradientSections></Gradient><EndOfProps /><AllowNoteEdit>True</AllowNoteEdit></Notes></Sheet></Sheets></Presentation><!--Drawing Section - This section contains the drawing objects and their properties.--><Drawing><Sheets><Sheet index=\"0\"><DrawingContainer class=\"FarPoint.Win.Spread.DrawingSpace.SpreadShapesContainer\"><Top>0</Top><Left>0</Left><Height>827</Height><Width>1417</Width><Name>spreadShapesContainer</Name><Text /><ForeColor>ControlText</ForeColor><BackColor>Transparent</BackColor><AlignHorz>Center</AlignHorz><AlignVert>Center</AlignVert><AlphaBlendBackColor>255</AlphaBlendBackColor><Font /><PictureTransparencyColor>Empty</PictureTransparencyColor><PictureTransparencyTolerance>0</PictureTransparencyTolerance><ShapeOutlineColor>Black</ShapeOutlineColor><ShapeOutlineThickness>1</ShapeOutlineThickness><ShapeOutlineStyle>Solid</ShapeOutlineStyle><RotationAngle>0</RotationAngle><PictureRotationAngle>0</PictureRotationAngle><TextRotationAngle>0</TextRotationAngle><Anchor>0</Anchor><Locked>False</Locked><CanRotate>True</CanRotate><CanPrint>True</CanPrint><SizeProportional>False</SizeProportional><FlipHorizontal>False</FlipHorizontal><FlipVertical>False</FlipVertical><CanMove>None</CanMove><CanSize>None</CanSize><EndOfProps /></DrawingContainer></Sheet></Sheets></Drawing><!--Data Section - This section contains the sheet data.--><Data><EnableCrossSheetReference>True</EnableCrossSheetReference><Sheets><Sheet index=\"0\"><DataArea class=\"FarPoint.Win.Spread.Model.DefaultSheetDataModel\" rows=\"20\" columns=\"24\"><AutoCalculation>True</AutoCalculation><AutoGenerateColumns>True</AutoGenerateColumns><ReferenceStyle>A1</ReferenceStyle><Iteration>False</Iteration><MaximumIterations>1</MaximumIterations><MaximumChange>0.001</MaximumChange><UnBoundNewRowIndex>-1</UnBoundNewRowIndex><SheetName>Sheet1</SheetName><Cells><Cell row=\"0\" column=\"0\"><Data type=\"System.String\">{[HIS]患者出入院信息.病历标题医院描述[字符]}</Data><Tag type=\"System.String\">$DataSet#UName:[HIS]患者出入院信息.病历标题医院描述[字符]#TYPE:DataSet#CNAME:EPRservice.SystemData#QNAME:GetATDInfo#FNAME:HospitalDesc|#SITE:R0C0</Tag></Cell><Cell row=\"2\" column=\"0\"><Data type=\"System.String\">登记号：{出院记录.登记号：.值}</Data><Tag type=\"System.String\">$Single#UName:出院记录.登记号：.值#TYPE:Simple#TID:1424#TVER:#SCODE:S0036#VTYPE:V|#SITE:R2C0</Tag></Cell><Cell row=\"2\" column=\"7\"><Data type=\"System.String\">出院记录</Data></Cell><Cell row=\"2\" column=\"17\"><Data type=\"System.String\">住院号：{出院记录.住院号：.值}</Data><Tag type=\"System.String\">$Single#UName:出院记录.住院号：.值#TYPE:Simple#TID:1424#TVER:#SCODE:S0001#VTYPE:V|#SITE:R2C14</Tag></Cell><Cell row=\"3\" column=\"0\"><Data type=\"System.String\">姓名：{出院记录.姓名：.值}</Data><Tag type=\"System.String\">$Single#UName:出院记录.姓名：.值#TYPE:Simple#TID:1424#TVER:#SCODE:S0002#VTYPE:V|#SITE:R3C0</Tag></Cell><Cell row=\"3\" column=\"5\"><Data type=\"System.String\">性别：{出院记录.性别：.值}</Data><Tag type=\"System.String\">$Single#UName:出院记录.性别：.值#TYPE:Simple#TID:1424#TVER:#SCODE:O0006#VTYPE:V|#SITE:R3C5</Tag></Cell><Cell row=\"3\" column=\"9\"><Data type=\"System.String\">年龄：{出院记录.年龄：.值}</Data><Tag type=\"System.String\">$Single#UName:出院记录.年龄：.值#TYPE:Simple#TID:1424#TVER:#SCODE:S0004#VTYPE:V|#SITE:R3C8</Tag></Cell><Cell row=\"3\" column=\"12\"><Data type=\"System.String\">科室：{出院记录.科室：.说明}</Data><Tag type=\"System.String\">$Single#UName:出院记录.科室：.说明#TYPE:Simple#TID:1424#TVER:#SCODE:I0035#VTYPE:V|#SITE:R3C11</Tag></Cell><Cell row=\"3\" column=\"19\"><Data type=\"System.String\">床号：{出院记录.床号：.值}</Data><Tag type=\"System.String\">$Single#UName:出院记录.床号：.值#TYPE:Simple#TID:1424#TVER:#SCODE:S0037#VTYPE:V|#SITE:R3C16</Tag></Cell><Cell row=\"4\" column=\"0\"><Data type=\"System.String\">第{打印页码}页</Data><Tag type=\"System.String\">$SystemData#UName:打印页码#TYPE:PageNum#SCODE:PAGENUM|#SITE:R4C0</Tag></Cell><Cell row=\"5\" column=\"0\"><Data type=\"System.String\">入院时间：{出院记录.入院时间:.标准日期格式}</Data><Tag type=\"System.String\">$Single#UName:出院记录.入院时间:.标准日期格式#TYPE:Simple#TID:1424#TVER:#SCODE:D0007#VTYPE:V|#SITE:R5C0</Tag></Cell><Cell row=\"5\" column=\"9\"><Data type=\"System.String\">出院时间：{出院记录.出院时间:.标准日期格式}</Data><Tag type=\"System.String\">$Single#UName:出院记录.出院时间:.标准日期格式#TYPE:Simple#TID:1424#TVER:#SCODE:D0008#VTYPE:V|#SITE:R5C8</Tag></Cell><Cell row=\"5\" column=\"19\"><Data type=\"System.String\">住院天数：{出院记录.住院天数:.值}天</Data><Tag type=\"System.String\">$Single#UName:出院记录.住院天数:.值#TYPE:Simple#TID:1424#TVER:#SCODE:S0027#VTYPE:V|#SITE:R5C16</Tag></Cell><Cell row=\"6\" column=\"0\"><Data type=\"System.String\">入院情况：</Data></Cell><Cell row=\"7\" column=\"0\"><Data type=\"System.String\">{出院记录.入院情况：}</Data><Note>复杂文本格式单元\n" +
                    "自动增行填充单元\n" +
                    "</Note><Tag type=\"System.String\">$Single#UName:出院记录.入院情况：#TYPE:TextDesc#TID:1424#TVER:#ECODE:E0009|#FORMAT:DF#SITE:R7C0</Tag></Cell><Cell row=\"8\" column=\"0\"><Data type=\"System.String\">入院诊断：</Data></Cell><Cell row=\"9\" column=\"0\"><Data type=\"System.String\">{出院记录.入院诊断：}</Data><Note>复杂文本格式单元\n" +
                    "自动增行填充单元\n" +
                    "</Note><Tag type=\"System.String\">$Single#UName:出院记录.入院诊断：#TYPE:TextDesc#TID:1424#TVER:#ECODE:E0039|#FORMAT:DF#SITE:R9C0</Tag></Cell><Cell row=\"10\" column=\"0\"><Data type=\"System.String\">诊疗经过：</Data></Cell><Cell row=\"11\" column=\"0\"><Data type=\"System.String\">{出院记录.诊疗经过：}</Data><Note>复杂文本格式单元\n" +
                    "自动增行填充单元\n" +
                    "</Note><Tag type=\"System.String\">$Single#UName:出院记录.诊疗经过：#TYPE:TextDesc#TID:1424#TVER:#ECODE:E0012|#FORMAT:DF#SITE:R17C0</Tag></Cell><Cell row=\"12\" column=\"0\"><Data type=\"System.String\">出院情况：</Data></Cell><Cell row=\"13\" column=\"0\"><Data type=\"System.String\">{出院记录.出院情况：}</Data><Note>复杂文本格式单元\n" +
                    "自动增行填充单元\n" +
                    "</Note><Tag type=\"System.String\">$Single#UName:出院记录.出院情况：#TYPE:TextDesc#TID:1424#TVER:#ECODE:E0014|#FORMAT:DF#SITE:R28C0</Tag></Cell><Cell row=\"14\" column=\"0\"><Data type=\"System.String\">出院诊断：</Data></Cell><Cell row=\"15\" column=\"0\"><Data type=\"System.String\">{出院记录.出院诊断：}</Data><Note>复杂文本格式单元\n" +
                    "自动增行填充单元\n" +
                    "</Note><Tag type=\"System.String\">$Single#UName:出院记录.出院诊断：#TYPE:TextDesc#TID:1424#TVER:#ECODE:E0038|#FORMAT:DF#SITE:R15C0</Tag></Cell><Cell row=\"16\" column=\"0\"><Data type=\"System.String\">出院医嘱：</Data></Cell><Cell row=\"17\" column=\"0\"><Data type=\"System.String\">{出院记录.出院医嘱：}</Data><Note>复杂文本格式单元\n" +
                    "自动增行填充单元\n" +
                    "</Note><Tag type=\"System.String\">$Single#UName:出院记录.出院医嘱：#TYPE:TextDesc#TID:1424#TVER:#ECODE:E0015|#FORMAT:DF#SITE:R38C0</Tag></Cell><Cell row=\"18\" column=\"15\"><Data type=\"System.String\">医师签名：{出院记录.医师签名：.说明}</Data><Tag type=\"System.String\">$Single#UName:出院记录.医师签名：.说明#TYPE:Simple#TID:1424#TVER:#SCODE:I0017#VTYPE:V|#SITE:R43C12</Tag></Cell><Cell row=\"19\" column=\"15\"><Data type=\"System.String\">上级医师签名：{出院记录.上级医师签名：.说明}</Data><Tag type=\"System.String\">$Single#UName:出院记录.上级医师签名：.说明#TYPE:Simple#TID:1424#TVER:#SCODE:I0016#VTYPE:V|#SITE:R44C12</Tag></Cell></Cells></DataArea><RowHeader class=\"FarPoint.Win.Spread.Model.DefaultSheetDataModel\" rows=\"20\" columns=\"1\"><AutoCalculation>True</AutoCalculation><AutoGenerateColumns>True</AutoGenerateColumns><ReferenceStyle>A1</ReferenceStyle><Iteration>False</Iteration><MaximumIterations>1</MaximumIterations><MaximumChange>0.001</MaximumChange><UnBoundNewRowIndex>-1</UnBoundNewRowIndex></RowHeader><ColumnHeader class=\"FarPoint.Win.Spread.Model.DefaultSheetDataModel\" rows=\"1\" columns=\"24\"><AutoCalculation>True</AutoCalculation><AutoGenerateColumns>True</AutoGenerateColumns><ReferenceStyle>A1</ReferenceStyle><Iteration>False</Iteration><MaximumIterations>1</MaximumIterations><MaximumChange>0.001</MaximumChange><UnBoundNewRowIndex>-1</UnBoundNewRowIndex></ColumnHeader><RangeGroupModel class=\"FarPoint.Win.Spread.DefaultRangeGroupModel\"><ColumnGroup /><collapsedColumns class=\"System.Collections.SortedList\" assembly=\"mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089\" encoded=\"true\">AAEAAAD/////AQAAAAAAAAAEAQAAAB1TeXN0ZW0uQ29sbGVjdGlvbnMuU29ydGVkTGlzdAcAAAAEa2V5cwZ2YWx1ZXMFX3NpemUHdmVyc2lvbghjb21wYXJlcgdrZXlMaXN0CXZhbHVlTGlzdAUFAAADAwMICBtTeXN0ZW0uQ29sbGVjdGlvbnMuQ29tcGFyZXIlU3lzdGVtLkNvbGxlY3Rpb25zLlNvcnRlZExpc3QrS2V5TGlzdCdTeXN0ZW0uQ29sbGVjdGlvbnMuU29ydGVkTGlzdCtWYWx1ZUxpc3QJAgAAAAkCAAAAAAAAAAAAAAAJAwAAAAoKEAIAAAAAAAAABAMAAAAbU3lzdGVtLkNvbGxlY3Rpb25zLkNvbXBhcmVyAQAAAAtDb21wYXJlSW5mbwMgU3lzdGVtLkdsb2JhbGl6YXRpb24uQ29tcGFyZUluZm8JBAAAAAQEAAAAIFN5c3RlbS5HbG9iYWxpemF0aW9uLkNvbXBhcmVJbmZvAwAAAAl3aW4zMkxDSUQHY3VsdHVyZQZtX25hbWUAAAEICAQIAAAECAAABgUAAAAFemgtQ04L</collapsedColumns><collapsedRows class=\"System.Collections.SortedList\" assembly=\"mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089\" encoded=\"true\">AAEAAAD/////AQAAAAAAAAAEAQAAAB1TeXN0ZW0uQ29sbGVjdGlvbnMuU29ydGVkTGlzdAcAAAAEa2V5cwZ2YWx1ZXMFX3NpemUHdmVyc2lvbghjb21wYXJlcgdrZXlMaXN0CXZhbHVlTGlzdAUFAAADAwMICBtTeXN0ZW0uQ29sbGVjdGlvbnMuQ29tcGFyZXIlU3lzdGVtLkNvbGxlY3Rpb25zLlNvcnRlZExpc3QrS2V5TGlzdCdTeXN0ZW0uQ29sbGVjdGlvbnMuU29ydGVkTGlzdCtWYWx1ZUxpc3QJAgAAAAkCAAAAAAAAAAAAAAAJAwAAAAoKEAIAAAAAAAAABAMAAAAbU3lzdGVtLkNvbGxlY3Rpb25zLkNvbXBhcmVyAQAAAAtDb21wYXJlSW5mbwMgU3lzdGVtLkdsb2JhbGl6YXRpb24uQ29tcGFyZUluZm8JBAAAAAQEAAAAIFN5c3RlbS5HbG9iYWxpemF0aW9uLkNvbXBhcmVJbmZvAwAAAAl3aW4zMkxDSUQHY3VsdHVyZQZtX25hbWUAAAEICAQIAAAECAAABgUAAAAFemgtQ04L</collapsedRows><RowGroup /></RangeGroupModel><SheetCornerDataModel class=\"FarPoint.Win.Spread.Model.DefaultSheetDataModel\" rows=\"1\" columns=\"1\"><AutoCalculation>True</AutoCalculation><AutoGenerateColumns>True</AutoGenerateColumns><ReferenceStyle>A1</ReferenceStyle><Iteration>False</Iteration><MaximumIterations>1</MaximumIterations><MaximumChange>0.001</MaximumChange><UnBoundNewRowIndex>-1</UnBoundNewRowIndex></SheetCornerDataModel></Sheet></Sheets></Data><EPRStyleCollection><EPRStyle ID=\"1\"><Font><Name>华文楷体</Name><Size>20</Size><Bold>True</Bold><CharSet>1</CharSet><Italic>False</Italic><Strikeout>False</Strikeout><Underline>False</Underline><Unit>Point</Unit><Vertical>False</Vertical></Font><HorizontalAlignment>Center</HorizontalAlignment></EPRStyle><EPRStyle ID=\"2\"><Font><Name>宋体</Name><Size>16</Size><Bold>True</Bold><CharSet>1</CharSet><Italic>False</Italic><Strikeout>False</Strikeout><Underline>False</Underline><Unit>Point</Unit><Vertical>False</Vertical></Font><HorizontalAlignment>Center</HorizontalAlignment></EPRStyle><EPRStyle ID=\"3\"><Border class=\"FarPoint.Win.ComplexBorder\"><Sides><Bottom><Color>Black</Color><Style>MediumLine</Style></Bottom><Left><Color>WindowFrame</Color><Style>None</Style></Left><Right><Color>WindowFrame</Color><Style>None</Style></Right><Top><Color>WindowFrame</Color><Style>None</Style></Top></Sides></Border></EPRStyle><EPRStyle ID=\"4\"><HorizontalAlignment>Center</HorizontalAlignment></EPRStyle><EPRStyle ID=\"5\"><Font><Name>宋体</Name><Size>10.5</Size><Bold>True</Bold><CharSet>1</CharSet><Italic>False</Italic><Strikeout>False</Strikeout><Underline>False</Underline><Unit>Point</Unit><Vertical>False</Vertical></Font></EPRStyle><EPRStyle ID=\"6\"><CellType class=\"dhcc.epr.systemframework.MyRichTextCellType\" assembly=\"SystemFramework\"><MaxLength>32767</MaxLength><Multiline>False</Multiline><WordWrap>False</WordWrap><ReadOnly>False</ReadOnly><Static>False</Static><ScrollBars>None</ScrollBars></CellType></EPRStyle></EPRStyleCollection></Spread>";
            html= batchXml2html.xml2Html(model,source,watermark,(String)patinfoMap.get("privacyflag"),htmlUrl);
        }
        } catch (Exception e) {
            log.info("数据转化成html文件失败,文件识别号:{}", fileSnCode, e);
            mapResult.put("returnDesc","解析电子病历parse阶段报错："+e+"文件识别号为："+fileSnCode);
        } finally {
            mapResult.put("htmlUrl",htmlUrl);
            mapResult.put("xmlUrl",xmlUrl);
            mapResult.put("html",html);
            return mapResult;
        }
    }
    public static String stringToHtml(String source,String htmlUrl,String watermark,Map<String,Object> patinfoMap,Map<String,Object> patientinfo) {
        BufferedReader br = null;
        PrintWriter out = null;
        String xmlStr=getXml(source,htmlUrl,watermark);
        Object newString=stringTransform(xmlStr);
        String htmlString=newString.toString();
        if(null==patinfoMap.get("privacyflag") || !"N".equals(patinfoMap.get("privacyflag"))){
            log.info("进入了脱敏隐私化的判断！！");
            //隐私化处理
            if(null!=patientinfo){
                for(String key :patientinfo.keySet()){
                    htmlString=htmlString.replaceAll(key,(String)patientinfo.get(key));
                }
            }
           if(null!=patinfoMap){
                Set<String> keys = patinfoMap.keySet();
                for(String key :keys){
                    if(!"privacyflag".equals(key)){
                        String value1="";
                        if(!"PATNAME".equals(key) && !"OTHERNAME".equals(key)){
                            if ("BUSSPHONE".equals(key) ) {
                                int length = ((String) patinfoMap.get("BUSSPHONE")).length();
                                if (length>=4) {
                                    value1=((String) patinfoMap.get(key)).substring(((String) patinfoMap.get(key)).length()-4,((String) patinfoMap.get(key)).length()-2);
                                }else{
                                    value1=(String) patinfoMap.get(key);
                                }
                            }else {
                                value1=((String) patinfoMap.get(key)).substring(((String) patinfoMap.get(key)).length()-4,((String) patinfoMap.get(key)).length()-2);
                            }
                            log.info("截取后的字符串是！！"+value1);
                        }else{
                            value1=(String) patinfoMap.get(key);
                            log.info("没有截取的字符串是！！"+value1);
                        }
                      if(htmlString.contains(value1)) {
                          if (null != patinfoMap.get(key)) {
                              if ("PATNAME".equals(key)) {
                                  patinfoMap.put("PATNAME", (((String) patinfoMap.get(key)).trim()));
                                  if (!StringUtils.isBlank((String) patinfoMap.get(key))) {
                                      String value = (new StringBuilder((String) patinfoMap.get(key)).replace(1, ((String) patinfoMap.get(key)).length(), "**")).toString();
                                      htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
                                  }
                              }
                              if ("OTHERNAME".equals(key)) {
                                  patinfoMap.put("OTHERNAME", (((String) patinfoMap.get(key)).trim()));
                                  if (!StringUtils.isBlank((String) patinfoMap.get(key)) && !"-".equals((String) patinfoMap.get(key))) {
                                      String value = (new StringBuilder((String) patinfoMap.get(key)).replace(1, ((String) patinfoMap.get(key)).length(), "**")).toString();
                                      htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
                                  }

                              }
                              if ("IDNO".equals(key)) {
                                  patinfoMap.put("IDNO", (((String) patinfoMap.get(key)).trim()));
                                  if (!StringUtils.isBlank((String) patinfoMap.get(key))) {
                                      String value = (new StringBuilder((String) patinfoMap.get(key)).replace(10, ((String) patinfoMap.get(key)).length(), "********")).toString();
                                      htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
                                  }
                              }
                              if ("HOMEPHONE".equals(key)) {
                                  if (((String) patinfoMap.get(key)).length() > 3) {
                                      String value = (new StringBuilder((String) patinfoMap.get(key)).replace(3, 7, "****")).toString();
                                      htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
                                  }
                              }
                              if ("OTHERPHONE".equals(key)) {
                                  if (((String) patinfoMap.get(key)).length() > 3) {
                                      String value = (new StringBuilder((String) patinfoMap.get(key)).replace(3, 7, "****")).toString();
                                      htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
                                  }
                              }
                              if ("BUSSPHONE".equals(key)) {
                                  if (((String) patinfoMap.get(key)).length() > 3) {
                                      log.info("进入了BUSSPHONE！！");
                                      //String value=((String) patinfoMap.get(key)).substring(((String) patinfoMap.get(key)).length()-4,((String) patinfoMap.get(key)).length()-2);
                                      String value = (new StringBuilder((String) patinfoMap.get(key)).replace(((String) patinfoMap.get(key)).length() - 4, ((String) patinfoMap.get(key)).length() - 2, "****")).toString();
                                      htmlString = htmlString.replaceAll((String) patinfoMap.get(key), value);
                                  }
                              }
                          }
                      }
                    }
                }
            }
        }
        try {
            out = new PrintWriter(new BufferedWriter(new FileWriter(htmlUrl)));
            String str = null;
            out.print(htmlString);
            return htmlString;
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            out.close();
            try {
                if(br!=null) br.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if(out!=null) out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return htmlString;
    }
    public static String stringTransform(String string){

        String newString1=string.replaceAll("&lt;","<").replaceAll("&gt;",">").replaceAll("style=", "class=")
                .replaceAll("props=", "style=").replaceAll("<cell", "<td").replaceAll("</cell>", "</td>")
                .replaceAll("bgcolor:", "background:#").replaceAll("<c", "<span").replaceAll("</c>", "</span>")
                .replaceAll("left-color:", "border-left-color:#").replaceAll("right-color:", "border-right-color:#").replaceAll("bot-color:", "border-bottom-color:#")
                .replaceAll("top-color:", "border-top-color:#").replaceAll("left-thickness", "border-left").replaceAll("right-thickness", "border-right")
                .replaceAll("top-thickness", "border-top").replaceAll("bot-thickness", "border-bottom").replaceAll("left-style:1", "border-left-style:solid")
                .replaceAll("right-style:1", "border-right-style:solid").replaceAll("bot-style:1", "border-bottom-style:solid").replaceAll("top-style:1", "border-top-style:solid")
                .replaceAll(">\\[.*?]",">");
        String newString2=null;
        if(EmrParse.getImgMap()!=null&&!EmrParse.getImgMap().isEmpty()){
            for (Map.Entry<String, String> entry : EmrParse.getImgMap().entrySet()) {
                if(newString1.indexOf("image dataid=\""+entry.getKey())>0){
                    String sda="image src='data:image/jpg;base64,"+entry.getValue();
                    newString1=newString1.replaceAll("image dataid=\""+entry.getKey(), "image src=\"data:image/jpg;base64,"+entry.getValue())
                            .replaceAll("<math", "<a").replaceAll("</math>", "</a>");
                    /*newString1=newString2;*/
                }
            }
           /* newString=newString1;*/
        }

        return newString1;
    }
    public static String getXml(String filePath,String fileName,String watermark){
        String classCssString="";
        String headString=null;
        String bodyString=null;
        String footString=null;
        SAXReader reader = new SAXReader();
        try {
            Document document = reader.read(new File(filePath));
            Element root = document.getRootElement();
            //获取文件名
            EmrParse.setFileName(fileName);
            //遍历List的方法
			/*List<Element> list = root.elements() ;
			 for (Element e:list){
			// System.out.println(e.getName());
			 }*/
            //abiword下的元素
/*
			List<Element> styles = root.element("NInstanceData").element("abiword").element("styles").elements();
*/
            /*String aa = root.attribute("InstanceID").getText();*/
            Element section = root.element("abiword");
            if(root.element("abiword").element("data")!=null&&
                    !root.element("abiword").element("data").elements().isEmpty()){
                List<Element> datas = root.element("abiword").element("data").elements();
                EmrParse.setImgMap(getImage(datas));
            }
            List<Element> abiword2 = root.element("abiword").elements();
            for (Element e:abiword2){
                if("section".equals(e.getName())){
                    List<Element> tables=e.elements("table");
                    for (Element tableelement:tables){
                        List<Element> cells=tableelement.elements("cell");
                        for (Element cellelement:cells){
                            List<Element> ps=cellelement.elements("p");
                            for (Element pelement:ps){
                                if(pelement.hasContent()){
                                    List<Element> celementlist=pelement.elements("c");
                                    for (Element celement:celementlist){
                                        List<Attribute> listAttr=celement.attributes();
                                        for(Attribute attr:listAttr){
                                            String name=attr.getName();//属性名称
                                            String value=attr.getValue();
                                            if("revision".equals(name) ){
                                                if(value.contains("-") ){
                                                    if(value.length()>10){
                                                        if(value.contains(",-") ){
                                                            celement.addAttribute("props","display:none");
                                                        }
                                                    }else if(value.length()<=10){
                                                        celement.addAttribute("props","display:none");
                                                    }

                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //获取图片信息的map
            for (Iterator i = section.elementIterator(); i.hasNext();) {
                Element el = (Element) i.next();
                if("section".equals(el.getName())){
                    for(Iterator j = el.elementIterator(); j.hasNext();){
                        Element el2 = (Element) j.next();
                        if("table".equals(el2.getName())){
                            //table上添加固定属性
                            el2.addAttribute("border", "0");
                            el2.addAttribute("cellpadding", "0");
                            if(null ==el2.attribute("props")){
                                el2.addAttribute("props","table-column-props:8.62cm/8.74cm/; table-row-heights:0.62cm/; table-column-leftpos:0.00cm; list-tag:1"+";width:900px;border-collapse:collapse;table-layout:fixed");
                            }else{
                                String props=el2.attribute("props").getValue();
                                el2.attribute("props").setValue(props+";width:900px;border-collapse:collapse;table-layout:fixed");
                            }
                            //table里的标签格式进行转换
                            String lastTopNum=null;
                            lastTopNum=tableParser(el2.elements(),lastTopNum,el2);
                        }
                    }
                }
                // System.out.println(el.asXML());
            }

            List<Element> abiword = root.element("abiword").elements();
            //根据type属性对应生成head,body,footer
            for (Element e:abiword){
                // System.out.println(e.getName());
                if("section".equals(e.getName())){

                    List<Element> ps=e.elements("p");
                    for (Element pelement:ps){
                        if(pelement.hasContent()){
                            List<Element> celementlist=pelement.elements("c");
                            for (Element celement:celementlist){
                                List<Attribute> listAttr=celement.attributes();
                                for(Attribute attr:listAttr){
                                    String name=attr.getName();//属性名称
                                    String value=attr.getValue();
                                    if("revision".equals(name) ){
                                        if(value.contains("-") ){
                                            if(value.length()>10){
                                                if(value.contains(",-") ){
                                                    celement.addAttribute("props"," display:none");
                                                }
                                            }else if(value.length()<=10){
                                                celement.addAttribute("props"," display:none");
                                            }

                                        }
                                    }
                                }
                            }
                        }
                        List<Attribute> listAttr=pelement.attributes();
                        for(Attribute attr:listAttr){
                            String name=attr.getName();//属性名称
                            String value=attr.getValue();

                        }
                    }
                    // System.out.println(e.asXML());
                    List<Attribute> listAttr=e.attributes();//当前节点的所有属性的list
                    for(Attribute attr:listAttr){//遍历当前节点的所有属性
                        String name=attr.getName();//属性名称
                        String value=attr.getValue();//属性的值
                        // System.out.println("属性名称："+name+"属性值："+value);
                        if("type".equals(name)&&"header".equals(value)){
                            // System.out.println(e.asXML());
                            headString= "<!DOCTYPE HTML PUBLIC '-//W3C//DTD HTML 4.01//EN' 'http://www.w3.org/TR/html4/strict.dtd'>\n"
                                    + "<html>\n"
                                    + "<head>\n"
                                    + "<meta http-equiv='Content-Type' content='text/html; charset=utf-8' />\n"
                                    +"<title>"+EmrParse.getFileName()+"</title>\n"
                                    +"<style type='text/css'>\n"
                                   +"body{\n"
                                    +"padding-top : 0px;\n"
                                    +"padding-bottom : 0px;\n"
                                    +"padding-left : 0px;\n"
                                    +"padding-right : 0px;\n"
                                    +"}\n"
                                    +classCssString
                                    +"</style>\n"
                                    +"<body version='V3.0'>"
                                    +"<body style =\"width:900px; height:500px; padding-right:0px; padding-left:0px; padding-bottom:0px; padding-top:0px; margin-left:0px; margin-top:0px\">\n" +
                                    "<script>\n";
                            if(null ==watermark || "".equals(watermark)){
                                headString=headString+"watermark({'watermark_txt':''});\n";
                            }else{
                                headString=headString+ "watermark({'watermark_txt':'"+watermark+"'});\n";
                            }
                            headString=headString+
                                    "function watermark(settings) {\n" +
                                    "    //默认设置\n" +
                                    "    var defaultSettings = {\n" +
                                    "        watermark_txt: \"\",\n" +
                                    "        watermark_x: 20, //水印起始位置x轴坐标\n" +
                                    "        watermark_y: 20, //水印起始位置Y轴坐标\n" +
                                    "        watermark_rows: 20, //水印行数\n" +
                                    "        watermark_cols: 20, //水印列数\n" +
                                    "        watermark_x_space: 100, //水印x轴间隔\n" +
                                    "        watermark_y_space: 50, //水印y轴间隔\n" +
                                    "        watermark_color: '#aaa', //水印字体颜色\n" +
                                    "        watermark_alpha: 0.2, //水印透明度\n" +
                                    "        watermark_fontsize: '15px', //水印字体大小\n" +
                                    "        watermark_font: '宋体', //水印字体\n" +
                                    "        watermark_width: 210, //水印宽度\n" +
                                    "        watermark_height: 80, //水印长度\n" +
                                    "        watermark_angle: 20 //水印倾斜度数\n" +
                                    "    };\n" +
                                    "    if (arguments.length === 1 && typeof arguments[0] === \"object\") {\n" +
                                    "        var src = arguments[0] || {};\n" +
                                    "        for (key in src) {\n" +
                                    "            if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) continue;\n" +
                                    "            else if (src[key]) defaultSettings[key] = src[key];\n" +
                                    "        }\n" +
                                    "    }\n" +
                                    "    var oTemp = document.createDocumentFragment();\n" +
                                    " //获取页面最大宽度\n" +
                                    "    var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);\n" +
                                    "    var cutWidth = page_width * 0.0150;\n" +
                                    "    var page_width = page_width - cutWidth;\n" +
                                    "    //获取页面最大高度\n" +
                                    "    var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight) + 450;\n" +
                                    "    page_height = Math.max(page_height, window.innerHeight - 30);\n" +
                                    "    //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔\n" +
                                    "    if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {\n" +
                                    "        defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));\n" +
                                    "        defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));\n" +
                                    "    }\n" +
                                    "    //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔\n" +
                                    "    if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {\n" +
                                    "        defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));\n" +
                                    "        defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));\n" +
                                    "    }\n" +
                                    "    var x;\n" +
                                    "    var y;\n" +
                                    "    for (var i = 0; i < defaultSettings.watermark_rows; i++) {\n" +
                                    "        y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;\n" +
                                    "        for (var j = 0; j < defaultSettings.watermark_cols; j++) {\n" +
                                    "            x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;\n" +
                                    "            var mask_div = document.createElement('div');\n" +
                                    "            mask_div.id = 'mask_div' + i + j;\n" +
                                    "            mask_div.className = 'mask_div';\n" +
                                    "            mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));\n" +
                                    "            //设置水印div倾斜显示\n" +
                                    "            mask_div.style.webkitTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
                                    "            mask_div.style.MozTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
                                    "            mask_div.style.msTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
                                    "            mask_div.style.OTransform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
                                    "            mask_div.style.transform = \"rotate(-\" + defaultSettings.watermark_angle + \"deg)\";\n" +
                                    "            mask_div.style.visibility = \"\";\n" +
                                    "            mask_div.style.position = \"absolute\";\n" +
                                    "            mask_div.style.left = x + 'px';\n" +
                                    "            mask_div.style.top = y + 'px';\n" +
                                    "            mask_div.style.overflow = \"hidden\";\n" +
                                    "            mask_div.style.zIndex = \"9999\";\n" +
                                    "            //让水印不遮挡页面的点击事件\n" +
                                    "            mask_div.style.pointerEvents = 'none';\n" +
                                    "            mask_div.style.opacity = defaultSettings.watermark_alpha;\n" +
                                    "            mask_div.style.fontSize = defaultSettings.watermark_fontsize;\n" +
                                    "            mask_div.style.fontFamily = defaultSettings.watermark_font;\n" +
                                    "            mask_div.style.color = defaultSettings.watermark_color;\n" +
                                    "            mask_div.style.textAlign = \"center\";\n" +
                                    "            mask_div.style.width = defaultSettings.watermark_width + 'px';\n" +
                                    "            mask_div.style.height = defaultSettings.watermark_height + 'px';\n" +
                                    "            mask_div.style.display = \"block\";\n" +
                                    "            oTemp.appendChild(mask_div);\n" +
                                    "        };\n" +
                                    "    };\n" +
                                    "    document.body.appendChild(oTemp);\n" +
                                    "}  </script>"
                                    + getNewXml(e.asXML(), root) +"\n</head>\n</body>";
                        }else if("type".equals(name)&&"footer".equals(value)){
                            footString="<footer>\n"+e.asXML()+"\n</footer>\n</body>\n</html>";
                        }else if (e.attribute("type")==null){
                            bodyString="<body>\n"+e.asXML()+"\n</body>\n";
                        }
                    }
                }
            }


        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return headString+bodyString+footString;
    }

    public static String getNewXml(String xml,Element root) {
        String newXml = "";
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new ByteArrayInputStream(xml.replace("&lt;","<").replace("&gt;",">").getBytes()));
            javax.xml.xpath.XPath xpath = XPathFactory.newInstance().newXPath();
            NodeList fields = (NodeList) xpath.evaluate("//field[@props]",doc, XPathConstants.NODESET);
            for (int i=0; i<fields.getLength(); i++) {
                org.w3c.dom.Element field = (org.w3c.dom.Element) fields.item(i);
                String propsValue = field.getAttribute("props");
                String[] split = propsValue.split(";");
                String cStr = "";
                for (int i1 = 0; i1 < split.length; i1++) {
                    if ("code".equals(split[i1].split(":")[0])) {
                        cStr = split[i1].split(":").length>=2 ? split[i1].split(":")[1].split("_").length>=3 ? "<c props=\"et:1; font-weight:bold; "+propsValue+"; font-size:12pt\">" + getValue(root.element("ScatterData").asXML(), split[i1].split(":")[1].split("_")[1], split[i1].split(":")[1].split("_")[2]) + "</c>" : "" : "";
                    }
                }
                org.w3c.dom.Text newContent = doc.createTextNode(cStr);
                field.appendChild(newContent);
            }
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            newXml = writer.toString();
            return newXml;
        } catch (Exception e) {
            e.printStackTrace();
            return newXml;
        }
    }

    public static String getValue(String xml,String code,String cCode) {
        String sStr = "";
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            org.w3c.dom.Document doc = factory.newDocumentBuilder()
                    .parse(new ByteArrayInputStream(xml.getBytes()));
            XPath xpath = XPathFactory.newInstance().newXPath();
            String xpathTemplate = "//MIString" +
                    "[Code/@Code='%s' and Code/@CCode='%s']" +
                    "/Value/text()";
            NodeList deptNodes = (NodeList) xpath.evaluate(String.format(xpathTemplate, cCode, code),doc, XPathConstants.NODESET);
            for(int i=0; i<deptNodes.getLength(); i++){
                sStr = deptNodes.item(i).getNodeValue();
            }
            return sStr;
        } catch (Exception e) {
            e.printStackTrace();
            return sStr;
        }
    }

    //获取图片
    public static Map<String, String> getImage(List<Element> datas){
        Map<String,String> imgMap=new HashMap<>();
        for(Element e:datas){
            String dataId=e.attribute("name").getValue();
            String dataText=e.getText();
            imgMap.put(dataId, dataText);
        }
        return imgMap;
        //List<Element> abiword = root.element("StructuredBody").element("NInstanceData").element("abiword").elements();
    }
    public static String tableParser(List<Element> tables,String lastTopNum,Element el2){
        if(tables!=null){
            Element tr=null;
            StringBuffer nodeStr=new StringBuffer();
            int index=0;
            for (Element e:tables){
                List<String> strList1=new ArrayList<>();
                List<String> strList2=new ArrayList<>();
                //把线条粗细的属性提到前边不然不会生效 ----strat
                String propss=e.attribute("props").getValue();
                String[] strs=propss.split(";");
                for(int i=0;i<strs.length;i++){
                    strList1.add(strs[i]);
                }
                for(Iterator<String> it = strList1.iterator();it.hasNext();){
                    String s1=it.next();
                    if(s1.contains("left-thickness")||s1.contains("right-thickness")||
                            s1.contains("top-thickness")||s1.contains("bot-thickness")){
                        strList2.add(s1);
                        it.remove();
                    }
                }
                strList2.addAll(strList1);
                String newProps=strList2.toString().replace("[", "").replace("]", "").replaceAll(",", ";");
                e.attribute("props").setValue(newProps);
                //把线条粗细的属性提到前边不然不会生效----end
                String props=e.attribute("props").getValue();
                String[] str1=props.split(";");
                String leftNum = null;
                String rightNum=null;
                String topNum=null;
                String botNum=null;
                for(int i=0;i<str1.length;i++){
                    if(str1[i].contains("left-thickness")||str1[i].contains("right-thickness")||
                            str1[i].contains("top-thickness")||str1[i].contains("bot-thickness")){
                    }
                    if(str1[i].contains("top-attach")){
                        topNum=str1[i].split(":")[1];
                        e.addAttribute("row",topNum );
                    }else if(str1[i].contains("left-attach")){
                        leftNum=str1[i].split(":")[1];
                        e.addAttribute("column", leftNum);
                    }else if(str1[i].contains("right-attach")){
                        /* rowspan="" colspan="6"*/
                        rightNum=str1[i].split(":")[1];
                    }else if(str1[i].contains("bot-attach")){
                        botNum=str1[i].split(":")[1];
                        /* rowspan="" colspan="6"*/
                    }
                    if(rightNum!=null&&leftNum!=null){
                        e.addAttribute("colspan",  (Integer.parseInt(rightNum)- Integer.parseInt(leftNum))+"");

                    }
                    if(topNum!=null&&botNum!=null){
                        e.addAttribute("rowspan",  (Integer.parseInt(botNum)- Integer.parseInt(topNum))+"");

                    }
                }
                if(topNum.equals(lastTopNum)&&tr!=null){
                    nodeStr.append(e.asXML()+"\n");
                    el2.remove(e);
                }else{
                    if(nodeStr.length()!=0){
                        tr.setText(nodeStr.toString());
                        nodeStr.setLength(0);
                        el2.remove(e);
                    }
                    tr=el2.addElement("tr");
                    if(nodeStr.length()==0){
                        nodeStr.append(e.asXML()+"\n");
                        el2.remove(e);
                    }
                    //System.out.println(e.asXML());

                }
                lastTopNum=topNum;
                //最后一次必须把内容设进去
                index++;
                if(index==tables.size()){
                    tr.setText(nodeStr.toString());

                }
            }
        }
        return lastTopNum;

    }
    @Override
    public void response(HttpServletResponse response, String target) throws IOException {
        log.info("开始返回html数据:{}", target);
        File htmlFile = new File(target);
        StopWatch cost = StopWatch.createStarted();
       /* while (true && cost.getTime(TimeUnit.SECONDS) < 60) {
            if (htmlFile.exists()) {
                log.info("html文件读取成功:{}", target);
                break;
            }
        }*/
        ServletOutputStream outputStream = null;
        InputStream decryptInputStream = new FileInputStream(htmlFile);
        try {
            response.reset();
            outputStream = response.getOutputStream();
            // 在http响应中输出流
            byte[] cache = new byte[1024];
            int nRead = 0;
            while ((nRead = decryptInputStream.read(cache)) != -1) {
                outputStream.write(cache, 0, nRead);
                outputStream.flush();
            }
            outputStream.flush();
        } catch (Exception e) {
            log.info("html文件返回异常:{}", target, e);
        } finally {
            decryptInputStream.close();
        }
    }

    @Override
    public void clean(String target) throws Exception {
        Assert.notEmpty(target, "指定删除的文件路径不能为空");
        File deleteFile = new File(target);
        if (deleteFile.exists()) {
            deleteFile.delete();
            if (!deleteFile.exists()) {
                log.info("已删除指定文件:{}", target);
            }
        }
    }

    @Override
    public Map<String, Object> getRegister() {
        return models;
    }

    public String generateHtmlName(String fileSnCode) {
        StopWatch stopWatch = StopWatch.createStarted();
        return DateTime.now().toDateStr() + stopWatch.getTime() + Math.random() + fileSnCode;
    }

    public byte[] filter(byte[] source, String fileSnCode) {
        if (source == null) {
            return source;
        }
        log.info("开始进行特殊过滤{}", fileSnCode);
        String str = new String(source, StandardCharsets.UTF_8);
        str = str.replace("&amp;KeyEnter;", "");
        str = str.replace("&amp;nbsp;", "");
        return str.getBytes();
    }
}
