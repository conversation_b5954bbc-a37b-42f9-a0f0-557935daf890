package com.jhids.emr.service;

import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Service
public class EmrService extends EmrParse {

    public String request(String fileSnCode, String watermark, String privacy, Map<String,Object> patientinfo/*, HttpServletResponse response*/) throws Exception {

        return process(fileSnCode,watermark,privacy, patientinfo);
    }
    public String requestSK(String fileSnCode, String watermark, String privacy, Map<String,Object> patientinfo,String EMR_CLASS_CODE/*, HttpServletResponse response*/) throws Exception {

        return processSK(fileSnCode,watermark,privacy, patientinfo,EMR_CLASS_CODE);
    }
}