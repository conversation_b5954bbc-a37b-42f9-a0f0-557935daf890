package com.jhids.emr.service;

import com.jhids.emr.mapper.JhPatInfoMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Blob;
import java.util.List;
import java.util.Map;

@Service
public class JhFileContentInfoServiceImpl implements JhFileContentInfoService{

    @Autowired
    private JhPatInfoMapper jhPatInfoMapper;
    @Override
    public Map<String, Object> getFileContent(String fileSnCode) {
        return jhPatInfoMapper.queryFileContent(fileSnCode);
    }

    @Override
    public List<Map<String, Object>> getFileContentSK(String fileSnCode) {
        return jhPatInfoMapper.queryFileContentSK(fileSnCode);
    }

    @Override
    public Map<String, Object> getFileContentSKModel(String EMR_CLASS_CODE) {
        return jhPatInfoMapper.getFileContentSKModel(EMR_CLASS_CODE);
    }

    @Override
    public void updateFileContent(Map<String,Object> b) {
        jhPatInfoMapper.updateFileContent(b);
    }

    @Override
    public Map<String, Object> getPatInfo(String fileSnCode) {
        return  jhPatInfoMapper.getPatInfo(fileSnCode);
    }

    @Override
    public Map<String, Object> getModel(String fileSnCode) {
        return  jhPatInfoMapper.getModel(fileSnCode);
    }

    @Override
    public Map<String, Object> getPatInfo2(String fileSnCode) {
        return  jhPatInfoMapper.getPatInfo2(fileSnCode);
    }

    @Override
    public void insert(Map<String, Object> b) {
        jhPatInfoMapper.insert(b);
    }

    @Override
    public Map<String, Object> getMrTopic(String fileSnCode) {
        return jhPatInfoMapper.getMrTopic(fileSnCode);
    }
}
