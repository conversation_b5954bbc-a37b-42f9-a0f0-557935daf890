package com.jhids.emr.service;

import org.apache.ibatis.annotations.Param;

import java.sql.Blob;
import java.util.List;
import java.util.Map;

public interface JhFileContentInfoService {

     Map<String,Object> getFileContent(String fileSnCode);
    List<Map<String, Object>> getFileContentSK(String fileSnCode);
    Map<String,Object> getFileContentSKModel(String fileSnCode);

     void updateFileContent(Map<String,Object>  b);

    Map<String, Object> getPatInfo(String fileSnCode);

    Map<String, Object> getModel(String fileSnCode);

    Map<String, Object> getPatInfo2(String fileSnCode);

    void insert(Map<String,Object>  b);

    Map<String, Object> getMrTopic(String fileSnCode);
}
