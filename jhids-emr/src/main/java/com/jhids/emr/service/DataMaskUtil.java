package com.jhids.emr.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DataMaskUtil {

    public static String toBeMasked(String html, Map<String, String> replaceRule) {
        for (Map.Entry<String, String> entry : replaceRule.entrySet()) {
            String beMasked = toBeMasked(entry.getKey(), entry.getValue());
            replaceRule.put(entry.getKey(), beMasked);
        }

        String record = getMaskedMedicalRecord(html, replaceRule);
        return record;
    }


    private static String toBeMasked(String target, String pattern) {
//        获取*号的索引
        List<Integer> index = new ArrayList<>();
        for (int i = 0; i < pattern.length(); i++) {
            if (pattern.charAt(i) == '*') {
                index.add(i);
            }
        }

        StringBuilder s = new StringBuilder(target);
        for (Integer i : index) {
            if (i < s.length()) {
                s.setCharAt(i, '*');
            }
        }
        return s.toString();

    }

    private static String getMaskedMedicalRecord(String html, Map<String, String> replacementMap) {
        for (Map.Entry<String, String> entry : replacementMap.entrySet()) {
            html = html.replaceAll(entry.getKey(), entry.getValue());

        }
        return html;
    }


}
