package com.jhids.emr.service;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.lang.Assert;
import com.jhids.emr.dto.JhFileContentDTO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public interface Emr extends DisPatcher {

    /**
     * 请求content数据
     * @param fileSnCode
     * @return
     * @throws Exception
     */
    JhFileContentDTO apply(String fileSnCode) throws Exception;

    JhFileContentDTO applySK(String fileSnCode,String EMR_CLASS_CODE) throws Exception;

    /**
     * 结合模板解析请求回来的数据，并生成html
     * @param source
     * @param watermark
     * @param fileSnCode
     * @return
     */
    Map<String,Object> parse(String source, String watermark, String fileSnCode,Map<String,Object> patinfoMap,Map<String,Object> patientinfo);

    /**
     * 将html数据返回前端
     * @param response
     * @param target
     * @throws IOException
     */
    void response(HttpServletResponse response, String target) throws IOException;

    /**
     * 将落地生成的html文件删除 如果不删除后面会堆积越来越大
     * @param target
     * @throws Exception
     */
    void clean(String target) throws Exception;
    public static boolean isBase64(String str) {
        String base64Pattern = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
        return Pattern.matches(base64Pattern, str);
    }
    /**
     * 执行主流程
     * @param fileSnCode
     * @throws Exception
     */
    default String process(String fileSnCode,String watermark,String privacy,Map<String,Object> patientinfo/*, HttpServletResponse response*/) throws Exception {
       /* response.setContentType("text/html;charset=utf-8");*/
        String target = null;
        String htmlUrl = null;
        String xmlUrl =null;
        String html="";
        JhFileContentDTO jhFileContentDTO = apply(fileSnCode);
        jhFileContentDTO.getPatinfoMap().put("privacyflag",privacy);
       if( isBase64(watermark)){
           try{
               byte [] orgnameBefore= Base64Decoder.decode(watermark);
               watermark = new String(orgnameBefore, "utf-8");
           }catch (Exception exception){

           }

       }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("<NInstanceData InstanceID=\"3059893||11\" HappenDateTime=\"2025-04-25 11:07\" DateTimeFormat=\"yyyy-MM-dd HH:mm\" TimeStamp=\"**********\">\n" +
                "<ScatterData>\n" +
                "<Component>\n" +
                "<Section>\n" +
                "<Code Code=\"Header\" DisplayName=\"页眉\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<Composite>\n" +
                "<Code Code=\"V001\" DisplayName=\"页眉\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0009\" SourceCode=\"L0009\"  CCode=\"V001\"  KBNodeID=\"\" DisplayName=\"医院名称\"/>\n" +
                "<Value>鞍钢集团公司总医院</Value>\n" +
                "</MIString>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0001\" SourceCode=\"L0001\"  CCode=\"V001\"  KBNodeID=\"\" DisplayName=\"姓名\"/>\n" +
                "<Value>鲁振喜</Value>\n" +
                "</MIString>\n" +
                "<MIMonoChoice>\n" +
                "<Code Code=\"O0001\" SourceCode=\"O0001\"  CCode=\"V001\"  KBNodeID=\"\" DisplayName=\"S\"/>\n" +
                "<Value><Choice Code=\"1\" DisplayName=\"男\"/></Value>\n" +
                "<Separator>、</Separator>\n" +
                "</MIMonoChoice>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "<Composite>\n" +
                "<Code Code=\"V003\" DisplayName=\"\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0010\" SourceCode=\"L0010\"  CCode=\"V003\"  KBNodeID=\"\" DisplayName=\"年龄\"/>\n" +
                "<Value>84岁</Value>\n" +
                "</MIString>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "<Composite>\n" +
                "<Code Code=\"V005\" DisplayName=\"\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0012\" SourceCode=\"L0012\"  CCode=\"V005\"  KBNodeID=\"\" DisplayName=\"病案号\"/>\n" +
                "<Value>A000000065133</Value>\n" +
                "</MIString>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "<Composite>\n" +
                "<Code Code=\"V007\" DisplayName=\"\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0015\" SourceCode=\"L0015\"  CCode=\"V007\"  KBNodeID=\"\" DisplayName=\"科室\"/>\n" +
                "<Value>重症医学科(ICU)</Value>\n" +
                "</MIString>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "</Section>\n" +
                "<Section>\n" +
                "<Code Code=\"Footer\" DisplayName=\"页脚\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "</Section>\n" +
                "<Section>\n" +
                "<Code Code=\"S002\" DisplayName=\"日常病程：\"/>\n" +
                "<Text>患者目前心率血压逐渐稳定，氧合良好，镇静镇痛药物逐渐减量，降低镇静镇痛深度，继续给予抗感染，化痰对症治疗。</Text>\n" +
                "<StyleText>患者目前心率血压逐渐稳定，氧合良好，镇静镇痛药物逐渐减量，降低镇静镇痛深度，继续给予抗感染，化痰对症治疗。</StyleText>\n" +
                "</Section>\n" +
                "<Section>\n" +
                "<Code Code=\"S003\" DisplayName=\"医师签名：\"/>\n" +
                "<Text>//陈汉敏/</Text>\n" +
                "<StyleText>//陈汉敏/</StyleText>\n" +
                "<Composite>\n" +
                "<Code Code=\"V008\" DisplayName=\"\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0016\" SourceCode=\"L0016\"  CCode=\"V008\"  KBNodeID=\"\" DisplayName=\"主任医师\"/>\n" +
                "<Value></Value>\n" +
                "</MIString>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "<Composite>\n" +
                "<Code Code=\"V009\" DisplayName=\"\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0017\" SourceCode=\"L0017\"  CCode=\"V009\"  KBNodeID=\"\" DisplayName=\"主治医师\"/>\n" +
                "<Value></Value>\n" +
                "</MIString>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "<Composite>\n" +
                "<Code Code=\"V010\" DisplayName=\"\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text>陈汉敏</Text>\n" +
                "<StyleText>陈汉敏</StyleText>\n" +
                "<SubItems>\n" +
                "<MIConcept>\n" +
                "<Code Code=\"V012\" DisplayName=\"\"/>\n" +
                "<Text>陈汉敏</Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIImage>\n" +
                "<Code Code=\"L0018\" SourceCode=\"\"  CCode=\"V012\"  KBNodeID=\"\" DisplayName=\"住院医师\"/>\n" +
                "<Value>陈汉敏</Value>\n" +
                "</MIImage>\n" +
                "</SubItems>\n" +
                "</MIConcept>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0018\"/>\n" +
                "<Value>陈汉敏</Value>\n" +
                "</MIString>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "<Composite>\n" +
                "<Code Code=\"V011\" DisplayName=\"\" KBNodeID=\"\" BindKBBaseID=\"\"/>\n" +
                "<Text></Text>\n" +
                "<StyleText></StyleText>\n" +
                "<SubItems>\n" +
                "<MIString>\n" +
                "<Code Code=\"L0019\" SourceCode=\"L0019\"  CCode=\"V011\"  KBNodeID=\"\" DisplayName=\"实习医师\"/>\n" +
                "<Value></Value>\n" +
                "</MIString>\n" +
                "</SubItems>\n" +
                "</Composite>\n" +
                "</Section>\n" +
                "</Component>\n" +
                "</ScatterData>");
        stringBuilder.append("<abiword template=\"false\" version=\"2.8.6\" xmlns:svg=\"http://www.w3.org/2000/svg\" xid-max=\"137\" xmlns:dc=\"http://purl.org/dc/elements/1.1/\" fileformat=\"1.1\" xmlns:fo=\"http://www.w3.org/1999/XSL/Format\" xmlns:math=\"http://www.w3.org/1998/Math/MathML\" xmlns:awml=\"http://www.abisource.com/awml.dtd\" xmlns=\"http://www.abisource.com/awml.dtd\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" xml:space=\"preserve\" props=\"table-row-heights:0.5cm/; dom-dir:ltr; bot-style:0; left-style:0; document-endnote-type:numeric; document-endnote-place-enddoc:1; document-footnote-restart-section:0; lang:zh-CN; table-column-props:14cm/; document-endnote-initial:1; document-endnote-restart-section:0; table-column-leftpos:0.200000cm; top-style:0; document-footnote-restart-page:0; document-footnote-type:numeric; right-style:0; document-footnote-initial:1; document-endnote-place-endsection:0\">\n" +
                "<revisions show=\"0\" mark=\"0\" show-level=\"268435455\" auto=\"0\">\n" +
                "<r id=\"1\" time-started=\"1745331729\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.30.21(林欣其)</r>\n" +
                "<r id=\"2\" time-started=\"1745333048\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.30.21(林欣其)</r>\n" +
                "<r id=\"3\" time-started=\"1745336438\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"4\" time-started=\"1745336947\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"5\" time-started=\"1745337014\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"6\" time-started=\"1745337060\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"7\" time-started=\"1745337193\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"8\" time-started=\"1745337452\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"9\" time-started=\"1745337468\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"10\" time-started=\"1745337472\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"11\" time-started=\"1745337856\" version=\"0\" revision-level=\"1\" revision-key=\"1157\">10.163.12.71(李吉涛)</r>\n" +
                "<r id=\"12\" time-started=\"1745338895\" version=\"0\" revision-level=\"1\" revision-key=\"1157\">10.163.12.71(李吉涛)</r>\n" +
                "<r id=\"13\" time-started=\"1745364628\" version=\"0\" revision-level=\"3\" revision-key=\"1160\">10.163.12.58(张殿红)</r>\n" +
                "<r id=\"14\" time-started=\"1745367339\" version=\"0\" revision-level=\"1\" revision-key=\"2399\">10.163.29.51(林欣其)</r>\n" +
                "<r id=\"15\" time-started=\"1745383744\" version=\"0\" revision-level=\"3\" revision-key=\"1123\">10.163.12.68(陈汉敏)</r>\n" +
                "<r id=\"16\" time-started=\"1745384708\" version=\"0\" revision-level=\"3\" revision-key=\"1123\">10.163.12.68(陈汉敏)</r>\n" +
                "<r id=\"17\" time-started=\"1745385004\" version=\"0\" revision-level=\"3\" revision-key=\"1156\">10.163.12.72(张浩强)</r>\n" +
                "<r id=\"18\" time-started=\"1745448418\" version=\"0\" revision-level=\"1\" revision-key=\"1842\">10.163.12.71(国东东)</r>\n" +
                "<r id=\"19\" time-started=\"1745455251\" version=\"0\" revision-level=\"3\" revision-key=\"1123\">10.163.12.62(陈汉敏)</r>\n" +
                "<r id=\"20\" time-started=\"1745455312\" version=\"0\" revision-level=\"3\" revision-key=\"1123\">10.163.12.62(陈汉敏)</r>\n" +
                "<r id=\"21\" time-started=\"1745457953\" version=\"0\" revision-level=\"3\" revision-key=\"1156\">10.163.12.163(张浩强)</r>\n" +
                "<r id=\"22\" time-started=\"1745550445\" version=\"0\" revision-level=\"3\" revision-key=\"1123\">10.163.12.72(陈汉敏)</r>\n" +
                "</revisions>\n" +
                "<pagesize pagetype=\"A4\" orientation=\"portrait\" width=\"210.000000\" height=\"297.000000\" units=\"mm\" page-scale=\"1.000000\"/>\n" +
                "<section footer=\"1\" header=\"0\" xid=\"638\" props=\"page-margin-footer:0.00cm; page-margin-header:1.50cm; page-margin-right:1.40cm; page-margin-left:2.40cm; page-margin-top:2.2021in; page-margin-bottom:1.50cm\">\n" +
                "<p xid=\"139\" props=\"text-align:left\"><bookmark usage=\"3\" type=\"start\" name=\"3059893||11^EMR100002WT\" xid=\"132\"/><c props=\"bgcolor:e0ecff; font-size:12pt; et:5; code:3059893||11^GuideDate; font-family:宋体; font-weight:bold\">2025-04-25 11:07</c><c props=\"font-weight:bold; font-family:宋体; font-size:12pt\">    </c><c props=\"bgcolor:ffffff; font-size:12pt; et:1; code:3059893||11^GuideTitle; font-family:宋体; font-weight:bold\">日常病程记录</c><bookmark usage=\"3\" type=\"end\" name=\"3059893||11^EMR100002WT\" xid=\"133\"/></p>\n" +
                "<p xid=\"131\" props=\"text-align:left\"><bookmark usage=\"1\" type=\"start\" name=\"3059893||11^S002\" xid=\"140\"/><c props=\"bgcolor:e0ecff; font-weight:bold; display:none; code:3059893||11^S002; et:9; font-size:12pt\">日常病程：</c><c props=\"display:inline; font-size:12pt\">   </c><c props=\"display:inline; font-size:12pt\">患者目前心率血压逐渐稳定，氧合良好，镇静镇痛药物逐渐减量，降低镇静镇痛深度，继续给予抗感染，化痰对症治疗。</c><bookmark usage=\"1\" type=\"end\" name=\"3059893||11^S002\" xid=\"141\"/></p>\n" +
                "<p xid=\"142\" props=\"text-align:right; margin-left:8.20cm\"><bookmark usage=\"1\" type=\"start\" name=\"3059893||11^S003\" xid=\"143\"/><c props=\"bgcolor:White; font-weight:bold; et:9; code:3059893||11^S003; display:inline; font-size:12pt\">医师签名：</c><c> </c><bookmark usage=\"2\" type=\"start\" name=\"3059893||11^S003_V008\" xid=\"144\"/><c props=\"bgcolor:c0c0c0; et:1; font-size:12pt; font-family:宋体; code:3059893||11^S003_V008_L0016\">[主任医师]</c><bookmark usage=\"2\" type=\"end\" name=\"3059893||11^S003_V008\" xid=\"145\"/><c>/</c><bookmark usage=\"2\" type=\"start\" name=\"3059893||11^S003_V009\" xid=\"146\"/><c props=\"bgcolor:c0c0c0; et:1; code:3059893||11^S003_V009_L0017\">[主治医师]</c><bookmark usage=\"2\" type=\"end\" name=\"3059893||11^S003_V009\" xid=\"147\"/><c>/</c><bookmark usage=\"2\" type=\"start\" name=\"3059893||11^S003_V010\" xid=\"148\"/><bookmark usage=\"4\" type=\"start\" name=\"3059893||11^S003_V010_V012\" xid=\"134\"/><image dataid=\"8a686922-2182-11f0-8d7c-d7691827ff75\" title=\"陈汉敏\" xid=\"137\" props=\"height:0.2000in; et:7; code:3059893||11^S003_V010_V012_L0018; width:0.4576in\"/><bookmark usage=\"4\" type=\"end\" name=\"3059893||11^S003_V010_V012\" xid=\"135\"/><bookmark usage=\"2\" type=\"end\" name=\"3059893||11^S003_V010\" xid=\"149\"/><c>/</c><bookmark usage=\"2\" type=\"start\" name=\"3059893||11^S003_V011\" xid=\"150\"/><c props=\"bgcolor:c0c0c0; et:1; code:3059893||11^S003_V011_L0019\">[实习医师]</c><bookmark usage=\"2\" type=\"end\" name=\"3059893||11^S003_V011\" xid=\"151\"/><bookmark usage=\"1\" type=\"end\" name=\"3059893||11^S003\" xid=\"152\"/></p>\n" +
                "</section>\n" +
                "<section id=\"1\" listid=\"0\" parentid=\"0\" type=\"footer\" xid=\"49\">\n" +
                "<p xid=\"50\" props=\"text-align:center; line-height:1pt\"><bookmark usage=\"1\" type=\"start\" name=\"Footer\" xid=\"53\"/></p>\n" +
                "<table xid=\"57\" props=\"list-tag:1\">\n" +
                "<cell xid=\"58\" props=\"bg-style:1; left-color:000000; bot-style:0; bot-thickness:1.0pt; right-attach:1; left-thickness:1.0pt; right-thickness:1.0pt; right-color:000000; bot-color:000000; left-attach:0; top-color:000000; top-style:1; right-style:0; left-style:0; top-thickness:1.0pt; bot-attach:1; top-attach:0\">\n" +
                "<p style=\"Normal\" xid=\"59\" props=\"text-align:center; line-height:8pt\"></p>\n" +
                "<p style=\"Normal\" xid=\"568\" props=\"text-align:center\"><c>第 </c><field type=\"page_number\" xid=\"65\"></field><c> 页</c></p>\n" +
                "</cell>\n" +
                "</table>\n" +
                "<p xid=\"56\" props=\"text-align:center; line-height:1pt\"><bookmark usage=\"1\" type=\"end\" name=\"Footer\" xid=\"54\"/></p>\n" +
                "</section>\n" +
                "<section id=\"0\" listid=\"0\" parentid=\"0\" type=\"header\" xid=\"1\">\n" +
                "<p xid=\"2\" props=\"text-align:left; line-height:2pt\"><bookmark usage=\"1\" type=\"start\" name=\"Header\" xid=\"5\"/><bookmark usage=\"2\" type=\"start\" name=\"Header_V001\" xid=\"67\"/><c> </c></p>\n" +
                "<table xid=\"8\" props=\"table-row-heights:0.87cm/1.00cm/0.75cm/; list-tag:1; bg-style:1; left-color:000000; bot-style:0; table-column-props:3.87cm/1.87cm/2.50cm/4.50cm/4.37cm/; right-color:000000; top-color:000000; top-style:0; bot-color:000000; right-style:0; left-style:0\">\n" +
                "<cell xid=\"9\" props=\"bot-attach:1; left-attach:0; right-attach:5; top-attach:0\">\n" +
                "<p style=\"Normal\" xid=\"10\" props=\"text-align:center\"><c props=\"font-size:16pt; et:1; code:Header_V001_L0009; font-weight:bold\">鞍钢集团公司总医院</c></p>\n" +
                "</cell>\n" +
                "<cell xid=\"18\" props=\"bot-attach:2; left-attach:0; right-attach:5; top-attach:1\">\n" +
                "<p style=\"Normal\" xid=\"19\" props=\"text-align:center; tabstops:0.00cm/L0,17.48cm/L0; margin-right:-0.12cm\"><c props=\"font-weight:bold; font-size:16pt\">病 程</c><c props=\"font-size:16pt\"> </c><c props=\"font-weight:bold; font-size:16pt\">记</c><c props=\"font-size:16pt\"> </c><c props=\"font-weight:bold; font-size:16pt\">录</c></p>\n" +
                "</cell>\n" +
                "<cell xid=\"27\" props=\"bg-style:1; bot-style:1; bot-thickness:1.0pt; right-attach:1; left-thickness:1.0pt; right-thickness:1.0pt; top-color:000000; bot-color:000000; left-attach:0; top-style:0; right-style:0; left-style:0; top-thickness:1.0pt; bot-attach:3; top-attach:2\">\n" +
                "<p style=\"Normal\" xid=\"28\" props=\"line-height:1.5\"><c props=\"font-weight:bold; font-size:12pt\">姓名:</c><c props=\"font-weight:normal; et:1; code:Header_V001_L0001; font-size:12pt\">鲁振喜</c></p>\n" +
                "</cell>\n" +
                "<cell xid=\"30\" props=\"bg-style:1; bot-style:1; bot-thickness:1.0pt; right-attach:2; left-thickness:1.0pt; right-thickness:1.0pt; top-color:000000; bot-color:000000; left-attach:1; top-style:0; right-style:0; left-style:0; top-thickness:1.0pt; bot-attach:3; top-attach:2\">\n" +
                "<p style=\"Normal\" xid=\"31\" props=\"tabstops:6.12cm/L0,1.37cm/L0,14.74cm/L0; margin-right:0.00cm\"><c props=\"font-weight:bold; font-size:12pt\">性别:</c><c props=\"et:3; code:Header_V001_O0001; font-size:12pt\">男</c></p>\n" +
                "</cell>\n" +
                "<cell xid=\"33\" props=\"bg-style:1; bot-style:1; bot-thickness:1.0pt; right-attach:3; left-thickness:1.0pt; right-thickness:1.0pt; top-color:000000; bot-color:000000; left-attach:2; top-style:0; right-style:0; left-style:0; top-thickness:1.0pt; bot-attach:3; top-attach:2\">\n" +
                "<p style=\"Normal\" xid=\"34\" props=\"text-indent:0.00cm; tabstops:12.11cm/L0,11.24cm/L0,11.36cm/L0,11.61cm/L0; margin-left:0.00cm\"><bookmark usage=\"2\" type=\"end\" name=\"Header_V001\" xid=\"68\"/><c props=\"font-weight:bold; font-size:12pt\">年龄</c><bookmark usage=\"2\" type=\"start\" name=\"Header_V003\" xid=\"110\"/><c props=\"font-weight:bold; font-size:12pt\">:</c><c props=\"et:1; code:Header_V003_L0010; font-size:12pt\">84岁</c><bookmark usage=\"2\" type=\"end\" name=\"Header_V003\" xid=\"111\"/></p>\n" +
                "</cell>\n" +
                "<cell xid=\"107\" props=\"bg-style:1; left-color:000000; bot-style:1; bot-thickness:1.0pt; right-attach:4; right-color:000000; top-color:000000; top-style:0; left-attach:3; left-style:0; right-style:0; bot-color:000000; bot-attach:3; top-attach:2\">\n" +
                "<p style=\"Normal\" xid=\"108\" props=\"tabstops:14.74cm/L0,1.37cm/L0,6.74cm/L0; margin-right:0.00cm\"><c props=\"font-weight:bold; font-size:12pt\">科室：</c><bookmark usage=\"2\" type=\"start\" name=\"Header_V007\" xid=\"119\"/><field type=\"dynamic_field\" param=\"当前科室\" xid=\"66\" props=\"code:Header_V007_L0015; et:1\"></field><bookmark usage=\"2\" type=\"end\" name=\"Header_V007\" xid=\"120\"/></p>\n" +
                "</cell>\n" +
                "<cell xid=\"98\" props=\"left-attach:4; right-attach:5; bot-style:1; bot-attach:3; top-attach:2; right-style:0; top-style:0; bg-style:1; left-style:0; bot-thickness:1.0pt\">\n" +
                "<p style=\"Normal\" xid=\"99\" props=\"tabstops:14.74cm/L0,1.37cm/L0,6.74cm/L0; margin-right:0.00cm\"><c props=\"font-weight:bold; font-size:12pt\">病案号</c><bookmark usage=\"2\" type=\"start\" name=\"Header_V005\" xid=\"114\"/><c props=\"font-weight:bold; font-size:12pt\">:</c><c props=\"et:1; code:Header_V005_L0012; font-size:12pt\">A000000065133</c><bookmark usage=\"2\" type=\"end\" name=\"Header_V005\" xid=\"115\"/></p>\n" +
                "</cell>\n" +
                "</table>\n" +
                "<p xid=\"7\" props=\"text-align:left; line-height:1pt\"><bookmark usage=\"1\" type=\"end\" name=\"Header\" xid=\"6\"/></p>\n" +
                "</section>\n");
        stringBuilder.append("<data>\n" +
                "<d name=\"8a686922-2182-11f0-8d7c-d7691827ff75\" mime-type=\"image/png\" base64=\"yes\">\n" +
                "iVBORw0KGgoAAAANSUhEUgAAAH4AAAA3CAMAAAD9qg4eAAAAAXNSR0IArs4c6QAAAARnQU1B\n" +
                "AACxjwv8YQUAAAMAUExURaytsCIjJVpbXnFydC4wNfb29tzd3zw9QdXV1jQ1OdnZ2hkaHZmZ\n" +
                "ncLCxLW2ubm6vT5ASDk7PsbGyM7P0ISGikVGS72+wMjJyvLy8/T09U5QU+Hi4oiJjaWmqKGh\n" +
                "pY2OkklKTQcIClBRVdra3H19gg0OEJaXmhwiNBcYG0JDRmZobHV2eaaoqfDw8aysruzs7Skq\n" +
                "LXp9gr/AwqmqrWhqbdbX2RESFSsySerr7A4PFYWIicDBw7a4vGxtcHl6fczNzxoeK4GChebm\n" +
                "6MrKzNDR0hwdIeLi5B4gI2Vmah0hKl5gZBUWGby9vkdISyYoLCUlM6Cio9LT1ZmanxUYIpGS\n" +
                "lXp7eqmqqqOkpTEzNygqRC0uMLGxs8vMzri5uurq6yUmKJeZne7u77GytVRVWejo6VVWXZOU\n" +
                "msTFx5+go4uNkGpuc6qtrVhYWm1vdBUVFsfIypaWnIKEh+Tk5WJlapqcobu8vk5OUc7Q0nd5\n" +
                "fpucn4qMjVtdYBkZIdPU1u7v8E5OVhUWHjs9T1JUWTY4O7S1ta6vsxodIN3e3zA0QhMUGqWl\n" +
                "pujp6n19f+Xl5sPDxR0eJRMVIyAiJ2FiZVlaYEtNUG9wc+3u7+vs7VBRWbu9woSFht/g4ZCR\n" +
                "lNfY2V5eYZOUliQpMykqMYyMj2Fja2hoaqKlqCMkKaKjqJ6eo36AhBIUHSYqOoyPlMPEyDY9\n" +
                "RhARE3+AgbO0tnt+fw8RGCAlLRkcJyotNYmKjyEhLhMTFhEOIykrNCYnLklLTystMC4uNBAR\n" +
                "HP39/f7+/vr6+vn5+fv7+/j4+Pv7/PX19vz8/PPz9Pn5+vr6+/z8/fz9/e3t7enq6/P09OPk\n" +
                "5fX29tXW1/Hy8ufn6Pf39/Hx8Y2Rk0xHZEpPXiAnPCklPCktPGxtb0BAW+zr7DY+S3V3fx8e\n" +
                "LDE4UZSVmOfo6ZKTmdvb3RgaJO/w8ImKjGpqcGpqdFNZYbGyuLO2uX1+hV1fZBQTGnFyeXJ0\n" +
                "ePj4+fv8/FpaZFpeZGpsa2trbw8WIRcbKP///9Xj12cAAAEAdFJOU///////////////////\n" +
                "////////////////////////////////////////////////////////////////////////\n" +
                "////////////////////////////////////////////////////////////////////////\n" +
                "////////////////////////////////////////////////////////////////////////\n" +
                "////////////////////////////////////////////////////////////////////////\n" +
                "/////////////////////////////////wBT9wclAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAJ\n" +
                "JElEQVRYR72YfXxMVxrHM4SIS5KJTPNCJENPcpJoRMiV2xJRxjSuRCVFpNqpEGUro0q8NKGR\n" +
                "WcOUiqlQFMuWtFJ6aWKZIuSFZtHaqsXudrfbrrUbre2+dO1Ld3v2nHPPfZnJ6OdjNfn+cc/v\n" +
                "OXfm/u6c1+dMAOo02i3mtOHRLLgLnWiPIo3HwYKfssA/nWmPekpiUSvTlGVTmFDoYD/4zbjN\n" +
                "p3bvYtH9kV8vPsck5VkAVjLJ0Nuvmjmbs3huVZ45I21kVffFLEvBxGamCdF8NreWaYZi36Ns\n" +
                "/lJOkrjZW16avEmYzmoRConoy9S9s9YsvMYk5Xmh/nEmFaj9R9OSrCKw54Yu+wJdNVQEPkJv\n" +
                "ofPpyXkuWHhOju6dMhCGr9eeMC5uI2EvkB38IRE6sH2vw05RGvTllfdJfJmrHi2PlhdS7BLk\n" +
                "Vru76dvvnkiqy0cPDbBUbjJn4qjVIQCfpsf2L+fy7vjpvVh4Pqw6i4qrn4pi2ryefT18OI3/\n" +
                "H2zC9lHVwtu//7aGRA/WbUqi1XoCzFbLj2TZPDD5ckn181SH2znHvBaEsiwbaOzNpZB2prw5\n" +
                "lzqNNrPMWWuYqS6xdtvbgPyC4ZaR8C35ho4AU0oqa90pUlqI5zRRTd15Po6I/mYDfocO9PGA\n" +
                "8oVM67hZJAC57SiTPEEpwtez5tUFfoTQBJv7V+aL7I5GgNazfSzHLsI8HGfmGgwzaVWMJ5WW\n" +
                "Pnz2H4sIipQOY0Tt/vrG8TOlLMIc5rqhZemo9GgKDt4NKoZQrtejm/cpYjEaW52AXims3dKD\n" +
                "1oSLh2nph2kOaPNqgPGGG7XGTYJumpZau5OiylWM0FNFM9vNr9JqL3T2j4FdKNoGhy5xj5Yr\n" +
                "PjRyXkumF00DK2hHMXrBglNRA4HtAosxpspp+PpPGx7vzd3GohWVsXK9Hp29icOXccCUsYSN\n" +
                "n5sWOgDuwnloYwrT3wofxIP7zCAWYzJvAdyGa0ty4HoUkPU+/nVyh3qh2V8KLCTFAvVTg0F8\n" +
                "Eyn9DT7ME9xTTOE1s5B7ERfb6vvIMSEDWPAzLN8mWpqjF+GZkgR8BgtBs4+0zybFVSfsTePW\n" +
                "cr4fLnqboK6FNM4bdFOy3DkEXx8F5qFyTNhe/wbKDMyeMeMkWr8MxyZAHueD9ugyvoSWa1yF\n" +
                "D5FyunUYvhbzDfZxJPShaQgdVzKPW8hvRxlmSFdOmeXCIfS37KM3ZhgvPEri0fwaWu+FZr9R\n" +
                "mohWkBGfK7+mtXYWQjs92ZAMIF9SgxZqgyzKFkPLWNdYWso01ra+VXkrYu7kE3+kAzgSOGi9\n" +
                "F5r9OM9OVHUTt6qb2sda5qJVMfBEjs3P6J/kHMAU4VievJYXetheRXjdatrOF5Q0r4THD9CK\n" +
                "cyahYy9qNeFiH7QIbky35SURw9yTpccke3ywu5t824sa83WmMLscobIQ4RxZEOJy1onZtVvR\n" +
                "Qt4VItf0k1yjZKWh2Ydysahlt12S4i+TsI941BWcjnZUPEzverOojqxkMnMKfxM2hiopiBYy\n" +
                "pQUjP6nHTw8VrMoumy4Ji5hU0OzL4CF8/WFC+gdy3PdgAE4TE+C7cuhFVB58hUmU0d0h8I9h\n" +
                "sRCQK2OrLTFxpITn8IgCbZ+b6a5IYJKh2edz45nSE8L7WSpxtTSfKYQHe3hjw+aPUQqvW3HH\n" +
                "FjQkrwMZONtMpPNZZmge9F76NPs/Qd80lPCcK9jvsmO1siTo52RpOn8qMSazRpJrCOOEgkFI\n" +
                "5CdcDB75+b/xNGXV6AhIY0pGs99m6cmUjn7WPYXqd/VM5+VvZl6iRVtYNv+OLps4UMn/DIlG\n" +
                "VH6i8PPFqOlV2x2aO7cnVXuPZNW+Oah2ApMa0YGfjISH8PzvwCVoJEVIKnu5poOVQr4sCWWC\n" +
                "pQk5HQMq546q+AHe8E7XghHjMgcAKYjkXRo6+8nqaFJJaXCvXh1mTZvEYh01Ir70DtbuWP9L\n" +
                "cyqZuBxuOOLWVTh7hMKJOF5x8sYtACrcxbplkaDaNwWf9n4xTCaEfy7JmftXSdQlMYxkEIlm\n" +
                "GbVZ+Zo1USQLP+WDYFuZ4/VyIO1Hazi8ds/JWro5SRCOxvtaqPYX9gbRpV5PQkVV0+/wRnUl\n" +
                "0JrUn9UplHP70WGHnJYQhu0xWvFIl2nnq1BEzLlUvHz+xUm2DkL4KXDUnOJtotr/o9YUxaRC\n" +
                "Wx53Ff9K8szRdo/PcaNGit5o1w5DbRxES/hG9vBYYQxqC6L7UH6DYo/QmIkeyI94Uh6tFNW+\n" +
                "zfLSNSYVdhhwDjxCuEJ0qMgf9pqBxrx+ad9ok+Jh53LUUnUyV45u8/g9dtmeRs1oiFVdITD9\n" +
                "vxSBBxqHtbMUU7W/Lv7rLJOM69Z4PLcP4BSM0LORExNeoJIQKX0FaXois9/p+BgXg3JGk4/0\n" +
                "sIWRpPYbA+7qJe5fkA+oXJs0WxQKzFx5xDPD9y9X7bd63vA5zuywknTmGaVDW++YPfYs5cQV\n" +
                "y7/D0ocoPGGKYdpVqm11wXg4TDDLWVoRl4mWN+CJ50103/lhALgq611m1X4V/DtTjKFpRtK2\n" +
                "w4B6pOgdDyTuZbmNgnNccfLrnt1ng2ISWzOmmclLRbjJHJ6yxv2eGZ787XGLn3xlTuS8nXEv\n" +
                "Rqr2g6uPMcXIgg+QYjHQLYbTLXYpniSiG+0FBqXjV06dms4kallgD0fXisrR9YPxZgAk8N6M\n" +
                "X/4aHGF3O6LaD9+rW7Qw/Th5c7wD6P7L+GwDNJB8LsYtsU3eh9a1Z9EDsNttwIGaSb0nPGmr\n" +
                "PG4Y7NOrOlT7YYFLmKL0MMbLYjOgvarSQqbneOD8VA79keLcIxayg2PLhkbtz4KOqPa3A+cy\n" +
                "RZnaSBOo/o/k8utphRdbnLoFpwP7QJ4uq/SZT96o9qU/0Tf+VMeztExProK65FnhyGx/uYHC\n" +
                "0+O/01KPaj/ihJYIfLEviR0JQmxfNeLzaaeh2k/cO5CptT9euk/5T2aFuM54l1PO94JqXxKo\n" +
                "yPUB2lIeW99g+u4/Bu8P1T7GwfJhL8aAerpxdBaqvcnoL9XD2+YfmOoUFPuoRgdLsLsUxT4D\n" +
                "6s8IXYZi3xeamOpSFPsA6ymmuhTFPt/NEpWuRbFP55KZ6lIU+xCLPiXrMhT7Nz3fy1/494pi\n" +
                "H2HueMTqAhT7zWY/23png9D/AFQqSN7wfEjTAAAAAElFTkSuQmCC\n" +
                "</d>\n" +
                "</data>\n" +
                "</abiword></NInstanceData>");
        jhFileContentDTO.setFILE_CONTENT(stringBuilder.toString());

        //将xml转化成html并放在指定路径下
        Map<String,Object> urlResult=parse(jhFileContentDTO.getFILE_CONTENT(), watermark, fileSnCode,jhFileContentDTO.getPatinfoMap(),patientinfo);
        try {

            if(null ==urlResult.get("returnDesc")){
                htmlUrl = (String)urlResult.get("htmlUrl");
                xmlUrl = (String)urlResult.get("xmlUrl");
                html=(String)urlResult.get("html");
                return html;
                /*response(response, htmlUrl);*/
            }else{
                ServletOutputStream outputStream = null;
                //在本地时用这个相对路径
               /* InputStream errPream = new FileInputStream("src/main/resources/html/twoError.html");*/
                //部署到服务器时用这个相对路径
                InputStream errPream = this.getClass().getResourceAsStream("/html/twoError.html");
                try {
                    /*response.reset();
                    outputStream = response.getOutputStream();*/
                    // 在http响应中输出流
                    byte[] cache = new byte[1024];
                    int nRead = 0;
                    while ((nRead = errPream.read(cache)) != -1) {
                        outputStream.write(cache, 0, nRead);
                        outputStream.flush();
                    }
                    outputStream.flush();
                } catch (Exception e) {

                } finally {
                    errPream.close();
                }
            }
        } finally {
            if(null ==urlResult.get("returnDesc")){
                clean(htmlUrl);
                clean(xmlUrl);
            }
        }
        return html;
    }

    /**
     * 模板分发
     * @param identifier
     * @return
     */
    @Override
    default Object dispatch(String identifier) {
        Assert.notEmpty(identifier, "模型匹配识别id不能为空");
        Assert.notEmpty(getRegister(), "模型组未初始化");
        Object model = getRegister().get(identifier);
        return model;
    }
    /*社康病历解析方法*/
    default String processSK(String fileSnCode,String watermark,String privacy,Map<String,Object> patientinfo,String EMR_CLASS_CODE/*, HttpServletResponse response*/) throws Exception {
        String html=null;
        JhFileContentDTO jhFileContentDTO = applySK(fileSnCode,EMR_CLASS_CODE);
        html=jhFileContentDTO.getFILE_CONTENT();
        return html;
    }
}
