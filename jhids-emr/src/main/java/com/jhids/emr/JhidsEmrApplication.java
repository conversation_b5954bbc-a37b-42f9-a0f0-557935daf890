package com.jhids.emr;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
@MapperScan(value = "com.jhids.emr.mapper")
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
public class JhidsEmrApplication {

    public static void main(String[] args) {
        SpringApplication.run(JhidsEmrApplication.class, args);
    }

}
