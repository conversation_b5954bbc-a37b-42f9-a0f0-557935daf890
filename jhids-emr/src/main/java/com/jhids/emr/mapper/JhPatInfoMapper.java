package com.jhids.emr.mapper;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-18
 */
@Repository
public interface JhPatInfoMapper  {

    Map<String, Object> queryFileContent(String fileSnCode);
    List<Map<String, Object>> queryFileContentSK(String fileSnCode);
    Map<String, Object> getFileContentSKModel(String fileSnCode);

    void updateFileContent(Map<String,Object> b);

    Map<String, Object> getPatInfo(String fileSnCode);

    Map<String, Object> getModel(String fileSnCode);

    Map<String, Object> getPatInfo2(String fileSnCode);

    void insert(Map<String, Object> b);

    Map<String, Object> getMrTopic(String fileSnCode);
}
