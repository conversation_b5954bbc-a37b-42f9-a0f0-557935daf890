<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="web" name="Web">
      <configuration>
        <webroots />
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: com.jhdl:jhdl-common:0.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hbase:hbase-shaded-client:2.2.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.stephenc.findbugs:findbugs-annotations:1.3.9-1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.htrace:htrace-core4:4.2.0-incubating" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.2" level="project" />
    <orderEntry type="library" name="Maven: log4j:log4j:1.2.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.yetus:audience-annotations:0.5.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:3.4.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:2.1.1.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-alibaba-nacos-discovery:2.1.1.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-client:1.1.4" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-common:1.1.4" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.4" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-api:1.1.4" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.83" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.13" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.10.1" level="project" />
    <orderEntry type="library" name="Maven: io.prometheus:simpleclient:0.5.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.2.1.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-context:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-netflix-archaius:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: commons-configuration:commons-configuration:1.8" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.ribbon:ribbon-transport:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: io.reactivex:rxnetty-contexts:0.4.9" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: io.reactivex:rxnetty-servo:0.4.9" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: javax.inject:javax.inject:1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: io.reactivex:rxnetty:0.4.9" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon-core:2.3.0" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon-httpclient:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: commons-collections:commons-collections:3.2.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.jersey:jersey-client:1.19.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.jersey:jersey-core:1.19.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: javax.ws.rs:jsr311-api:1.1.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.jersey.contribs:jersey-apache-client4:1.19.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.servo:servo-core:0.12.21" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-commons-util:0.3.0" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.ribbon:ribbon-loadbalancer:2.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-statistics:0.1.1" level="project" />
    <orderEntry type="library" name="Maven: io.reactivex:rxjava:1.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.17.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.17.0" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.29" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.yaml:snakeyaml:1.25" level="project" />
    <orderEntry type="library" name="Maven: com.microsoft.sqlserver:sqljdbc4:4.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.5" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:8.0.18" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:3.6.1" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: org.projectlombok:lombok:1.18.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.3.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.10.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.10.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.10.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.29" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.29" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.29" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.jhdl:jhdl-common-api:1.0.0-SNAPSHOT" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-openfeign:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-rsa:1.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcpkix-jdk15on:1.60" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.60" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-openfeign-core:2.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form-spring:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.3.1" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-core:10.4.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-slf4j:10.4.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-hystrix:10.4.0" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.archaius:archaius-core:0.7.6" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.google.code.findbugs:jsr305:3.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.hystrix:hystrix-core:1.5.18" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.9" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-httpclient:10.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.10" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.12" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger2:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spi:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-core:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-schema:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-common:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-web:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:20.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mapstruct:mapstruct:1.2.0.Final" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-ui:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-models:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.10.1" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-bean-validators:2.9.2" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid-spring-boot-starter:1.1.22" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid:1.1.22" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.29" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-boot-starter:3.3.1" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus:3.3.1" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:dynamic-datasource-spring-boot-starter:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.5" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-generator:3.3.1.tmp" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-extension:3.3.1.tmp" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-core:3.3.1.tmp" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-annotation:3.3.1.tmp" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:3.1" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.freemarker:freemarker:2.3.30" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.2.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-test:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-test:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.jayway.jsonpath:json-path:2.4.0" level="project" />
    <orderEntry type="library" name="Maven: net.minidev:json-smart:2.3" level="project" />
    <orderEntry type="library" name="Maven: net.minidev:accessors-smart:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.ow2.asm:asm:5.0.4" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:2.3.2" level="project" />
    <orderEntry type="library" name="Maven: jakarta.activation:jakarta.activation-api:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: org.junit.jupiter:junit-jupiter:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.junit.jupiter:junit-jupiter-api:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.opentest4j:opentest4j:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.junit.platform:junit-platform-commons:1.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.junit.jupiter:junit-jupiter-params:5.5.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.junit.jupiter:junit-jupiter-engine:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.junit.vintage:junit-vintage-engine:5.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apiguardian:apiguardian-api:1.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.junit.platform:junit-platform-engine:1.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.mockito:mockito-junit-jupiter:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.assertj:assertj-core:3.13.2" level="project" />
    <orderEntry type="library" name="Maven: org.hamcrest:hamcrest:2.1" level="project" />
    <orderEntry type="library" name="Maven: org.mockito:mockito-core:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy:1.10.4" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy-agent:1.10.4" level="project" />
    <orderEntry type="library" name="Maven: org.objenesis:objenesis:2.6" level="project" />
    <orderEntry type="library" name="Maven: org.skyscreamer:jsonassert:1.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-test:5.2.2.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.xmlunit:xmlunit-core:2.6.3" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okhttp3:okhttp:3.14.4" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okio:okio:1.17.2" level="project" />
    <orderEntry type="library" name="Maven: oracle.jdbc.driver:ojdbc:8" level="project" />
    <orderEntry type="library" name="Maven: cn.easyproject:orai18n:12.1.0.2.0" level="project" />
    <orderEntry type="library" name="Maven: dom4j:dom4j:1.6" level="project" />
    <orderEntry type="library" name="Maven: xml-apis:xml-apis:1.0.b2" level="project" />
    <orderEntry type="library" name="Maven: junit:junit:4.12" level="project" />
    <orderEntry type="library" name="Maven: org.hamcrest:hamcrest-core:2.1" level="project" />
    <orderEntry type="library" name="Maven: jaxen:jaxen:1.2.0" level="project" />
  </component>
</module>